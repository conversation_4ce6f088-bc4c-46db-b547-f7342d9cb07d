package com.tencent.andata.sql;

public class DwmImOnlineBackendDataSql {
    public static String DWM_IM_ONLINE_BACKEND_DATA_SQL = ""
            + "with tmp1 as (\n"
            + "    select\n"
            + "        *,\n"
            + "        row_number() over(partition by conversation_id order by ftime desc) as rn\n"
            + "    from dwd_im_online_backend_data\n"
            + ")\n"
            + "select\n"
            + "     conversation_id,\n"
            + "     long_to_sql_timestamp(CAST((UNIX_TIMESTAMP()*1000) AS BIGINT)) AS dwm_create_time,\n"
            + "     record_update_time,\n"
            + "     conversation_ticket_ids,\n"
            + "     ticket_ids,\n"
            + "     customer_type,\n"
            + "     owner_uin,\n"
            + "     uin,\n"
            + "     uid,\n"
            + "     category_level1_name,\n"
            + "     category_level2_name,\n"
            + "     category_level3_name,\n"
            + "     customer_name,\n"
            + "     source,\n"
            + "     title,\n"
            + "     staff_title,\n"
            + "     status,\n"
            + "     create_time,\n"
            + "     first_should_assign_id,\n"
            + "     should_assign_id,\n"
            + "     fact_assign_id,\n"
            + "     service_scene,\n"
            + "     staffs as staffs_user_ids,\n"
            + "     solve_status,\n"
            + "     appraise,\n"
            + "     service_rate,\n"
            + "     unsatisfy_reason,\n"
            + "     conversation_service_rate,\n"
            + "     conversation_unsatisfy_reason,\n"
            + "     product_rate,\n"
            + "     product_unsatisfy_reason,\n"
            + "     recommend_score,\n"
            + "     parent,\n"
            + "     creator_type,\n"
            + "     finish_type,\n"
            + "     alarm_level,\n"
            + "     origin_status,\n"
            + "     current_staff,\n"
            + "     company_id,\n"
            + "     chat_type\n"
            + "from tmp1\n"
            + "where rn = 1";
}
