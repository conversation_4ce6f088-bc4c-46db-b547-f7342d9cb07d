package com.tencent.andata.sql;

public class DwdImOnlineBackendDataSql {
    public static String DWD_IM_ONLINE_BACKEND_DATA_SQL = ""
            + "    select\n"
            + "        long_to_sql_timestamp(cast((unix_timestamp()*1000) as bigint)) as `dwd_create_time`,\n"
            + "        ftime,\n"
            + "        value_of_primary_key,\n"
            + "        string_to_sql_timestamp(`record_update_time`) as record_update_time,\n"
            + "        requestid as request_id,\n"
            + "        rpc_name as operation,\n"
            + "        conversationid as conversation_id,\n"
            + "        owneruin as owner_uin,\n"
            + "        uin as uin,\n"
            + "        uid as uid,\n"
            + "        source as source,\n"
            + "        status as status,\n"
            + "        simple_string_trans(`categoryid`, 0) as category_id,\n"
            + "        simple_string_trans(`firstshouldassign`, 0) as first_should_assign_id,\n"
            + "        simple_string_trans(`shouldassign`, 0) as should_assign_id,\n"
            + "        simple_string_trans(`factassign`, 0) as fact_assign_id,\n"
            + "        simple_string_trans(`servicescene`, 0) as service_scene,\n"
            + "        currentstaff as current_staff,\n"
            + "        staffs as staffs,\n"
            + "        appraise as appraise,\n"
            + "        servicerate as service_rate,\n"
            + "        unsatisfyreason as unsatisfy_reason,\n"
            + "        long_to_sql_timestamp(cast(simple_string_trans(`appraisetime`, 0) as bigint) * 1000) "
            + "             as appraise_time,\n"
            + "        long_to_sql_timestamp(cast(simple_string_trans(`createtime`, 0) as bigint) * 1000) "
            + "             as create_time,\n"
            + "        long_to_sql_timestamp("
            + "             cast(simple_string_trans(`customerupdatedtime`, 0) as bigint) * 1000"
            + "         ) as customer_updated_time,\n"
            + "        long_to_sql_timestamp(cast(simple_string_trans(`staffupdatedtime`, 0) as bigint) * 1000) "
            + "             as staff_updated_time,\n"
            + "        ticketids as ticket_ids,\n"
            + "        conversationticketids as conversation_ticket_ids,\n"
            + "        title as title,\n"
            + "        alllevelcategory as all_level_category,\n"
            + "        long_to_sql_timestamp("
            + "             cast(simple_string_trans(`customerfirstupdatedtime`, 0) as bigint) * 1000"
            + "         ) as customer_first_updated_time,\n"
            + "        long_to_sql_timestamp("
            + "             cast(simple_string_trans(`stafffirstupdatedtime`, 0) as bigint) * 1000"
            + "         ) as staff_first_updated_time,\n"
            + "        case\n"
            + "             when isalarm = 'true' then 1\n"
            + "             else 0\n"
            + "        end as is_alarm,\n"
            + "        simple_string_trans(`solvestatus`, 0) as solve_status,\n"
            + "        customername as customer_name,\n"
            + "        simple_string_trans(`isclean`, 0) as is_clean,\n"
            + "        simple_string_trans(`customertype`, 0) as customer_type,\n"
            + "        long_to_sql_timestamp(cast(simple_string_trans(`finishtime`, 0) as bigint) * 1000) "
            + "             as finish_time,\n"
            + "        stafftitle as staff_title,\n"
            + "        get_json_object(`alllevelcategory`, '$.first_level.name') as category_level1_name,\n"
            + "        get_json_object(`alllevelcategory`, '$.second_level.name') as category_level2_name, \n"
            + "        get_json_object(`alllevelcategory`, '$.third_level.name') as category_level3_name,\n"
            + "        istransferred as is_transferred,\n"
            + "        chattype as chat_type,\n"
            + "        msgdata as msg_data,\n"
            + "        conversationservicerate as conversation_service_rate,\n"
            + "        isticketcreated as is_ticket_created,\n"
            + "        conversationunsatisfyreason as conversation_unsatisfy_reason,\n"
            + "        productrate as product_rate,\n"
            + "        productunsatisfyreason as product_unsatisfy_reason,\n"
            + "        companyid as company_id,\n"
            + "        recommendscore as recommend_score,\n"
            + "        post as post,\n"
            + "        parent as parent,\n"
            + "        simple_string_trans(`creatortype`, 0) as creator_type,\n"
            + "        finisher as finisher,\n"
            + "        simple_string_trans(`finishtype`, 0) as finish_type,\n"
            + "        long_to_sql_timestamp(cast(simple_string_trans(`applyfinishtime`, 0) as bigint) * 1000) "
            + "             as apply_finish_time,\n"
            + "        creator as creator,\n"
            + "        long_to_sql_timestamp("
            + "             cast(simple_string_trans(`awaitingsupplementtime`, 0) as bigint) * 1000"
            + "         ) as awaiting_supplement_time,\n"
            + "        contact as contact,\n"
            + "        alarmlevel as alarm_level,\n"
            + "        status as origin_status,\n"
            + "        msgseq as msg_seq,\n"
            + "        senderid as sender_id\n"
            + "    from f_ods_im_online_backend_data\n";

    public static String DIM_CUSTOMER_STAFF_INFO = ""
            + "select \n"
            + "    uid,\n"
            + "    user_id,\n"
            + "    user_name,\n"
            + "    company_id,\n"
            + "    group_id,\n"
            + "    assign_company_id\n"
            + "from iceberg_source_dim_customer_staff_info/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n";

    public static String DIM_INCIDENT_DUTY = ""
            + "select \n"
            + "    duty_id,\n"
            + "    duty_name\n"
            + "from iceberg_dim_duty /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/";
}
