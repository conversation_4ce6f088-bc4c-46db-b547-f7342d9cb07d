package com.tencent.andata;

import com.tencent.andata.dwd.DwdImOnlineBackendData;
import com.tencent.andata.dwd.InitApplication;
import com.tencent.andata.dwm.DwmPGImOnlineBackendData;
import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.common.RuntimeExecutionMode;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class Application {
    private static final Logger logger = LoggerFactory.getLogger(Application.class);

    /**
     * 在一个main方法中运行多个flink etl 任务
     *
     * @param args args[0] = iceberg db name, args[1] = pg db name
     */
    public static void main(String[] args) throws Exception {
        FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        final IcebergCatalogReader catalog = new IcebergCatalogReader();

        String parallelism = parameterTool.get("parallelism", "1");
        // table env config
        Configuration configuration = flinkEnv.streamTEnv().getConfig().getConfiguration();

        // 设置flink应用程序名称
        configuration.setString("pipeline.name", "Webim Application");

        // 状态保留3天
        // configuration.setString("table.exec.state.ttl", "259200000");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        configuration.setString("table.dynamic-table-options.enabled", "true");

        // 关闭iceberg source的自动推断并行度
        configuration.setString("table.exec.iceberg.infer-source-parallelism", "false");
        configuration.setString("table.exec.iceberg.infer-source-parallelism.max", parallelism);
        configuration.setString("table.exec.resource.default-parallelism", parallelism);

        // get iceberg db name and pg db name from args
        String pgDbName = parameterTool.get("pgDbName");
        String icebergDbName = parameterTool.get("icebergDbName");
        String startSnapshot = parameterTool.get("startSnapshot");
        String streamingStr = parameterTool.get("streaming");
        boolean streaming = Boolean.parseBoolean(streamingStr);

        // instantiate the DWD ETL
        List<Object> appList = new ArrayList<>();
        appList.add(InitApplication.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
        appList.add(
                DwdImOnlineBackendData
                        .builder()
                        .icebergDbName(icebergDbName)
                        .pgDbName(pgDbName)
                        .startSnapshot(Long.parseLong(startSnapshot))
                        .streaming(streaming)
                        .parallelism(parallelism)
                        .build()
        );
        appList.add(
                DwmPGImOnlineBackendData
                        .builder()
                        .icebergDbName(icebergDbName)
                        .pgDbName(pgDbName)
                        .parallelism(parallelism)
                        .build()
        );

        // 遍历appList，调用run方法
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", flinkEnv, catalog)));

        if (!streaming) {
            flinkEnv.env().setRuntimeMode(RuntimeExecutionMode.BATCH);
        }
        // execute the sql statements
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("Webim Application");
    }
}