package com.tencent.andata.dwm;

import com.tencent.andata.sql.DwmImOnlineBackendDataSql;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseEnum;
import lombok.Builder;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Properties;

@Builder
public class DwmPGImOnlineBackendData {

    private final String icebergDbName;
    private final String pgDbName;
    private final String parallelism;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");

        // 注册Iceberg表
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(
                                        catalog.getTableInstance(
                                                icebergDbName,
                                                "dwm_im_online_base_info"
                                        )
                                )
                                        .primaryKeyName(
                                                "conversation_id"
                                        )
                        )
                        .flinkTableName("iceberg_sink_dwm_im_base_info")
                        .build()
        );
        // Dwd数据 去重
        Table dwmImOnlineBackendDataTable = tEnv.sqlQuery(
                DwmImOnlineBackendDataSql.DWM_IM_ONLINE_BACKEND_DATA_SQL
        );
        // 注册计算后的DWD表
        tEnv.createTemporaryView(
                "dwm_in_online_backend_data",
                dwmImOnlineBackendDataTable
        );
        // 入库
        flinkEnv.stmtSet().addInsertSql(
                        TableUtils.insertIntoSql(
                                "dwm_in_online_backend_data",
                                "iceberg_sink_dwm_im_base_info",
                                tEnv.from("iceberg_sink_dwm_im_base_info"),
                                DatabaseEnum.ICEBERG
                        )
                );
    }
}
