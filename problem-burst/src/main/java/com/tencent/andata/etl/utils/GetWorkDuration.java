package com.tencent.andata.etl.utils;

import org.apache.flink.table.functions.ScalarFunction;
import com.tencent.andata.utils.WorkDurationUtils;

public class GetWorkDuration extends ScalarFunction {
    /**
     * 获取工作日时长
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param holidayDays 节假日list的string
     * @return 工作日时长
     */
    public double eval(String startTime,String endTime,String holidayDays) {
        return WorkDurationUtils.getWorkDuration(startTime, endTime, holidayDays);
    }
}
