package com.tencent.andata.etl.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;

public class GetProblemFirstTime extends ScalarFunction {

    /**
     * 获取首次时长
     * @param operationJson 操作流水 json
     * @param problemDeliveryMode 交付模式
     * @return 计算结果
     * @throws Exception 异常
     */
    public String eval(String operationJson, String problemDeliveryMode) throws Exception {
        if (StringUtils.isBlank(operationJson) || StringUtils.isBlank(problemDeliveryMode)) {
            return "";
        }
        String firstProcessingTime = "1970-01-01 00:00:00"; // 问题单首次进入处理中时间
        String firstApproveTime = "1970-01-01 00:00:00"; // 问题单首次进入改进中时间
        String firstConfirmCloseTime = "1970-01-01 00:00:00"; // 问题单首次进入待确认结单时间
        String closeTime = "1970-01-01 00:00:00"; // 问题单结单时间
        String firstDealTime = "1970-01-01 00:00:00"; // 问题单首次处理完成时间
        String firstImproveTime = "1970-01-01 00:00:00"; // 问题单首次进入改进中时间
        String firstInflowApprovingTime = "1970-01-01 00:00:00"; // 问题单首次进入审批中时间

        // LOG.info("GetProblemDealDurationAndFisrtTime:param:"+param+"operationJson:"+operationJson);

        GetOperationList opList = new GetOperationList();
        List<Map<String, Object>> operationList = opList.operationJsonToList(operationJson);
        for (Map<String, Object> map : operationList) {
            String operationType = map.get("operation_type").toString(); // 操作类型
            String operateTime = map.get("operate_time").toString(); // 操作时间

            if (operationType.equals("17") && firstProcessingTime.equals("1970-01-01 00:00:00")) {
                firstProcessingTime = operateTime; // 问题单首次进入处理中时间
            } else if (operationType.equals("46") && firstApproveTime.equals("1970-01-01 00:00:00")) {
                firstApproveTime = operateTime; // 问题单首次进入改进中时间
            } else if (operationType.equals("24")) {
                closeTime = operateTime; // 问题单结单时间
            } else if (operationType.equals("13") && firstConfirmCloseTime.equals("1970-01-01 00:00:00")) {
                firstConfirmCloseTime = operateTime; // 问题单首次进入待确认结单时间
            } else if (operationType.equals("20") && firstDealTime.equals("1970-01-01 00:00:00")) {
                firstDealTime = operateTime; // 问题单首次处理完成时间
            }
        }
        if (!firstApproveTime.equals("1970-01-01 00:00:00")) {
            firstImproveTime = firstApproveTime;
        } else if (!firstDealTime.equals("1970-01-01 00:00:00")
                && (firstDealTime.compareTo("2021-07-08 16:05:00") <= 0 || !problemDeliveryMode.equals("1"))) {
            firstImproveTime = firstDealTime;
        }

        if (!firstDealTime.equals("1970-01-01 00:00:00") && firstDealTime.compareTo("2021-07-08 16:05:00") > 0
                && problemDeliveryMode.equals("1")) {
            firstInflowApprovingTime = firstDealTime;
        }
        // 创建一个Map来存储返回值
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("first_processing_time", firstProcessingTime);
        resultMap.put("first_approve_time", firstApproveTime);
        resultMap.put("first_confirm_close_time", firstConfirmCloseTime);
        resultMap.put("close_time", closeTime);
        resultMap.put("first_deal_time", firstDealTime);
        resultMap.put("first_improve_time", firstImproveTime);
        resultMap.put("first_inflow_approving_time", firstInflowApprovingTime);
        // 创建ObjectMapper对象
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(resultMap);
    }
}