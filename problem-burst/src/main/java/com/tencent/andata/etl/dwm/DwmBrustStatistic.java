package com.tencent.andata.etl.dwm;

import com.tencent.andata.etl.sql.DwmProblemBrustStatisticSql;
import com.tencent.andata.etl.tablemap.DwmProblemBrustStatisticMapping;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableUtils;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;


@Builder
public class DwmBrustStatistic {

    private final String icebergDbName;


    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        ObjectMapper mapper = new ObjectMapper();

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(DwmProblemBrustStatisticMapping.icebergTable2FlinkTable,
                        ArrayNode.class), tEnv, catalog
        );

        // create temporary view for source table
        flinkEnv.streamTEnv().createTemporaryView("dwm_fault_view",
                flinkEnv.streamTEnv().sqlQuery(DwmProblemBrustStatisticSql.DwmFaultManagementStatisticSql));

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(
                insertIntoSql(
                        "dwm_fault_view",
                        "iceberg_sink_dwm_fault_management_statistic_rt",
                        tEnv.from("iceberg_sink_dwm_fault_management_statistic_rt"),
                        ICEBERG)
        );
    }
}
