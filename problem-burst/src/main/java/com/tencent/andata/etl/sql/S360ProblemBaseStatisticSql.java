package com.tencent.andata.etl.sql;

public class S360ProblemBaseStatisticSql {

    public static String S360ProblemBaseStatisticSql = ""
            + "SELECT\n"
            + "    t.record_update_time,\n"
            + "    problem_id as value_of_primary_key,\n"
            + "    problem_id,\n"
            + "    title,\n"
            + "    description,\n"
            + "    t.service_scene_display,\n"
            + "    t.service_scene_level1_name,\n"
            + "    t.service_scene_level2_name,\n"
            + "    t.service_scene_display3_name,\n"
            + "    reason,\n"
            + "    t.reason_category_name as reason_category,\n"
            + "    case when c.priority_name='L1' and t.status=3 then '故障总结中' else b.status_name end as status,\n"
            + "    creator,\n"
            + "    current_operator,\n"
            + "    manager,\n"
            + "    responsible,\n"
            + "    cast(create_time as timestamp) as create_time,\n"
            + "    related_ticket_id,\n"
            + "    related_ticket_id_amount,\n"
            + "    c.priority_name as priority,\n"
            + "    problem_delivery_mode,\n"
            + "    cast(close_time as timestamp) as close_time,\n"
            + "    case when measures_improve_plan_time is not null "
            + "    and CAST(measures_improve_plan_time as string) >'2000-01-01 00:00:00'\n"
            + "    and cast(measures_improve_plan_time+ INTERVAL '8' HOUR as date)<cast(now() as date) then 1 "
            + "    else 0 end as measures_is_overtime,\n"
            + "    case when measures_improve_plan_time is not null "
            + "    and CAST(measures_improve_plan_time as string) >'2000-01-01 00:00:00' "
            + "    then cast(measures_improve_plan_time+ INTERVAL '8' HOUR as timestamp) "
            + "    when measures_finished_plan_time is not null "
            + "    then cast(measures_finished_plan_time+ INTERVAL '8' HOUR as timestamp) "
            + "    else cast('1970-01-01 00:00:00' as timestamp) end as measures_plan_finish_time,\n"
            + "    case when status=6 then 1 else 0 end as is_close_flag,\n"
            + "    priority_score,\n"
            + "    case when status=4 then COALESCE(t.measure_day_num,0)*3600*24 "
            + "    else 0 end as priority_cost,\n"
            + "    is_fault,\n"
            + "    is_lone_problem,\n"
            + "    handle_duration,\n"
            + "    improve_duration,\n"
            + "    service_scene_id\n"
            + "FROM (select *,row_number() over(partition by problem_id order by record_update_time desc) as rn\n"
            + " from iceberg_source_dwm_problem_base_statistic_rt \n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ )as t\n"
            + "left join (select value_of_primary_key,status_name\n"
            +  "from iceberg_source_dim_problem_status /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n"
            + ") as b on t.status=b.value_of_primary_key\n"
            + "left join (select value_of_primary_key,priority_name\n"
            +  "from iceberg_source_dim_problem_priority /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n"
            + ") as c on t.priority=c.value_of_primary_key\n"
            + "where t.rn=1\n";
}
