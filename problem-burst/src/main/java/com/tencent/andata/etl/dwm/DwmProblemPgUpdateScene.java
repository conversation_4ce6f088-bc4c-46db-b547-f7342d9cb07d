package com.tencent.andata.etl.dwm;

import com.tencent.andata.etl.sql.DwmProblemPgUpdateSceneSql;
import com.tencent.andata.etl.tablemap.DwmProblemPgUpdateSceneMapping;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;


@Builder
public class DwmProblemPgUpdateScene {
    private static final Logger LOG = LoggerFactory.getLogger(DwmProblemPgUpdateScene.class);
    private final String icebergDbName;
    private final String pgDbName;

    private static String getInsertStatement(String pgSinkTableName) {
        String insertSql = "";
        switch (pgSinkTableName) {
            case "pg_sink_dwm_fault_management_statistic":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `value_of_primary_key`,"
                        + "     `problem_id`,"
                        + "     `service_scene_id`"
                        + "FROM %s", "dwm_fault_scene_view");
                break;
            case "pg_sink_dwm_problem_base_statistic":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `problem_id`,"
                        + "     `service_scene_id`"
                        + "FROM %s", "dwm_problem_scene_view");
                break;
            case "pg_sink_dwm_s360_problem_base":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `value_of_primary_key`,"
                        + "     `problem_id`,"
                        + "     `service_scene_id`"
                        + "FROM %s", "dwm_problem_scene_view");
                break;
            default:
                break;
        }
        return insertSql;
    }

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf pgDatawareDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", "dataware"))
                .build();

        final DatabaseConf pgS360DBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", "s360"))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(DwmProblemPgUpdateSceneMapping.icebergTable2FlinkTable,
                        ArrayNode.class), tEnv, catalog
        );

        ArrayNode pgsqlSinkDatawareTable2FlinkTableMap = mapper.readValue(
                DwmProblemPgUpdateSceneMapping.pgsqlSinkDataware2FlinkTable, ArrayNode.class);
        TableUtils.pgdbTable2FlinkTable(
                pgDatawareDBConf, pgsqlSinkDatawareTable2FlinkTableMap, PGSQL, tEnv,"sink");

        ArrayNode pgsqlSinkS360Table2FlinkTableMap = mapper.readValue(
                DwmProblemPgUpdateSceneMapping.pgsqlSinkS3602FlinkTable, ArrayNode.class);
        TableUtils.pgdbTable2FlinkTable(pgS360DBConf, pgsqlSinkS360Table2FlinkTableMap, PGSQL, tEnv,"sink");

        Table table = tEnv.sqlQuery(DwmProblemPgUpdateSceneSql.DwmProblemSceneSql);
        tEnv.createTemporaryView("dwm_problem_scene_view", table);

        Table faultTable = tEnv.sqlQuery(DwmProblemPgUpdateSceneSql.DwmFaultSceneSql);
        tEnv.createTemporaryView("dwm_fault_scene_view", faultTable);

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(getInsertStatement("pg_sink_dwm_fault_management_statistic"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_problem_base_statistic"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_s360_problem_base"));

    }
}