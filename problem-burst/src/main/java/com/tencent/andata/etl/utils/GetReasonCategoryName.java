package com.tencent.andata.etl.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class GetReasonCategoryName  extends ScalarFunction {

    /**
     * 获取原因分类
     * @param reasonCategoryList category json数组
     * @param mapList category map映射
     * @return 返回原因分类
     */
    public String eval(String reasonCategoryList, String mapList) {
        String result = "";
        if (StringUtils.isEmpty(reasonCategoryList) || StringUtils.isEmpty(mapList)) {
            return result;
        }
        String[] array = mapList.split(",");
        Map<Integer, String> map = new HashMap<>();
        for (String item : array) {
            String[] parts = item.split("\\|");
            int key = Integer.parseInt(parts[0]);
            String value = parts[1];
            map.put(key, value);
        }

        reasonCategoryList = reasonCategoryList.replace("[", "").replace("]", "");
        String[] elements = reasonCategoryList.split(","); // 拆分字符串
        String[] resultArray = new String[elements.length];
        for (int i = 0; i < elements.length; i++) {
            if (!StringUtils.isEmpty(elements[i])) {
                int key = Integer.parseInt(elements[i].trim());
                resultArray[i] = map.get(key);
            } else {
                resultArray[i] = "";
            }
        }

        return Arrays.toString(resultArray).replaceFirst("\\[", "").replaceFirst("\\]$", "");
    }
}