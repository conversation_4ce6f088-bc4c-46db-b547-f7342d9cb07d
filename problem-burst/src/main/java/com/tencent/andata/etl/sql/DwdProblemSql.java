package com.tencent.andata.etl.sql;

public class DwdProblemSql {
    public static final String QUERY_T005_PROBLEM_OPERATION_SQL = ""
            + "SELECT \n"
            + "  CAST(operation_id as bigint) as operation_id,\n"
            + "  CAST(problem_id as bigint) as problem_id,\n"
            + "  content,\n"
            + "  operator_type,\n"
            + "  CAST(operation_type as BIGINT) as operation_type,\n"
            + "  CAST(field_type as BIGINT) as field_type,\n"
            + "  options,\n"
            + "  CAST(action as BIGINT) as action,\n"
            + "  operate_time,\n"
            + "  operator,\n"
            + "  CAST(status as BIGINT) as status,\n"
            + "  CAST(target_status as BIGINT) as target_status,\n"
            + "  current_operator,\n"
            + "  next_operator\n"
            + "  from mysql_source_t005_problem_operation\n";

    public static final String QUERY_T002_PROBLEM_MEASURE_SQL = ""
            + "SELECT \n"
            + "  CAST(measure_id as bigint) as measure_id,\n"
            + "  CAST(problem_id as bigint) as problem_id,\n"
            + "  status,\n"
            + "  cast(plan_finish_time as timestamp) as plan_finish_time,\n"
            + "  cast(actual_finish_time as timestamp) as actual_finish_time,\n"
            + "  cast(create_time as timestamp) as create_time\n"
            + "  from iceberg_source_dwd_problem_measure /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n";
}
