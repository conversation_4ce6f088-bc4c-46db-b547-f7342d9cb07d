package com.tencent.andata.etl.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

public class GetOperationList {
    /**
     * json 转 map
     * @param operationJson 操作流水 json
     * @return 返回操作流水Map
     * @throws Exception 异常
     */
    public List<Map<String, Object>> operationJsonToList(String operationJson) throws Exception {
        try {
            if (operationJson == null) {
                throw new NullPointerException();
            }
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, Object>> dataMapList = objectMapper.readValue(operationJson,
                    new TypeReference<List<Map<String, Object>>>() {});
            Collections.sort(dataMapList, new Comparator<Map<String, Object>>() {
                @Override
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    return ((String) o1.get("operate_time")).compareTo((String) o2.get("operate_time"));
                }
            });
            return dataMapList;
        } catch (Exception e) {
            return null;
        }
    }
}
