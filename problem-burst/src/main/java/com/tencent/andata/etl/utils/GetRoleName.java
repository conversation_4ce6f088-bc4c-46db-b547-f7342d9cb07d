package com.tencent.andata.etl.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GetRoleName extends ScalarFunction {
    private static final Logger LOG = LoggerFactory.getLogger(GetRoleName.class);

    /**
     * 获取rolename
     * @param person 员工 rtx
     * @param staffPostJson 员工对应角色
     * @return 返回 rolename
     */
    public String eval(String person, String staffPostJson) {
        if (StringUtils.isEmpty(person) | StringUtils.isEmpty(staffPostJson)) {
            return "";
        }
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> dataMap = new HashMap<>();
        try {
            dataMap = objectMapper.readValue(staffPostJson,
                    new TypeReference<Map<String, String>>() {});
        } catch  (Exception e) {
            e.printStackTrace();
        }
        /*Map<Integer, String> roleMap  = new HashMap<>();
        roleMap.put(0, "其他");
        roleMap.put(1, "供应商");
        roleMap.put(2, "一线");
        roleMap.put(3, "1.5线");
        roleMap.put(4, "运维");
        roleMap.put(5, "产研");
        roleMap.put(8, "售后运维");
        roleMap.put(9, "TCE产研");
        roleMap.put(10, "TBDS产研");
        roleMap.put(11, "垂直产研");
        roleMap.put(12, "二线");*/

        String[] roleList = {
                "其他",
                "供应商",
                "一线",
                "1.5线",
                "运维",
                "产研",
                null,
                null,
                "售后运维",
                "TCE产研",
                "TBDS产研",
                "垂直产研",
                "二线"
        };

        // 按分号拆分person字符串
        String[] personList = person.split(";");
        // 获取匹配的value对应的id值
        String result = "";
        for (String key : personList) {
            String value = dataMap.get(key);
            if (value != null) {
                String[] ids = value.split(",");
                for (String id : ids) {
                    int intId = Integer.parseInt(id);
                    if (intId >= 0 && intId < roleList.length && roleList[intId] != null) {
                        result += roleList[intId] + "&";
                    }
                }
            }
        }

        // 移除最后一个多余的"&"符号
        if (!result.isEmpty()) {
            result = result.substring(0, result.length() - 1);
        }
        return result;
    }
}
