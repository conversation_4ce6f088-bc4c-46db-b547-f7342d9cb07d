package com.tencent.andata.etl.sql;

public class DwmProblemPgUpdateSceneSql {

    public static String DwmProblemSceneSql = ""
            + "SELECT\n"
            + "    CAST(t.problem_id AS BIGINT) AS value_of_primary_key,\n"
            + "    CAST(t.problem_id AS BIGINT) AS problem_id,\n"
            + "    cast(t.service_scene_id as bigint) as service_scene_id\n"
            + "FROM (select *,row_number() over(partition by problem_id order by update_time desc) as rn "
            + "      from iceberg_source_dwd_problem_info /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL') */) as t \n"
            + "where t.rn=1\n";

    public static String DwmFaultSceneSql = ""
            + "SELECT\n"
            + "    CAST(t.problem_id AS BIGINT) AS value_of_primary_key,\n"
            + "    CAST(t.problem_id AS BIGINT) AS problem_id,\n"
            + "    cast(t1.service_scene_id as bigint) as service_scene_id\n"
            + "FROM iceberg_source_dwd_burst_base_info "
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ AS t\n"
            + "LEFT JOIN (select *,row_number() over(partition by problem_id order by update_time desc) as rn \n"
            + "from iceberg_source_dwd_problem_info /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/"
            + " )AS t1 ON t.problem_id = t1.problem_id and t1.rn=1\n";
}
