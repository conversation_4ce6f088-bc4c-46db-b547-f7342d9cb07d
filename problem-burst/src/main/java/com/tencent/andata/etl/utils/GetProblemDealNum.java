package com.tencent.andata.etl.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetProblemDealNum extends ScalarFunction {
    /**
     * 计算问题单处理时长
     * @param operationJson 操作流水 json
     * @return 计算结果
     * @throws Exception 异常
     */
    public  String eval(String operationJson) throws Exception {
        if (StringUtils.isBlank(operationJson)) {
            return "";
        }
        int managerDenyNum = 0;
        int secondThirdLineDenyNum = 0;
        int managerDenyCloseNum = 0;
        int factReopenNum = 0;
        int reopenNum = 0;
        String finalHandler = "";
        String finalApprover = "";
        int isManagerCheck = 0;
        int isSecondThirdLineHandle = 0;
        int isManagerConfirmSituation = 0;

        GetOperationList opList = new GetOperationList();
        List<Map<String, Object>> operationList = opList.operationJsonToList(operationJson);
        for (Map<String, Object> map : operationList) {
            String operator = map.get("operator").toString();
            String targetStatus = map.get("target_status").toString();
            String operationType = map.get("operation_type").toString(); // 操作类型
            if ((operationType.equals("20") && Arrays.asList("12", "4").contains(targetStatus))
                    || (operationType.equals("21") && targetStatus.equals("2"))) {
                finalHandler = operator;
            }

            if (operationType.equals("46") || operationType.equals("47")) {
                finalApprover = operator;
            }
            if (operationType.equals("17") || operationType.equals("18")
                    || operationType.equals("4") || operationType.equals("16")) {
                isManagerCheck = 1;
            }

            switch (operationType) {
                case "18":
                case "4":
                case "16":
                    managerDenyNum += 1;
                    break;
                case "20":
                    isSecondThirdLineHandle = 1;
                    break;
                case "21":
                    secondThirdLineDenyNum += 1;
                    break;
                case "25":
                    managerDenyCloseNum += 1;
                    isManagerConfirmSituation = 1;
                    break;
                case "24":
                    isManagerConfirmSituation = 1;
                    break;
                case "12":
                    reopenNum += 1;
                    break;
                case "27":
                    factReopenNum += 1;
                    break;
                default:
                    break;
            }
        }
        // 创建一个Map来存储返回值
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("manager_deny_num", managerDenyNum);
        resultMap.put("second_third_line_deny_num", secondThirdLineDenyNum);
        resultMap.put("manager_deny_close_num", managerDenyCloseNum);
        resultMap.put("fact_reopen_num", factReopenNum);
        resultMap.put("reopen_num", reopenNum);
        resultMap.put("final_handler", finalHandler);
        resultMap.put("final_approver", finalApprover);
        resultMap.put("is_manager_check", isManagerCheck);
        resultMap.put("is_second_third_line_handle", isSecondThirdLineHandle);
        resultMap.put("is_manager_confirm_situation", isManagerConfirmSituation);
        // 创建ObjectMapper对象
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(resultMap);
    }
}
