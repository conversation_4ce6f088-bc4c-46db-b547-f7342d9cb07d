package com.tencent.andata.etl.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.functions.ScalarFunction;

public class GetSceneName extends ScalarFunction {

    /**
     * 获取归档类型
     * @param serviceSceneDisplayList 归档类型名称json数组
     * @param level 返回归档类型的级别
     * @return 返回归档类型
     */
    public String eval(String serviceSceneDisplayList, int level) {
        String result = "";
        if (StringUtils.isEmpty(serviceSceneDisplayList)) {
            return result;
        }
        String serviceSceneDisplay = ""; // 问题归档类型名称
        String serviceSceneLevel1Name = ""; // 一级产品名称
        String serviceSceneLevel2Name = ""; // 二级产品名称
        String serviceSceneDisplayThirdLevel = ""; // 问题归档类型前三级名称
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode jsonNode = objectMapper.readTree(serviceSceneDisplayList);
            int index = 0;
            for (JsonNode node : jsonNode) {
                String name = node.get("name").asText();
                if (index == 0) {
                    serviceSceneLevel1Name = name;
                }
                else if (index == 1) {
                    serviceSceneLevel2Name = name;
                }
                if (index < 3) {
                    serviceSceneDisplayThirdLevel += name + ">";
                }
                serviceSceneDisplay += name + ">";
                index++;
            }
            // 删除最后一个 ">" 符号
            if (serviceSceneDisplay.length() > 0) {
                serviceSceneDisplay = serviceSceneDisplay.substring(0, serviceSceneDisplay.length() - 1);
            }
            if (serviceSceneDisplayThirdLevel.length() > 0) {
                serviceSceneDisplayThirdLevel = serviceSceneDisplayThirdLevel.substring(
                        0, serviceSceneDisplayThirdLevel.length() - 1);
            }
            if (level == 0) {
                result = serviceSceneDisplay;
            } else if (level == 1) {
                result = serviceSceneLevel1Name;
            } else if (level == 2) {
                result = serviceSceneLevel2Name;
            } else if (level == 3) {
                result = serviceSceneDisplayThirdLevel;
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
