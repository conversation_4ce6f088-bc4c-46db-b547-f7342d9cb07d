package com.tencent.andata.etl.tablemap;

public class DwmProblemPgUpdateSceneMapping {
    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_burst_base_info\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_burst_base_info\",\n"
            + "        \"primaryKey\": \"problem_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_info\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_problem_info\",\n"
            + "        \"primaryKey\": \"problem_id\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlSinkDataware2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwm_problem_base_statistic\",\n"
            + "        \"fTable\":\"pg_sink_dwm_problem_base_statistic\",\n"
            + "        \"fSchema\":\"(problem_id bigint,service_scene_id bigint,"
            + "                       PRIMARY KEY (problem_id) NOT ENFORCED)\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwm_fault_management_statistic\",\n"
            + "        \"fTable\":\"pg_sink_dwm_fault_management_statistic\",\n"
            + "        \"fSchema\":\"(value_of_primary_key bigint,problem_id bigint,service_scene_id bigint,"
            + "                       PRIMARY KEY (problem_id) NOT ENFORCED)\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlSinkS3602FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwm_s360_problem_base\",\n"
            + "        \"fTable\":\"pg_sink_dwm_s360_problem_base\",\n"
            + "        \"fSchema\":\"(value_of_primary_key bigint,problem_id bigint,service_scene_id bigint,"
            + "                       PRIMARY KEY (value_of_primary_key) NOT ENFORCED)\"\n"
            + "    }\n"
            + "]";
}
