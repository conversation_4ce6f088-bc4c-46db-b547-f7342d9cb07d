package com.tencent.andata.etl.tablemap;

public class DwmProblemBrustStatisticMapping {
    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_reason_category\",\n"
            + "        \"fTable\": \"iceberg_sink_dim_reason_category\",\n"
            + "        \"primaryKey\": \"reason_category_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_info\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_problem_info\",\n"
            + "        \"primaryKey\": \"problem_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_burst_base_info\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_burst_base_info\",\n"
            + "        \"primaryKey\": \"problem_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_burst_related_customer\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_burst_related_customer\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_burst_effect_assessment\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_burst_effect_assessment\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_burst_timeline\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_burst_timeline\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_extra\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_problem_extra\",\n"
            + "        \"primaryKey\": \"problem_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_burst_event_log\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_burst_event_log\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_related_ticket\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_problem_related_ticket\",\n"
            + "        \"primaryKey\": \"relation_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_operation\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_problem_operation\",\n"
            + "        \"primaryKey\": \"operation_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_measure\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_problem_measure\",\n"
            + "        \"primaryKey\": \"measure_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwm_fault_management_statistic_rt\",\n"
            + "        \"fTable\": \"iceberg_sink_dwm_fault_management_statistic_rt\",\n"
            + "        \"primaryKey\": \"value_of_primary_key\"\n"
            + "    }\n"
            + "]";
}
