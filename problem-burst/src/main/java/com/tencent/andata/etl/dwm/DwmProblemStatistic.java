package com.tencent.andata.etl.dwm;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;

import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.etl.sql.DwmProblemBrustStatisticSql;
import com.tencent.andata.etl.tablemap.DwmProblemStatisticMapping;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;


@Builder
public class DwmProblemStatistic {

    private final String icebergDbName;


    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        ObjectMapper mapper = new ObjectMapper();

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(DwmProblemStatisticMapping.icebergTable2FlinkTable,
                        ArrayNode.class), tEnv, catalog
        );
        Table operationTable = tEnv.sqlQuery(DwmProblemBrustStatisticSql.DwmProblemOperationStatisticSql);
        flinkEnv.streamTEnv().createTemporaryView("dwm_problem_operation_view", operationTable);

        Table tbl = tEnv.sqlQuery(DwmProblemBrustStatisticSql.DwmProblemBaseStatisticSql);

        // create temporary view for source table
        flinkEnv.streamTEnv().createTemporaryView("dwm_problem_view", tbl);

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(
                insertIntoSql(
                        "dwm_problem_view",
                        "iceberg_sink_dwm_problem_base_statistic_rt",
                        tEnv.from("iceberg_sink_dwm_problem_base_statistic_rt"),
                        ICEBERG)
        );
    }
}
