package com.tencent.andata.etl.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.utils.WorkDurationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Collections;

public class GetProblemReverseOperationDuration extends ScalarFunction {
    /**
     * 计算逆序流水结果
     * @param operationJson 操作流水 json
     * @param holidayList 节假日信息
     * @return 计算结果
     * @throws Exception 异常
     */
    public String eval(String operationJson, String holidayList) throws Exception {
        if (StringUtils.isBlank(operationJson) || StringUtils.isBlank(holidayList)) {
            return "";
        }
        int checkDuration = 0; // 问题审核时长
        int denyDuration = 0; // 问题审核拒绝时长
        int handleDuration = 0;// 给出问题根因及措施所需时长
        int secondThirdLineDenyDuration = 0; // 二三线拒绝时长
        int managerConfirmCloseDuration = 0; // 问题经理确认结单时长
        int managerConfirmDenyDuration = 0; // 问题经理驳回时长
        int problemDealDuration = 0; // 问题总处理时长
        int isApprovedByLeader = 0; // leader是否已经操作审批
        int approvedDurationByLeader = 0; // leader审批时长
        int rejectedDurationByLeader = 0; // leader审批驳回时长
        int processingSolvedDuration = 0; // 问题经待确认结单处理时长

        // 辅助字段
        String checkEndTime = "";
        String denyEndTime = "";
        String handleEndTime = "";
        String secondThirdLineDenyEndTime = "";
        String managerConfirmCloseEndTime = "";
        String managerConfirmDenyEndTime = "";
        String problemDealEndTime = "";
        String approvedDurationByLeaderEndTime = "";
        String rejectedDurationByLeaderEndTime = "";
        boolean managerDurationFlag = false;
        boolean operatorDurationFlag = false;
        String managerDurationStartTime = "";
        String managerDurationEndTime = "";
        String operatorDurationStartTime = "";
        String operatorDurationEndTime = "";
        String processingSolvedDurationEndTime = "";
        List<String> operatorWorkDate = new ArrayList<>();
        List<String> managerWorkDate = new ArrayList<>();

        GetOperationList opList = new GetOperationList();
        List<Map<String, Object>> operationList = opList.operationJsonToList(operationJson);
        Collections.reverse(operationList);
        for (Map<String, Object> map : operationList) {
            String operationType = map.get("operation_type").toString(); // 操作类型
            String operateTime = map.get("operate_time").toString(); // 操作时间

            if (operationType.equals("17") && StringUtils.isEmpty(checkEndTime)) {
                checkEndTime = operateTime; // 问题审核结束时间
            }
            else if ((operationType.equals("18") || operationType.equals("4") || operationType.equals("16"))
                    && StringUtils.isEmpty(denyEndTime)) {
                denyEndTime = operateTime; // 问题审核拒绝结束时间
            }
            else if (operationType.equals("20") && StringUtils.isEmpty(handleEndTime)) {
                handleEndTime = operateTime; // 给出问题根因及措施结束时间
            }
            else if (operationType.equals("21") && StringUtils.isEmpty(secondThirdLineDenyEndTime)) {
                secondThirdLineDenyEndTime = operateTime; // 二三线拒绝结束时间
            }
            else if (operationType.equals("24")) {
                if (StringUtils.isEmpty(managerConfirmCloseEndTime)) {
                    managerConfirmCloseEndTime = operateTime; // 问题经理确认结单结束时间
                }

                if (StringUtils.isEmpty(problemDealEndTime)) {
                    problemDealEndTime = operateTime; // 问题总处理结束时间
                }
            }
            else if  (operationType.equals("25") && StringUtils.isEmpty(managerConfirmDenyEndTime)) {
                managerConfirmDenyEndTime = operateTime; //问题经理驳回结束时间
            }
            else if  (operationType.equals("46")) {
                isApprovedByLeader = 1;
                if (StringUtils.isEmpty(approvedDurationByLeaderEndTime)) {
                    approvedDurationByLeaderEndTime = operateTime; //leader审批通过结束时间
                }
            }
            else if  (operationType.equals("47")) {
                isApprovedByLeader = 1;
                if (StringUtils.isEmpty(rejectedDurationByLeaderEndTime)) {
                    rejectedDurationByLeaderEndTime = operateTime; //leader审批驳回结束时间
                }
            }

            if ((operationType.equals("1") || operationType.equals("15") || operationType.equals("21"))) {
                if (StringUtils.isNotEmpty(checkEndTime)) {
                    double workDuration = WorkDurationUtils.getWorkDuration(
                            operateTime, checkEndTime, holidayList
                    );
                    checkDuration += (int) workDuration;
                    checkEndTime = "";
                }

                if (StringUtils.isNotEmpty(denyEndTime)) {
                    double workDuration = WorkDurationUtils.getWorkDuration(
                            operateTime, denyEndTime, holidayList
                    );
                    denyDuration += (int) workDuration;
                    denyEndTime = "";
                }
            }

            if ((operationType.equals("17") || operationType.equals("25")
                    || operationType.equals("27") || operationType.equals("47"))) {
                if (StringUtils.isNotEmpty(handleEndTime)) {
                    double workDuration = WorkDurationUtils.getWorkDuration(
                            operateTime, handleEndTime, ""
                    );
                    handleDuration += (int) workDuration;
                    handleEndTime = "";
                }

                if (StringUtils.isNotEmpty(secondThirdLineDenyEndTime)) {
                    double workDuration = WorkDurationUtils.getWorkDuration(
                            operateTime, secondThirdLineDenyEndTime, ""
                    );
                    secondThirdLineDenyDuration += (int) workDuration;
                    secondThirdLineDenyEndTime = "";
                }
            }

            if (operationType.equals("13")) {
                if (StringUtils.isNotEmpty(managerConfirmCloseEndTime)) {
                    double workDuration = WorkDurationUtils.getWorkDuration(
                            operateTime, managerConfirmCloseEndTime, ""
                    );
                    managerConfirmCloseDuration += (int) workDuration;
                    managerConfirmCloseEndTime = "";
                }

                if (StringUtils.isNotEmpty(managerConfirmDenyEndTime)) {
                    double workDuration = WorkDurationUtils.getWorkDuration(
                            operateTime, managerConfirmDenyEndTime, ""
                    );
                    managerConfirmDenyDuration += (int) workDuration;
                    managerConfirmDenyEndTime = "";
                }
            }

            if (operationType.equals("1") || operationType.equals("27")) {
                if (StringUtils.isNotEmpty(problemDealEndTime)) {
                    double workDuration = WorkDurationUtils.getWorkDuration(
                            operateTime, problemDealEndTime, ""
                    );
                    problemDealDuration += (int) workDuration;
                    problemDealEndTime = "";
                }
            }

            if (operationType.equals("20")) {
                if (StringUtils.isNotEmpty(approvedDurationByLeaderEndTime)) {
                    double workDuration = WorkDurationUtils.getWorkDuration(
                            operateTime, approvedDurationByLeaderEndTime, holidayList
                    );
                    approvedDurationByLeader += (int) workDuration;
                    approvedDurationByLeaderEndTime = "";
                }
                if (StringUtils.isNotEmpty(rejectedDurationByLeaderEndTime)) {
                    double workDuration = WorkDurationUtils.getWorkDuration(
                            operateTime, rejectedDurationByLeaderEndTime, holidayList
                    );
                    rejectedDurationByLeader += (int) workDuration;
                    rejectedDurationByLeaderEndTime = "";
                }
            }
            // 问题经理处理时长
            if (!managerDurationFlag && (operationType.equals("17") || operationType.equals("18"))) {
                managerDurationEndTime = operateTime;
                managerDurationFlag = true;
            }
            if (managerDurationFlag && (operationType.equals("1"))
                    || operationType.equals("15") || operationType.equals("21")) {
                managerDurationStartTime = operateTime;
                managerDurationFlag = false;
            }
            if (StringUtils.isNotEmpty(managerDurationStartTime)
                    && StringUtils.isNotEmpty(managerDurationEndTime)) {
                List<String> workDate = WorkDurationUtils.getWorkDate(
                        managerDurationStartTime, managerDurationEndTime, holidayList
                );
                if (workDate != null) {
                    managerWorkDate.addAll(workDate);
                }
                managerDurationStartTime = "";
                managerDurationEndTime = "";
            }
            // 处理人处理时长
            if (!operatorDurationFlag && (operationType.equals("20") || operationType.equals("21"))) {
                operatorDurationEndTime = operateTime;
                operatorDurationFlag = true;
            }
            if (operatorDurationFlag && (operationType.equals("17"))
                    || operationType.equals("25") || operationType.equals("27")) {
                operatorDurationStartTime = operateTime;
                operatorDurationFlag = false;
            }
            if (StringUtils.isNotEmpty(operatorDurationStartTime)
                    && StringUtils.isNotEmpty(operatorDurationEndTime)) {
                List<String> workDate = WorkDurationUtils.getWorkDate(
                        operatorDurationStartTime, operatorDurationEndTime, holidayList
                );
                if (workDate != null) {
                    operatorWorkDate.addAll(workDate);
                }
                operatorDurationStartTime = "";
                operatorDurationEndTime = "";
            }
            String fieldType = map.get("field_type").toString();
            String targetStatus = map.get("target_status").toString();
            // 问题经待确认结单处理时长
            if (fieldType.equals("1") && ((targetStatus.equals("6")
                    && operationType.equals("24")) || operationType.equals("25"))
                    && StringUtils.isEmpty(processingSolvedDurationEndTime)) {
                processingSolvedDurationEndTime = operateTime;
            }
            if (fieldType.equals("1") && (operationType.equals("13") || operationType.equals("46"))
                    && targetStatus.equals("5")) {
                if (StringUtils.isEmpty(processingSolvedDurationEndTime)) {
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    processingSolvedDurationEndTime = now.format(formatter);
                }
                double workDuration = WorkDurationUtils.getWorkDuration(
                        operateTime, processingSolvedDurationEndTime, holidayList
                );
                processingSolvedDuration += (int) workDuration;
                processingSolvedDurationEndTime = "";
            }

        }

        int managerDuration = (int) managerWorkDate.stream().distinct().count();
        int operatorDuration = (int) operatorWorkDate.stream().distinct().count();

        // 创建一个Map来存储返回值
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("check_duration", checkDuration);
        resultMap.put("deny_duration", denyDuration);
        resultMap.put("handle_duration", handleDuration);
        resultMap.put("second_third_line_deny_duration", secondThirdLineDenyDuration);
        resultMap.put("manager_confirm_close_duration", managerConfirmCloseDuration);
        resultMap.put("manager_confirm_deny_duration", managerConfirmDenyDuration);
        resultMap.put("problem_deal_duration", problemDealDuration);
        resultMap.put("is_approved_by_leader", isApprovedByLeader);
        resultMap.put("approved_duration_by_leader", approvedDurationByLeader);
        resultMap.put("rejected_duration_by_leader", rejectedDurationByLeader);
        resultMap.put("manager_duration", managerDuration);
        resultMap.put("operator_duration", operatorDuration);
        resultMap.put("processing_solved_duration", processingSolvedDuration);
        // 创建ObjectMapper对象
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(resultMap);
    }
}
