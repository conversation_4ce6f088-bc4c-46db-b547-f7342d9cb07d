package com.tencent.andata.etl.tablemap;

public class ProblemInfoMapping {

    public static String mysqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"t005_problem_operation\",\n"
            + "        \"partitionPattern\": \"_[0-9]{6}\",\n"
            + "        \"fTable\": \"mysql_source_t005_problem_operation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t006_problem_operation_field\",\n"
            + "        \"partitionPattern\": \"_[0-9]{6}\",\n"
            + "        \"fTable\": \"mysql_source_t006_problem_operation_field\"\n"
            + "    }\n"
            + "]";

    public static String starRocksTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwm_problem_base_statistic\",\n"
            + "        \"fTable\":\"flink_starrock_dwm_problem_base_statistic\",\n"
            + "        \"primaryKey\": \"value_of_primary_key\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_burst_related_customer\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_burst_related_customer\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_problem_info\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_problem_info\",\n"
            + "        \"primaryKey\": \"problem_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_problem_event\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_problem_event\",\n"
            + "        \"primaryKey\": \"value_of_primary_key\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_problem_extra\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_problem_extra\",\n"
            + "        \"primaryKey\": \"problem_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_problem_measure\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_problem_measure\",\n"
            + "        \"primaryKey\": \"measure_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_problem_operation\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_problem_operation\",\n"
            + "        \"primaryKey\": \"operation_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_problem_related_ticket\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_problem_related_ticket\",\n"
            + "        \"primaryKey\": \"relation_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_problem_operation_field\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_problem_operation_field\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_burst_effect_assessment\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_burst_effect_assessment\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_burst_timeline\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_burst_timeline\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_burst_duty_split\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_burst_duty_split\",\n"
            + "        \"primaryKey\": \"value_of_primary_key\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_operation\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_problem_operation\",\n"
            + "        \"primaryKey\": \"operation_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_operation_field\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_problem_operation_field\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_burst_related_customer\",\n"
            + "        \"fTable\": \"iceberg_source2_dwd_burst_related_customer\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_info\",\n"
            + "        \"fTable\": \"iceberg_source2_dwd_problem_info\",\n"
            + "        \"primaryKey\": \"problem_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_measure\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_problem_measure\",\n"
            + "        \"primaryKey\": \"measure_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_event\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_problem_event\",\n"
            + "        \"primaryKey\": \"value_of_primary_key\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_extra\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_problem_extra\",\n"
            + "        \"primaryKey\": \"problem_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_problem_related_ticket\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_problem_related_ticket\",\n"
            + "        \"primaryKey\": \"relation_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_burst_effect_assessment\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_burst_effect_assessment\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_burst_timeline\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_burst_timeline\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_burst_duty_split\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_burst_duty_split\",\n"
            + "        \"primaryKey\": \"value_of_primary_key\"\n"
            + "    }\n"
            + "]";

}

