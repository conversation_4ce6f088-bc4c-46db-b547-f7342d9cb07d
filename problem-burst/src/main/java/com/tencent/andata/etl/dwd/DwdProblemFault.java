package com.tencent.andata.etl.dwd;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.TableUtils.row2Json;
import static com.tencent.andata.utils.TableUtils.sinkToHbase;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

import com.tencent.andata.etl.sql.DwdProblemSql;
import com.tencent.andata.etl.tablemap.ProblemInfoMapping;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;


@Builder
public class DwdProblemFault {

    private final String icebergDbName;


    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "problem"))
                .build();

        DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(ProblemInfoMapping.mysqlTable2FlinkTable,
                ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlDBConf, mysqlTable2FlinkTableMap, MYSQL, tEnv);

        TableUtils.hbaseTable2FlinkTable(
                "h_problem_operation",
                "problem_operation",
                "cf", tEnv
        );
        TableUtils.hbaseTable2FlinkTable(
                "h_problem_measure",
                "problem_measure",
                "cf", tEnv
        );
        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(ProblemInfoMapping.icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );

        ArrayNode starRocksTable2FlinkTableMap = mapper.readValue(ProblemInfoMapping.starRocksTable2FlinkTable,
                ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(rocksDbConf, starRocksTable2FlinkTableMap, ROCKS, tEnv);

        Map<String, Operation> tblMap = new HashMap<>();
        tblMap.put("mysql_source_t005_problem_operation",
                Operation.builder().serverId("/*+ OPTIONS('server-id'='1101-1200') */").
                        icebergSink("iceberg_sink_dwd_problem_operation").build());
        tblMap.put("mysql_source_t006_problem_operation_field",
                Operation.builder().serverId("/*+ OPTIONS('server-id'='1201-1300') */").
                        icebergSink("iceberg_sink_dwd_problem_operation_field").build());

        // 问题单操作流水表
        Table problemOperationTable = tEnv.sqlQuery(DwdProblemSql.QUERY_T005_PROBLEM_OPERATION_SQL);
        tEnv.createTemporaryView("problem_operation_view", problemOperationTable);
        tEnv.createTemporaryView("problem_operation_json_view", tEnv.sqlQuery(
                row2Json(problemOperationTable,
                        "CAST(`problem_id` AS STRING) || '-' || CAST(`operation_id` AS STRING)",
                        "problem_operation_view"
                )));

        // 问题单措施表
        Table problemMeasureTable = tEnv.sqlQuery(DwdProblemSql.QUERY_T002_PROBLEM_MEASURE_SQL);
        tEnv.createTemporaryView("problem_measure_view", problemMeasureTable);
        tEnv.createTemporaryView("problem_measure_json_view", tEnv.sqlQuery(
                row2Json(problemMeasureTable,
                        "CAST(`problem_id` AS STRING) || '-' || CAST(`measure_id` AS STRING)",
                        "problem_measure_view"
                )));

        StatementSet stmtSet = flinkEnv.stmtSet();
        for (Map.Entry<String, Operation> entry : tblMap.entrySet()) {
            stmtSet.addInsertSql(insertIntoSql(
                    entry.getKey() + " " + entry.getValue().getServerId(),
                    entry.getValue().getIcebergSink(),
                    tEnv.from(entry.getValue().getIcebergSink()),
                    ICEBERG
            ));
        }

        String hints = "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
                + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/";
        tEnv.createTemporaryView(
                "dwd_burst_related_customer_view",
                tEnv.sqlQuery(String.format("SELECT * FROM iceberg_source2_dwd_burst_related_customer %s", hints)));

        tEnv.createTemporaryView(
                "dwd_problem_info_view",
                tEnv.sqlQuery(String.format("SELECT * FROM iceberg_source2_dwd_problem_info %s", hints)));

        tEnv.createTemporaryView(
                "dwd_problem_event_view",
                tEnv.sqlQuery(String.format("SELECT * FROM iceberg_source_dwd_problem_event %s", hints)));

        tEnv.createTemporaryView(
                "dwd_problem_extra_view",
                tEnv.sqlQuery(String.format("SELECT * FROM iceberg_source_dwd_problem_extra %s", hints)));

        tEnv.createTemporaryView(
                "dwd_problem_related_ticket_view",
                tEnv.sqlQuery(String.format("SELECT * FROM iceberg_source_dwd_problem_related_ticket %s", hints)));

        tEnv.createTemporaryView(
                "dwd_burst_effect_assessment_view",
                tEnv.sqlQuery(String.format("SELECT * FROM iceberg_source_dwd_burst_effect_assessment %s", hints)));

        tEnv.createTemporaryView(
                "dwd_burst_timeline_view",
                tEnv.sqlQuery(String.format("SELECT * FROM iceberg_source_dwd_burst_timeline %s", hints)));

        tEnv.createTemporaryView(
                "dwd_burst_duty_split_view",
                tEnv.sqlQuery(String.format("SELECT * FROM iceberg_source_dwd_burst_duty_split %s", hints)));

        tEnv.createTemporaryView(
                "dwd_problem_measure_view",
                tEnv.sqlQuery(String.format("SELECT * FROM iceberg_source_dwd_problem_measure %s", hints)));


        stmtSet.addInsertSql(insertIntoSql(
                "dwd_burst_related_customer_view",
                "flink_starrocks_dwd_burst_related_customer",
                tEnv.from("flink_starrocks_dwd_burst_related_customer"),
                ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "dwd_problem_info_view",
                        "flink_starrocks_dwd_problem_info",
                        tEnv.from("flink_starrocks_dwd_problem_info"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "problem_operation_view",
                        "flink_starrocks_dwd_problem_operation",
                        tEnv.from("flink_starrocks_dwd_problem_operation"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t006_problem_operation_field",
                        "flink_starrocks_dwd_problem_operation_field",
                        tEnv.from("flink_starrocks_dwd_problem_operation_field"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "dwd_problem_measure_view",
                        "flink_starrocks_dwd_problem_measure",
                        tEnv.from("flink_starrocks_dwd_problem_measure"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "dwd_problem_event_view",
                        "flink_starrocks_dwd_problem_event",
                        tEnv.from("flink_starrocks_dwd_problem_event"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "dwd_problem_extra_view",
                        "flink_starrocks_dwd_problem_extra",
                        tEnv.from("flink_starrocks_dwd_problem_extra"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "dwd_problem_related_ticket_view",
                        "flink_starrocks_dwd_problem_related_ticket",
                        tEnv.from("flink_starrocks_dwd_problem_related_ticket"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "dwd_burst_effect_assessment_view",
                        "flink_starrocks_dwd_burst_effect_assessment",
                        tEnv.from("flink_starrocks_dwd_burst_effect_assessment"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "dwd_burst_timeline_view",
                        "flink_starrocks_dwd_burst_timeline",
                        tEnv.from("flink_starrocks_dwd_burst_timeline"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "dwd_burst_duty_split_view",
                        "flink_starrocks_dwd_burst_duty_split",
                        tEnv.from("flink_starrocks_dwd_burst_duty_split"),
                        ROCKS
                ))
                .addInsertSql(sinkToHbase("problem_operation_json_view", "h_problem_operation"))
                // 问题单操作流水表写入hbase
                .addInsertSql(sinkToHbase("problem_measure_json_view", "h_problem_measure"));
    }
}
