package com.tencent.andata.etl;


import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

import com.tencent.andata.etl.dwd.DwdProblemFault;
import com.tencent.andata.etl.dwm.DwmProblemStatistic;
import com.tencent.andata.etl.dwm.S360ProblemBaseStatistic;
import com.tencent.andata.etl.dwm.DwmBrustStatistic;
import com.tencent.andata.etl.dwm.DwmProblemPgUpdateScene;
import com.tencent.andata.etl.utils.GetReasonCategoryName;
import com.tencent.andata.etl.utils.GetSceneName;
import com.tencent.andata.etl.utils.GetProblemDealDuration;
import com.tencent.andata.etl.utils.GetProblemReverseOperationDuration;
import com.tencent.andata.etl.utils.GetProblemFirstTime;
import com.tencent.andata.etl.utils.GetWorkDuration;
import com.tencent.andata.etl.utils.GetProblemDealNum;
import com.tencent.andata.etl.utils.GetRoleName;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.udf.GetJsonObject;
import com.tencent.andata.utils.udf.GetMinute;
import com.tencent.andata.utils.udf.UnicodeToString;
import com.tencent.andata.utils.udf.ToJson;
import com.tencent.andata.utils.udf.GetMidStringListToInt;

import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Application {

    private static final Logger logger = LoggerFactory.getLogger(Application.class);

    /**
     * 在一个main方法中运行多个flink etl 任务
     *
     * @param args args[0] = iceberg db name, args[1] = pg db name
     */
    public static void main(String[] args) throws Exception {
        FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);

        final IcebergCatalogReader catalog = new IcebergCatalogReader();

        String parallelism = parameterTool.get("parallelism", "1");
        // table env config
        Configuration configuration = flinkEnv.streamTEnv().getConfig().getConfiguration();

        // 设置flink应用程序名称
        configuration.setString("pipeline.name", "Problem Burst Application");

        //开启微批模式
        configuration.setString("table.exec.mini-batch.size", "5000");
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "3 s");
        // enable two-phase, i.e. local-global aggregation
        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");


        // 状态保留3天
        configuration.setString("execution.runtime-mode", "streaming");
        configuration.setString("execution.checkpointing.interval", "30s");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        //configuration.setString("table.exec.sink.upsert-materialize", "FORCE");
        configuration.setString("table.dynamic-table-options.enabled", "true");

        // 关闭iceberg source的自动推断并行度
        configuration.setString("table.exec.iceberg.infer-source-parallelism", "false");
        //configuration.setString("table.exec.resource.default-parallelism", parallelism);

        // 优化join性能
        configuration.setString("table.optimizer.join-reorder-enabled", "true");
        configuration.setString("table.optimizer.reuse-source-enabled", "true");
        configuration.setString("table.optimizer.reuse-sub-plan-enabled", "true");
        configuration.setString("table.optimizer.multiple-input-enabled", "true");
        configuration.setString("table.exec.disabled-operators", "NestedLoopJoin");
        configuration.setString("table.optimizer.source.aggregate-pushdown-enabled", "true");

        // 注册 flink udf
        flinkEnv.streamTEnv().createFunction("get_min", GetMinute.class);
        flinkEnv.streamTEnv().createFunction("unicode_to_string", UnicodeToString.class);
        flinkEnv.hiveModuleV2().registryHiveUDF("to_json", ToJson.class.getName());
        flinkEnv.streamTEnv().createFunction("get_mid", GetMidStringListToInt.class);
        flinkEnv.streamTEnv().createFunction("get_category_name", GetReasonCategoryName.class);
        flinkEnv.streamTEnv().createFunction("get_scene_name", GetSceneName.class);
        flinkEnv.streamTEnv().createFunction("get_problem_duration", GetProblemDealDuration.class);
        flinkEnv.streamTEnv().createFunction("get_problem_reserse_duration", GetProblemReverseOperationDuration.class);
        flinkEnv.streamTEnv().createFunction("get_problem_first_time", GetProblemFirstTime.class);
        flinkEnv.streamTEnv().createFunction("get_duration_time", GetWorkDuration.class);
        flinkEnv.streamTEnv().createFunction("get_problem_deal_num", GetProblemDealNum.class);
        flinkEnv.streamTEnv().createFunction("get_role_name", GetRoleName.class);
        flinkEnv.streamTEnv().createFunction("get_json_object", GetJsonObject.class);
        // get iceberg db name and pg db name from args
        String pgDbName = parameterTool.get("pgDbName");
        String icebergDbName = parameterTool.get("icebergDbName");

        // instantiate the DWD ETL
        List<Object> appList = new ArrayList<>();
        appList.add(DwdProblemFault.builder().icebergDbName(icebergDbName).build());
        appList.add(DwmProblemStatistic.builder().icebergDbName(icebergDbName).build());
        appList.add(S360ProblemBaseStatistic.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
        appList.add(DwmBrustStatistic.builder().icebergDbName(icebergDbName).build());
        appList.add(DwmProblemPgUpdateScene.builder().icebergDbName(icebergDbName).build());

        // 遍历appList，调用run方法
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", flinkEnv, catalog)));


        // execute the sql statements
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("Problem Burst Application");
    }
}
