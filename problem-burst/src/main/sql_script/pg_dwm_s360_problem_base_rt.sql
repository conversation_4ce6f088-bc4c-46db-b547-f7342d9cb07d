-- 服务360问题单表
drop table dwm_s360_problem_base_rt;
CREATE TABLE  dwm_s360_problem_base_rt (
    record_update_time timestamp(6) without time zone default (now() at time zone ('utc-8')),
    value_of_primary_key bigint primary key,

    problem_id bigint,
    title varchar(1024),
    description text,
    service_scene_display varchar(1024),
    service_scene_level1_name varchar(1024),
    service_scene_level2_name varchar(1024),
    service_scene_display3_name varchar(1024),
    reason text,
    reason_category varchar(1024),
    status varchar(1024),
    creator varchar(1024),
    current_operator varchar(1024),
    manager varchar(1024),
    responsible varchar(1024),
    create_time timestamp(6) without time zone,
    related_ticket_id text,
    related_ticket_id_amount int,
    priority varchar(1024),
    problem_delivery_mode varchar(1024),
    close_time timestamp(6) without time zone,
    measures_is_overtime int not null,
    measures_plan_finish_time timestamp(6) without time zone,
    is_close_flag int,
    priority_score int,
    priority_cost bigint,
    is_fault int,
    is_lone_problem int,
    handle_duration bigint,
    improve_duration bigint,
    service_scene_id int
);
comment on table dwm_s360_problem_base_rt is '服务360问题单表';

comment on column dwm_s360_problem_base_rt.value_of_primary_key is '唯一键';
comment on column dwm_s360_problem_base_rt.record_update_time is '记录更新时间';

comment on column dwm_s360_problem_base_rt.problem_id is '问题单ID';
comment on column dwm_s360_problem_base_rt.title is '问题标题';
comment on column dwm_s360_problem_base_rt.description is '问题描述';
comment on column dwm_s360_problem_base_rt.service_scene_display is '问题归档类型';
comment on column dwm_s360_problem_base_rt.service_scene_level1_name is '一级产品名称';
comment on column dwm_s360_problem_base_rt.service_scene_display3_name is '前三级产品名称';
comment on column dwm_s360_problem_base_rt.reason is '原因分析';
comment on column dwm_s360_problem_base_rt.reason_category is '原因分类';
comment on column dwm_s360_problem_base_rt.status is '问题单状态';
comment on column dwm_s360_problem_base_rt.creator is '问题单创建人';
comment on column dwm_s360_problem_base_rt.current_operator is '当前处理人';
comment on column dwm_s360_problem_base_rt.manager is '问题经理';
comment on column dwm_s360_problem_base_rt.responsible is '负责人';
comment on column dwm_s360_problem_base_rt.create_time is '问题单创建时间';
comment on column dwm_s360_problem_base_rt.related_ticket_id is '关联事件';
comment on column dwm_s360_problem_base_rt.related_ticket_id_amount is '关联事件量';
comment on column dwm_s360_problem_base_rt.priority is '问题单优先级：SVIP非常紧急,非常紧急,紧急,标准';
comment on column dwm_s360_problem_base_rt.problem_delivery_mode is '交付模式:1|公有云，2｜私有云';
comment on column dwm_s360_problem_base_rt.close_time is '问题单结单时间';
comment on column dwm_s360_problem_base_rt.measures_is_overtime is '改进中是否超时';
comment on column dwm_s360_problem_base_rt.measures_plan_finish_time is '最晚计划完成时间';
comment on column dwm_s360_problem_base_rt.is_close_flag is '是否已结单';
comment on column dwm_s360_problem_base_rt.priority_score is '优先级分值';
comment on column dwm_s360_problem_base_rt.priority_cost is '优先级成本';
comment on column dwm_s360_problem_base_rt.is_fault is '是否故障单：0｜问题单,1｜故障单';
comment on column dwm_s360_problem_base_rt.is_lone_problem is '曾经是否是L1优先级；1是0否';
comment on column dwm_s360_problem_base_rt.service_scene_level2_name is '二级产品名称';
comment on column dwm_s360_problem_base_rt.handle_duration is '给出问题根因及措施所需时长';
comment on column dwm_s360_problem_base_rt.improve_duration is '问题单改进时长';
comment on column dwm_s360_problem_base_rt.service_scene_id is '归档类型 id';

create unique index index_dwm_s360_problem_base_rt_id on dwm_s360_problem_base_rt (value_of_primary_key);
create index index_dwm_s360_problem_base_rt_manager on dwm_s360_problem_base_rt (service_scene_level1_name);
create index index_dwm_s360_problem_base_rt_create_time on dwm_s360_problem_base_rt (create_time);


-- 服务 360问题单中间表priority_cost计算
CREATE VIEW dwm_s360_problem_base_tmp_view as
select
    a.problem_id,
    a.title,
    a.description,
    a.service_scene_display,
    a.service_scene_level1_name,
    a.service_scene_display3_name,
    a.reason,
    a.reason_category,
    a.status,
    a.creator,
    a.current_operator,
    a.manager,
    a.responsible,
    a.create_time,
    a.related_ticket_id,
    a.related_ticket_id_amount,
    a.priority,
    a.problem_delivery_mode,
    a.close_time,
    a.measures_is_overtime,
    a.measures_plan_finish_time,
    a.is_close_flag,
    a.priority_score,
    cast(case when a.status='处理中' and a.priority<>'L1' then COALESCE(b.processing_priority_cost,0) else a.priority_cost end as bigint) as priority_cost,
    a.is_fault,
    a.is_lone_problem,
    a.service_scene_level2_name,
    a.service_scene_id
from dwm_s360_problem_base_rt as a
         left join (
    SELECT service_scene_display3_name, percentile_cont(0.5) WITHIN GROUP (ORDER BY improve_duration+handle_duration) AS processing_priority_cost
    FROM dwm_s360_problem_base_rt
    where status in ('待确认结单','已结单') and service_scene_display3_name<>''
    group by service_scene_display3_name
) as b on a.service_scene_display3_name=b.service_scene_display3_name
;



-- 服务360问题单表
CREATE VIEW dwm_s360_problem_base_rt_view as
select
a.problem_id,
a.title,
a.description,
a.service_scene_level1_name,
a.service_scene_level2_name,
a.reason,
a.reason_category,
a.status,
a.creator,
a.current_operator,
a.manager,
a.responsible,
a.create_time,
a.related_ticket_id,
a.related_ticket_id_amount,
a.priority,
a.close_time,
a.measures_is_overtime,
a.measures_plan_finish_time,
round(case when (b.max_priority_score-b.min_priority_score)=0 then 0
else (((a.priority_score-b.min_priority_score)*1.0)/(b.max_priority_score-b.min_priority_score))*80 end
+ case when (b.max_priority_cost-b.min_priority_cost)=0 then 0
else (((a.priority_cost-b.min_priority_cost)*1.0)/(b.max_priority_cost-b.min_priority_cost))*20 end,0) as roi_value,
is_close_flag,
a.problem_delivery_mode,
a.service_scene_id
from dwm_s360_problem_base_tmp_view as a
left join (
    select
    service_scene_level1_name,
    max(priority_score) as max_priority_score,
    min(priority_score) as min_priority_score,
    max(priority_cost) as max_priority_cost,
    min(priority_cost) as min_priority_cost
    from dwm_s360_problem_base_tmp_view
    where is_fault=0 or (priority ='L1' and is_fault=1 and is_lone_problem=1)
    group by service_scene_level1_name
) as b
on a.service_scene_level1_name=b.service_scene_level1_name
where is_fault=0 or (priority ='L1' and is_fault=1 and is_lone_problem=1);

GRANT ALL ON dwm_s360_problem_base_rt_view TO andata_reader;

comment on column dwm_s360_problem_base_rt_view.problem_id is '问题单ID';
comment on column dwm_s360_problem_base_rt_view.title is '问题标题';
comment on column dwm_s360_problem_base_rt_view.description is '问题描述';
comment on column dwm_s360_problem_base_rt_view.service_scene_level1_name is '一级产品名称';
comment on column dwm_s360_problem_base_rt_view.service_scene_level2_name is '二级产品名称';
comment on column dwm_s360_problem_base_rt_view.reason is '原因分析';
comment on column dwm_s360_problem_base_rt_view.reason_category is '原因分类';
comment on column dwm_s360_problem_base_rt_view.status is '问题单状态';
comment on column dwm_s360_problem_base_rt_view.creator is '问题单创建人';
comment on column dwm_s360_problem_base_rt_view.current_operator is '当前处理人';
comment on column dwm_s360_problem_base_rt_view.manager is '问题经理';
comment on column dwm_s360_problem_base_rt_view.responsible is '负责人';
comment on column dwm_s360_problem_base_rt_view.create_time is '问题单创建时间';
comment on column dwm_s360_problem_base_rt_view.related_ticket_id is '关联事件';
comment on column dwm_s360_problem_base_rt_view.related_ticket_id_amount is '关联事件量';
comment on column dwm_s360_problem_base_rt_view.priority is '问题单优先级：SVIP非常紧急,非常紧急,紧急,标准';
comment on column dwm_s360_problem_base_rt_view.close_time is '问题单结单时间';
comment on column dwm_s360_problem_base_rt_view.measures_is_overtime is '改进中是否超时';
comment on column dwm_s360_problem_base_rt_view.measures_plan_finish_time is '最晚计划完成时间';
comment on column dwm_s360_problem_base_rt_view.is_close_flag is '是否已结单';
comment on column dwm_s360_problem_base_rt_view.roi_value is 'ROI';
comment on column dwm_s360_problem_base_rt_view.problem_delivery_mode is '交付模式';