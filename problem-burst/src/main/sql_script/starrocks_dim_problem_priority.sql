CREATE TABLE dim_problem_priority
(
    `value_of_primary_key` INT          COMMENT '唯一键',
    `record_update_time`   DATETIME     COMMENT '记录更新时间',
    `key`                  INT          COMMENT '问题优先级key',
    `value`                STRING       COMMENT '问题优先级名称'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
