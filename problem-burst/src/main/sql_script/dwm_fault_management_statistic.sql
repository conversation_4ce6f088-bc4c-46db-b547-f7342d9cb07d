
CREATE TABLE dwm_fault_management_statistic(
   value_of_primary_key bigint,
   record_update_time datetime,
   problem_id bigint,
   create_time datetime,
   status int,
   priority int,
   description string,
   manager string,
   title string,
   current_operator string,
   reason_category bigint,
   is_fault int,
   reason string,
   reason_category_supplement string,
   is_lone_problem int,
   service_scene_display string,
   is_affect_public_cloud int,
   is_affect_private_cloud int,
   is_public_cloud_problem_solved int,
   is_private_cloud_problem_solved int,
   problem_change_production_href string,
   burst_status int,
   burst_title string,
   fault_grade int,
   is_related_with_testing int,
   is_without_testing int,
   fault_influenced_customer string,
   delivery_mode int,
   is_supplement int,
   is_yanxi int,
   is_sensitive_accident int,
   is_major_accident int,
   affected_bussiness_display string,
   relative_owners string,
   affected_range int,
   fault_category int,
   affect_availability int,
   data_miss_desc string,
   is_regional_delivery int,
   region_id int,
   trade_id int,
   is_breaking_prohibited_rules int,
   burst_time_end string,
   is_mechanism_common_problem int,
   common_problem_description string,
   is_change_production_happen int,
   change_production_happen int,
   ltc_id string,
   customer_cid string,
   judian_name string,
   judian_id string,
   project_information string,
   final_customer string,
   is_involve_regional_business int,
   is_violate_sla int,
   qa_summary string,
   burst_time_start string,
   breaking_standard_rules string,
   industry_department string,
   feedback_customer string,
   is_transfer_operation int,
   affected_department string,
   affected_department_display string,
   qa string,
   no_private_cloud_type int,
   involve_responsibility_attribution int,
   process_operator string,
   process_operator_time string,
   judian_status int,
   manager_or_second_intervention_time string,
   one_button_push int,
   one_button_call int,
   one_button_call_object string,
   one_button_call_persons string,
   burst_push int,
   maintenance_or_research_in_time string,
   backend_leader string,
   process string,
   duty_leader_list string,
   duty_gm_list string,
   is_breaking_safe_rules int,
   locate_reason_time string,
   regional_business_person string,
   research_in_time string,
   plan_finish_time datetime,
   responsible string,
   validate_method string,
   validate_person string,
   improvement_status int,
   first_burst_time datetime,
   third_burst_time datetime,
   first_transfer_special_time datetime,
   first_transfer_operation_or_research_time datetime,
   burst_create_time datetime,
   monitor_time datetime,
   recover_time datetime,
   influence_customer_counts bigint,
   influence_uin_list string,
   influence_customer_name_list string,
   affected_business string,
   affected_module string,
   affected_minutes string,
   responsible_business string,
   responsible_module string,
   responsible_minutes string,
   responsible_time string,
   affected_time string,
   discovery_time datetime,
   discovery_way int,
   duty_delimitation int,
   duty_group_display string,
   is_break_change_rule int,
   monitor_alarm_situation int,
   is_push_customer int,
   is_monitor_confirm int,
   monitor_belong_to int,
   monitor_improve_remark string,
   break_change_rules string,
   change_duty_group string,
   change_duty_group_display string,
   first_report_time datetime,
   l_one_hidden int,
   reason_category_name string,
   all_num	bigint,
   p0_num	bigint,
   p1_num	bigint,
   p2_num	bigint,
   p3_num	bigint,
   p4_num	bigint,
   is_affect_multi int,
   customer_impact_time int
)ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
