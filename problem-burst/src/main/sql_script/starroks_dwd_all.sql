CREATE TABLE dim_burst_status (
    `value_of_primary_key` int comment '唯一键'
    , `record_update_time` 	datetime comment '记录更新时间'
    , `burst_status_id` int comment '状态key'
    , `burst_status_name` string comment '问题状态名称'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");



CREATE TABLE dim_burst_fault_grade (
    `value_of_primary_key`  int comment '唯一键'
    , `record_update_time` datetime comment '记录更新时间'
    , `key` int comment '故障定级key'
    , `value` string comment '故障定级名称'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");


CREATE TABLE dwd_problem_event(
    value_of_primary_key BIGINT comment '唯一键',
    record_update_time datetime comment '记录更新时间',
    dwd_is_valid int comment '记录是否有效',

    id BIGINT comment 'id',
    problem_id BIGINT comment '问题单id',
    status INT comment '状态id',
    status_cn STRING comment '状态',
    prev_status INT comment '前一个状态id',
    prev_status_cn STRING comment '前一个状态',
    creator STRING comment '创建人',
    modifier STRING comment '更新人',
    create_time datetime comment '创建时间',
    update_time datetime comment '更新时间',
    product STRING comment '云产品名称',
    title STRING comment '事件名称',
    event_status INT comment '事件状态id',
    event_status_cn STRING comment '事件状态',
    report_time datetime comment '事件上报时间',
    region STRING comment '地域',
    region_name STRING comment '地域名称',
    zone STRING comment '可用区',
    zone_name STRING comment '可用区名称',
    site INT comment '站点',
    site_cn STRING comment '站点名称',
    event_id STRING comment '来自EB协议的事件ID',
    description_cn STRING comment '对外话术，中',
    description_en STRING comment '对外话术，英',
    confirm_owner STRING comment '信息确认人',
    release_owner STRING comment '发布审核人',
    is_exercise INT comment '是否演习',
    is_exercise_cn STRING comment '是否演习名称',
    event_status_decision STRING comment '事件判断逻辑',
    product_name STRING COMMENT '云产品中文名',
    parent_id INT COMMENT '父级事件主键ID',
    create_from INT COMMENT '创建来源：1|SMP自动建单；2|L1故障单页手动录单；3|SMP手动新增子Event；4|L1故障单页手动新增子Event',
    approved_at INT COMMENT '审批操作时间戳',
    abolish_reason STRING COMMENT '作废原因'
)ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");


CREATE TABLE dwd_problem_extra(
  problem_id BIGINT,
    monitor_alarm_situation INT,
    discovery_way INT,
    is_push_customer INT,
    is_introduce_without_test INT,
    is_leak_test INT,
    level_of_leak_test INT,
    category_of_leak_test STRING,
    reason_of_leak_test STRING,
    category_of_defect STRING,
    tester STRING,
    tester_comment STRING,
    failure_statement STRING,
    is_monitor_confirm INT,
    monitor_belong_to INT,
    monitor_improve_remark STRING,
    create_time datetime,
    update_time datetime,
    last_operator STRING,
    discovery_time datetime,
    duty_delimitation INT,
    is_break_change_rule INT,
    break_change_rules STRING,
    duty_group STRING,
    duty_group_display STRING,
    service_problem_type INT,
    responsibility_queue_id INT,
    responsibility_queue_name STRING,
    quality_problem_type_id INT,
    quality_problem_type_name STRING,
    review_tss_id STRING,
    duty_delimitation_pcloud_burst STRING,
    duty_group_complete STRING,
    change_duty_group STRING,
    change_duty_group_display STRING,
    change_duty_group_complete STRING,
    is_preview_env INT,
    is_customer_disaster_recovery INT,
    is_consultant_identify_dangers INT,
    is_breaking_prohibited_rules INT,
    breaking_standard_rules STRING
)ENGINE=OLAP
PRIMARY KEY(`problem_id`)
DISTRIBUTED BY HASH(`problem_id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");




CREATE TABLE dwd_problem_measure(
    measure_id BIGINT,
    problem_id BIGINT,
    content STRING,
    status INT,
    plan_finish_time datetime,
    actual_finish_time datetime,
    creator STRING,
    responsible STRING,
    create_time datetime,
    update_time datetime,
    last_operator STRING,
    tapd_bug_id STRING,
    tapd_bug_workspace_id BIGINT,
    is_transfer_tapd INT,
    measure_category INT,
    validate_method STRING,
    validate_person STRING,
    is_deleted INT,
    cancel_reason STRING,
    validate_time datetime,
    modify_finish_time_reason STRING,
    transfer_type INT,
    delay_reason STRING,
    package_type INT,
    is_launch_change INT,
    improvement_measure_qa STRING,
    measure_proof STRING
)ENGINE=OLAP
PRIMARY KEY(`measure_id`)
DISTRIBUTED BY HASH(`measure_id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");



CREATE TABLE dwd_problem_operation(
    operation_id BIGINT,
    problem_id BIGINT,
    content STRING,
    operator_type STRING,
    operation_type INT,
    field_type INT,
    options STRING,
    action INT,
    operate_time datetime,
    operator STRING,
    status INT,
    target_status INT,
    current_operator STRING,
    next_operator STRING
)ENGINE=OLAP
PRIMARY KEY(`operation_id`)
DISTRIBUTED BY HASH(`operation_id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");



CREATE TABLE dwd_problem_related_ticket(
    relation_id BIGINT,
    problem_id BIGINT,
    ticket_id BIGINT,
    customer_uin STRING,
    creator STRING,
    create_time datetime,
    update_time datetime,
    is_deleted INT,
    from_merge_problem_id BIGINT,
    relation_from INT
)ENGINE=OLAP
PRIMARY KEY(`relation_id`)
DISTRIBUTED BY HASH(`relation_id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");




CREATE TABLE dwd_problem_operation_field(
    id BIGINT,
    operation_id BIGINT,
    field_name_en STRING,
    field_name_zh STRING,
    value_before STRING,
    value_after STRING,
    create_time datetime
)ENGINE=OLAP
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");



CREATE TABLE dwd_burst_effect_assessment(
    id INT,
    problem_id BIGINT,
    affected_business INT,
    affected_module INT,
    affected_minutes INT,
    responsible_business INT,
    responsible_module INT,
    responsible_minutes INT,
    business_display STRING,
    description STRING,
    is_deleted INT,
    creator STRING,
    updator STRING,
    create_time datetime,
    update_time datetime,
    affected_time STRING,
    responsible_time STRING
)ENGINE=OLAP
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");


CREATE TABLE dwd_burst_timeline(
    id BIGINT,
    problem_id BIGINT,
    first_burst_time datetime,
    third_burst_time datetime,
    first_transfer_special_time datetime,
    first_transfer_operation_or_research_time datetime,
    burst_create_time datetime,
    monitor_time datetime,
    recover_time datetime,
    is_auto INT,
    create_time datetime,
    modify_time datetime,
    modifier STRING
)ENGINE=OLAP
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");



CREATE TABLE dwd_burst_duty_split(
value_of_primary_key STRING COMMENT 'value_of_primary_key',
    record_update_time datetime COMMENT '记录更新时间',
    problem_id BIGINT COMMENT '新责任界定',
    duty_delimitation_pcloud_burst_value STRING COMMENT '责任团队',
    duty_group_display_value STRING COMMENT '确认项描述',
    duty_delimitation_pcloud_burst STRING COMMENT '新责任界定数组',
    duty_group_display STRING COMMENT '责任团队数组'
)ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");


