CREATE TABLE dwd_problem_info
(
    `problem_id`                          BIGINT  COMMENT '问题ID',
    `title`                               STRING COMMENT '标题',
    `description`                         STRING COMMENT '描述',
    `service_scene_id`                    INT COMMENT '服务场景ID',
    `service_scene_display`               STRING COMMENT '服务场景显示',
    `reason`                              STRING COMMENT '原因',
    `reason_category`                     BIGINT COMMENT '原因分类',
    `is_known_problem`                    INT COMMENT '是否已知问题',
    `responsibility_attribution`          INT COMMENT '责任归属',
    `temporary_solution`                  STRING COMMENT '临时解决方案',
    `temporary_solution_operator`         STRING COMMENT '临时解决方案操作人',
    `status`                              INT COMMENT '状态',
    `creator`                             STRING COMMENT '创建人',
    `current_operator`                    STRING COMMENT '当前操作人',
    `manager`                             STRING COMMENT '经理',
    `responsible`                         STRING COMMENT '负责人',
    `test_analysis_bug_id`                BIGINT COMMENT '测试分析Bug ID',
    `is_priority_modified`                INT COMMENT '是否修改优先级',
    `priority`                            INT COMMENT '优先级',
    `create_time`                         DATETIME COMMENT '创建时间',
    `close_time`                          DATETIME COMMENT '关闭时间',
    `update_time`                         DATETIME COMMENT '更新时间',
    `last_operator`                       STRING COMMENT '最后操作人',
    `first_should_assign`                 INT COMMENT '首次应分配',
    `first_fact_assign`                   INT COMMENT '首次实际分配',
    `should_assign`                       INT COMMENT '应分配',
    `fact_assign`                         INT COMMENT '实际分配',
    `problem_chat_group_id`               STRING COMMENT '问题聊天组ID',
    `feedback_source`                     INT COMMENT '反馈来源',
    `source`                              INT COMMENT '来源',
    `is_fault`                            INT COMMENT '是否故障',
    `priority_score`                      INT COMMENT '优先级评分',
    `history_burst_operator`              STRING COMMENT '历史爆发操作人',
    `is_tech_plan_wrong`                  INT COMMENT '技术方案是否错误',
    `is_no_test_related`                  INT COMMENT '是否与测试无关',
    `problem_delivery_mode`               INT COMMENT '问题交付方式',
    `project_stage`                       INT COMMENT '项目阶段',
    `delete_person`                       STRING COMMENT '删除人',
    `new_reason_category`                 STRING COMMENT '新原因分类',
    `reason_category_supplement`          STRING COMMENT '原因分类补充',
    `product_short_name`                  STRING COMMENT '产品简称',
    `child_product_name`                  STRING COMMENT '子产品名称',
    `discover_version`                    STRING COMMENT '发现版本',
    `is_private_sale_create`              INT COMMENT '是否私有销售创建',
    `incident_releate_tapd`               STRING COMMENT '关联的Incident TAPD',
    `incident_releate_workspace`          STRING COMMENT '关联的Incident Workspace',
    `problem_is_change_production_happen` INT COMMENT '问题是否变更产生',
    `problem_change_production_happen`    INT COMMENT '问题变更产生',
    `is_missed_test`                      INT COMMENT '是否遗漏测试',
    `is_common_problem`                   INT COMMENT '是否常见问题',
    `is_lone_before`                      INT COMMENT '是否之前独立',
    `is_lone_problem`                     INT COMMENT '是否独立问题',
    `reason_operator`                     STRING COMMENT '原因操作人',
    `reason_operator_time`                STRING COMMENT '原因操作人时间',
    `temporary_solution_operator_time`    STRING COMMENT '临时解决方案操作人时间',
    `no_private_cloud_type`               INT COMMENT '非私有云类型',
    `before_modif_priority`               INT COMMENT '修改前优先级',
    `priority_update_reason`              STRING COMMENT '优先级更新原因',
    `problem_change_production_href`      STRING COMMENT '问题变更产生链接',
    `is_affect_public_cloud`              INT COMMENT '是否影响公有云',
    `is_affect_private_cloud`             INT COMMENT '是否影响私有云',
    `is_public_cloud_problem_solved`      INT COMMENT '公有云问题是否解决',
    `is_private_cloud_problem_solved`     INT COMMENT '私有云问题是否解决'
) ENGINE=OLAP
PRIMARY KEY(`problem_id`)
DISTRIBUTED BY HASH(`problem_id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
