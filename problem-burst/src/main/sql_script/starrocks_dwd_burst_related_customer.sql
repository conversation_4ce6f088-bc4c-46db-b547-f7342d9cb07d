CREATE TABLE dwd_burst_related_customer
(
    `id`                         BIGINT                     COMMENT 'ID',
    `problem_id`                 DECIMAL(20, 0)             COMMENT '问题ID',
    `customer_uin`               STRING                     COMMENT '客户UIN',
    `customer_name`              STRING                     COMMENT '客户名称',
    `sales_supportor`            STRING                     COMMENT '销售支持人员',
    `bussiness_supportor`        STRING                     COMMENT '业务支持人员',
    `arch_supportor`             STRING                     COMMENT '架构支持人员',
    `is_big_customer`            INT                        COMMENT '是否大客户',
    `is_report`                  INT                        COMMENT '是否报备',
    `is_compensate`              INT                        COMMENT '是否赔付',
    `is_repair`                  INT                        COMMENT '是否修复',
    `abnormal_info`              STRING                     COMMENT '异常信息',
    `ticket_id`                  BIGINT                     COMMENT '票据ID',
    `group_name`                 STRING                     COMMENT '组名',
    `affect_desc`                STRING                     COMMENT '影响描述',
    `trade`                      STRING                     COMMENT '贸易',
    `grade`                      INT                        COMMENT '等级',
    `is_head`                    INT                        COMMENT '是否为负责人',
    `creator`                    STRING                     COMMENT '创建人',
    `last_operator`              STRING                     COMMENT '最后操作人',
    `created_at`                 DATETIME                   COMMENT '创建时间',
    `updated_at`                 DATETIME                   COMMENT '更新时间',
    `damage_type`                INT                        COMMENT '损坏类型',
    `compensate_amount`          DECIMAL(12, 2)             COMMENT '赔付金额',
    `compensate_way`             INT                        COMMENT '赔付方式',
    `customer_loss_incre`        STRING                     COMMENT '客户损失增量',
    `customer_loss_stock`        STRING                     COMMENT '客户损失存量',
    `nickname`                   STRING                     COMMENT '昵称',
    `ticket_create_time`         STRING                     COMMENT '票据创建时间',
    `is_deleted`                 INT                        COMMENT '是否已删除',
    `customer_feedback`          STRING                     COMMENT '客户反馈',
    `is_cut`                     INT                        COMMENT '是否切割',
    `influence_time`             STRING                     COMMENT '影响时间',
    `repair_way`                 STRING                     COMMENT '修复方式',
    `owner_group`                STRING                     COMMENT '负责组',
    `is_excite`                  INT                        COMMENT '是否激励',
    `is_appease`                 INT                        COMMENT '是否安抚',
    `fsm_id`                     STRING                     COMMENT 'FSM ID',
    `sales_supportor_leader`     STRING                     COMMENT '销售支持领导',
    `bussiness_supportor_leader` STRING                     COMMENT '业务支持领导',
    `arch_supportor_leader`      STRING                     COMMENT '架构支持领导',
    `is_sync_leader`             INT                        COMMENT '是否同步领导',
    `is_new_sync_leader`         INT                        COMMENT '是否新同步领导',
    `department`                 STRING                     COMMENT '部门',
    `service_channel`            INT                        COMMENT '服务渠道',
    `ltc_id`                     STRING                     COMMENT 'LTC ID'
) ENGINE=OLAP
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
