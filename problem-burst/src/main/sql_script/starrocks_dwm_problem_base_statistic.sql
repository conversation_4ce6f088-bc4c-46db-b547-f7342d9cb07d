CREATE TABLE dwm_problem_base_statistic
(
    `value_of_primary_key`                BIGINT,
    `record_update_time`                  DATETIME,
    `problem_id`                          BIGINT,
    `title`                               STRING,
    `description`                         STRING,
    `service_scene_display`               STRING,
    `reason`                              STRING,
    `reason_category`                     STRING,
    `is_known_problem`                    INT,
    `responsibility_attribution`          INT,
    `temporary_solution`                  STRING,
    `status`                              INT,
    `creator`                             STRING,
    `current_operator`                    STRING,
    `manager`                             STRING,
    `responsible`                         STRING,
    `create_time`                         DATETIME,
    `first_should_assign`                 INT,
    `first_fact_assign`                   INT,
    `should_assign`                       INT,
    `fact_assign`                         INT,
    `priority`                            INT,
    `is_priority_modified`                INT,
    `priority_score`                      INT,
    `problem_delivery_mode`               INT,
    `project_stage`                       INT,
    `reason_category_supplement`          STRING,
    `product_short_name`                  STRING,
    `child_product_name`                  STRING,
    `discover_version`                    STRING,
    `history_burst_operator`              STRING,
    `is_tech_plan_wrong`                  INT,
    `is_no_test_related`                  INT,
    `delete_person`                       STRING,
    `problem_change_production_happen`    INT,
    `problem_is_change_production_happen` INT,
    `is_fault`                            INT,
    `is_lone_problem`                     INT,
    `no_private_cloud_type`               INT,
    `is_affect_public_cloud`              INT,
    `is_affect_private_cloud`             INT,
    `is_public_cloud_problem_solved`      INT,
    `is_private_cloud_problem_solved`     INT,
    `problem_change_production_href`      STRING,
    `measures_amount`                     BIGINT,
    `measures_finished_on_time`           BIGINT,
    `measures_finished_not_on_time`       BIGINT,
    `measures_finished_plan_time`         DATETIME,
    `measures_improve_plan_time`          DATETIME,
    `related_ticket_id`                   STRING,
    `related_ticket_id_amount`            BIGINT,
    `monitor_alarm_situation`             INT,
    `discovery_way`                       INT,
    `is_push_customer`                    INT,
    `is_introduce_without_test`           INT,
    `is_leak_test`                        INT,
    `level_of_leak_test`                  INT,
    `category_of_leak_test`               STRING,
    `reason_of_leak_test`                 STRING,
    `category_of_defect`                  STRING,
    `tester`                              STRING,
    `tester_comment`                      STRING,
    `is_monitor_confirm`                  INT,
    `monitor_belong_to`                   INT,
    `monitor_improve_remark`              STRING,
    `duty_group`                          STRING,
    `duty_group_display`                  STRING,
    `change_duty_group`                   STRING,
    `change_duty_group_display`           STRING,
    `is_preview_env`                      INT,
    `is_customer_disaster_recovery`       INT,
    `is_consultant_identify_dangers`      INT,
    `is_break_change_rule`                INT,
    `break_change_rules`                  STRING,
    `is_breaking_prohibited_rules`        INT,
    `breaking_standard_rules`             STRING,
    reason_category_name string,
    `first_processing_time` DATETIME,
    `first_approve_time` DATETIME,
    `first_confirm_close_time` DATETIME,
    `close_time` DATETIME,
    `first_deal_time` DATETIME,
    `first_approver` string ,
    `processing_duration`  bigint ,
    `resolved_duration`  bigint ,
    second_third_line_handler string ,
    first_improve_time DATETIME ,
    `check_duration`  bigint ,
    `deny_duration`  bigint ,
    `handle_duration`  bigint ,
    `second_third_line_deny_duration`  bigint ,
    `manager_confirm_close_duration`  bigint ,
    `manager_confirm_deny_duration`  bigint ,
    `problem_deal_duration`  bigint ,
    `is_approved_by_leader`  bigint ,
    `approved_duration_by_leader`  bigint ,
    `rejected_duration_by_leader`  bigint ,
    `manager_duration`  bigint ,
    `operator_duration`  bigint ,
    `processing_solved_duration`  bigint ,
    manager_deny_num bigint ,
    second_third_line_deny_num bigint ,
    manager_deny_close_num bigint ,
    fact_reopen_num bigint ,
    reopen_num bigint ,
    final_handler string ,
    final_approver string ,
    is_manager_check bigint ,
    is_second_third_line_handle bigint ,
    is_manager_confirm_situation bigint ,
    improve_duration  bigint,
    first_inflow_approving_time  DATETIME,
    processing_max_duration_operator string,
    creator_role string,
    current_operator_role string,
    manager_role string,
    responsible_role string
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
