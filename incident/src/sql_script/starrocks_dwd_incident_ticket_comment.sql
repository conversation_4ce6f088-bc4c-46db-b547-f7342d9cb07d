CREATE TABLE dwd_incident_ticket_comment
(

    `id`                                          BIGINT            COMMENT '主键id',
    `ticket_id`                                   BIGINT            COMMENT '工单ID',
    `role`                                        BIGINT            COMMENT '角色：1|投诉队列;2|值班经理;3|服务监理;4|其他',
    `risk_level`                                  STRING            COMMENT '风险级别：SS|S|A|B',
    `risk_type`                                   BIGINT            COMMENT '风险类型：1|产品问题;2|服务问题;3|其他;4|安全入侵类;5|违规封禁;6|非腾讯云业务',
    `risk_subtype`                                BIGINT            COMMENT '风险子类：1|产品规则流程;2|产品稳定性;3|产品功能易用性;4|服务专业性;5|服务态度;6|其他;7|数据恢复;8|入侵索赔;9|甲方通报;10|内部发现-云鼎;11|内部发现-信安;12|内部发现-合规;13|非腾讯云业务',
    `claim_compensation`                          DECIMAL(12,2)     COMMENT '客户索赔金额',
    `actual_compensation`                         DECIMAL(12,2)     COMMENT '客户索赔金额',
    `actual_compensation_type`                    BIGINT            COMMENT '实际赔付类型：1|调账;2|退款;3|通用代金券;4|满减代金券;5|赠送金;6|现金',
    `treat_mode`                                  BIGINT            COMMENT '处置方式：1|电话安抚;2|文字安抚',
    `customer_feedback`                           BIGINT            COMMENT '客户情况：1|接受;2|不接受;3|客情升级',
    `upgrade_to`                                  BIGINT            COMMENT '升级情况：1|报备leader;2|报备/升级投诉团队;3|升级专项;4|无升级动作',
    `key_scene`                                   BIGINT            COMMENT '重点场景：1|商业化（计费变更/涨价）;2|产品重大变更;3|重大运营活动;4|产品(功能)上下线;5|政策合规类事件',
    `detail`                                      STRING            COMMENT '补充详细内容',
    `creator`                                     STRING            COMMENT '创建人',
    `create_time`                                 DATETIME          COMMENT '创建时间',
    `is_sensitive_customer`                       BIGINT            COMMENT '是否敏感客户 0-否，1-是',
    `sensitive_type`                              BIGINT            COMMENT '敏感客户类型 1、媒体背景2、政府背景 3、法律专业人士 4、高频舆情客户 5、高频投诉客户 6、大额索赔客户 7、弱势群体 8、其他'
) ENGINE=OLAP
PRIMARY KEY(`id`, `ticket_id`)
COMMENT "DWD层工单评论表"
DISTRIBUTED BY HASH(`ticket_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "ticket_id"
);


-- add column by julioguo 2022-08-29
ALTER TABLE dwd_incident_ticket_comment ADD COLUMN (
    `customer_demand`                           BIGINT              COMMENT '用户诉求',
	`is_complaint`                              BIGINT              COMMENT '是否为投诉单 0-否，1-是',
	`complaint_type`                            BIGINT              COMMENT '投诉单类型',
	`complaint_risk_type`                       BIGINT              COMMENT '投诉风险类型',
	`is_compensation`                           BIGINT              COMMENT '是否提及赔付 0-否，1-是',
	`current_progress`                          BIGINT              COMMENT '当前进展'
);


-- add column by julioguo 2023-11-07
ALTER TABLE dwd_incident_ticket_comment ADD COLUMN (
    `measure_type`                    BIGINT COMMENT '措施类型: 1-首单主动服务、2-忠诚客户TSA、3-2次交互未明确外呼、4-外呼确认结单、5-资源到期提醒、6-差评回访修复',
    `customer_sentiment`              STRING COMMENT '客户情绪',
    `has_other_issues`                STRING COMMENT '是否有其他问题',
    `has_notify_service_mode`         STRING COMMENT '服务模式告知',
    `satisfaction_survey`             STRING COMMENT '腾讯云满意度调研',
    `contact_method`                  STRING COMMENT '触达方式',
    `has_product_introduction`        STRING COMMENT '云顾问产品是否介绍',
    `phone_connection`                STRING COMMENT '电话接通',
    `customer_issue`                  STRING COMMENT '客户问题',
    `has_send_sms`                    STRING COMMENT '是否发短信',
    `has_suggest_requirement`         STRING COMMENT '建议并转需求',
    `follow_conclusion`               STRING COMMENT '回访结论',
    `repair_result`                   STRING COMMENT '客情修复结果',
    `has_gift_voucher`                STRING COMMENT '赠送代金券',
    `voucher_amount`                  STRING COMMENT '赠送代金券金额',
    `voucher_status`                  STRING COMMENT '代金券发送状态'
);
