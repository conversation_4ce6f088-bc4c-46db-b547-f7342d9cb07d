CREATE TABLE dim_t_category
(
    `dim_id`                BIGINT COMMENT 'dim_id',
    `level1_category_id`    BIGINT COMMENT '官网一级标签ID',
    `level2_category_id`    BIGINT COMMENT '官网二级标签ID',
    `level3_category_id`    BIGINT COMMENT '官网三级标签ID',
    `level1_category_name`  STRING COMMENT '官网一级标签',
    `level2_category_name`  STRING COMMENT '官网二级标签',
    `level3_category_name`  STRING COMMENT '官网三级标签',
    `status`                BIGINT COMMENT '状态：0-有效，-1-无效'
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "官网分类维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);
