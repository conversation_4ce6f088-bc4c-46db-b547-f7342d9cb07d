CREATE TABLE dim_incident_ticket_unsatisfy_reason
(
  dim_id                         BIGINT            COMMENT '工单不满意id',
  unsatisfy_reason               STRING            COMMENT '工单不满意英文',
  extern_status_chin             STRING            COMMENT '工单不满意中文'
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "工单不满意原因维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);

INSERT INTO dim_incident_ticket_unsatisfy_reason(dim_id, unsatisfy_reason, extern_status_chin) VALUES
(0, 'NOT_EVALUATED', '未评价'),
(1, 'SERVICE_ATTITUDE', '服务态度差'),
(2, 'TECHNICAL_CAPABILITY', '技术能力弱'),
(3, 'RESOLVE_TIMELINESS', '解决时效差'),
(4, 'UNREASONABLE_PLANNING', '规则不合理'),
(5, 'INSUFFICIENT_PRODUCT', '产品功能不足'),
(6, 'PRODUCT_STABILITY', '产品稳定性差'),
(7, 'OVERALL_SATISFACTION', '整体比较满意'),
(8, 'MIGRATION_SOLUTION', '迁云方案差'),
(9, 'PRESALES_TRAINING', '售前培训差'),
(10, 'EXCHANGE_VISITS', '交流拜访差');
