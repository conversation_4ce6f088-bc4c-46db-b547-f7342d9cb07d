CREATE TABLE ods_s360_customer_dynamic
(
    `value_of_primary_key` STRING COMMENT '唯一主键',
    `record_update_time`   STRING COMMENT '时间',
    `user`                 STRING COMMENT '点击人',
    `id`                   STRING COMMENT 'id',
    `name`                 STRING COMMENT '名称',
    `url`                  STRING COMMENT '链接',
    `type`                 STRING COMMENT '类型',
    `reason`               STRING COMMENT '原因',
    `pos`                  STRING COMMENT '点击位置',
    `content`              STRING COMMENT '内容',
    `device`               STRING COMMENT '设备',
    `module`               STRING COMMENT '模块',
    `function`             STRING COMMENT '功能',
    `source`               STRING COMMENT '来源',
    `event_id`             STRING COMMENT 'event_id',
    `page`                 STRING COMMENT '页面',
    `org`                  STRING COMMENT '组织架构',
    `level`                STRING COMMENT '等级'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
COMMENT "360客户动态表"
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "value_of_primary_key"
);
