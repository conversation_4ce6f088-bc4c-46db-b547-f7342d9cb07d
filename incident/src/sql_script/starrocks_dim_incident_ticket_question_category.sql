CREATE TABLE dim_incident_ticket_question_category
(
  dim_id                           BIGINT            COMMENT '问题分类id',
  question_category                STRING            COMMENT '问题分类英文',
  question_category_chin           STRING            COMMENT '问题分类中文'
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "工单问题分类维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);


INSERT INTO dim_incident_ticket_question_category(dim_id, question_category, question_category_chin) VALUES
(1, 'FAULT', '问题排查类'),
(2, 'STORY', '客户需求类'),
(3, 'CONSULTATION', '客户咨询类'),
(4, 'SERVICE', '主动服务类'),
(5, 'INFRASTRUCTURE', '基础设施问题'),
(6, 'DEPLOYMENT', '规划配置问题'),
(7, 'TOOL', '部署工具问题'),
(8, 'OPERATION', '部署操作问题'),
(9, 'EXPERIENCE', '产品需求'),
(10, 'BUG', '产品Bug'),
(11, 'DOCUMENTATION', '资料文档问题'),
(12, 'BUSINESS_APPLICATION', '二次开发问题'),
(13, 'PLATFORM_BUSINESS', '平台业务规划'),
(14, 'USER_OPERATE', '用户操作报障'),
(15, 'INFRASTRUCTURE_PROBLEM', '基础设施异常'),
(16, 'DEPLOYMENT_PLAN', '部署规划问题'),
(17, 'DATA_DOCUMENT', '资料文档问题'),
(18, 'SERVICE_SOFTWARE', '服务软件故障'),
(19, 'CUSTOMER_REQUIREMENT_CHANGE', '客户服务请求'),
(20, 'USAGE_CONSULTATION', '使用问题咨询'),
(21, 'CUSTOMER_REQUIREMENT', '客户需求'),
(22, 'TO_BE_CONFIRMED', '待确认'),
(23, 'CUSTOMER_SELF_PROBLEM', '客户自身问题'),
(24, 'PRODUCT_CONSULTATION', '产品咨询'),
(25, 'AFTER_SALES_INITIATIVE', '巡检发现异常'),
(26, 'PRODUCT_FUNCTION_BUG', '产品Bug'),
(27, 'PRODUCT_INSTALL_BUG', '产品安装部署bug'),
(28, 'UPGRADE_CHANGE_ISSUES', '升级变更问题'),
(29, 'SAFETY_PROBLEM', '安全问题'),
(30, 'PERFORMANCE_ISSUES', '性能问题'),
(31, 'PRODUCT_ABNORMAL', '产品出现异常'),
(32, 'USAGE_METHOD_CONSULTATION', '使用方法咨询'),
(33, 'PRODUCT_TECH_CONSULTATION', '产品技术咨询'),
(34, 'ALTER_ABNORMAL', '变更出现异常'),
(35, 'THIRD_PARTY_ISSUES', '第三方软件问题'),
(36, 'RELEASE_OPERATION_ISSUES', '[运营]发布变更操作错误'),
(37, 'INCIDENTDESC', '事件描述类'),
(38, 'BUSINESS_PROBlEM', '业务出现异常'),
(39, 'QUESTION_CHECK', '问题排查类');
