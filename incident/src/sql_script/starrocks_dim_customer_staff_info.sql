CREATE TABLE dim_customer_staff_info
(
    `uid`                       STRING COMMENT  'uid',
    `user_id`                   STRING COMMENT  'user_id',
    `user_name`                 STRING COMMENT  '员工中文名',
    `company_id`                BIGINT COMMENT  '公司ID',
    `group_id`                  BIGINT COMMENT  'group_id',
    `assign_company_id`         BIGINT COMMENT  '派单对应的供应商'
) ENGINE=OLAP
PRIMARY KEY(`uid`)
COMMENT "清洗后的staff纬表"
DISTRIBUTED BY HASH(`uid`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "uid"
);
