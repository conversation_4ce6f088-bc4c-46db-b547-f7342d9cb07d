CREATE TABLE dwm_incident_ticket_operator_duration_info
(
  `value_of_primary_key`                    STRING       COMMENT '主键',
  `create_time`                             DATETIME     COMMENT '工单创建时间',
  `ticket_id`                               BIGINT       COMMENT '工单ID',
  `operator`                                STRING       COMMENT '工单操作人',
  `post`                                    INT          COMMENT '操作人岗位',
  `duration`                                BIGINT       COMMENT '操作人处理时长(秒)',
  `service_channel`                         BIGINT       COMMENT '服务通道',
  `service_scene`                           BIGINT       COMMENT '客户/客服提交的服务场景',
  `service_scene_checked`                   BIGINT       COMMENT '客服修改后的服务场景',
  `priority`                                BIGINT       COMMENT '工单优先级：1|非常紧急,2|紧急,3|一般',
  `close_time`                              DATETIME     COMMENT '工单结单时间',
  `ticket_solved_time`                      DATETIME     COMMENT '问题解决时间',
  `in_out_time_list`                        STRING       COMMENT '工单经手人进场/出场时间',
  `interval_duration_list`                  STRING       COMMENT '工单经手人每次处理时长(秒)'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`, `create_time`)
COMMENT "DWM层工单经手人时长信息"
PARTITION BY RANGE (`create_time`) (
    START ('2016-01-01') END ('2030-01-01') EVERY (INTERVAL 1 MONTH)
)
DISTRIBUTED BY HASH(`value_of_primary_key`, `create_time`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "enable_persistent_index" = "true",
    "dynamic_partition.enable" = "true",
    "dynamic_partition.buckets" = "8",
    "dynamic_partition.time_unit" = "MONTH",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.history_partition_num" = "0",
    "bloom_filter_columns" = "value_of_primary_key"
);
