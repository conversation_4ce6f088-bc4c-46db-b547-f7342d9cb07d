CREATE TABLE dwm_incident_ticket_statistic
(
    `ticket_id`                                           BIGINT       DEFAULT '0' COMMENT '工单ID,起始值1000001,旧工单系统的工单ID小于1000000',
    `create_time`                                         DATETIME     COMMENT '工单创建时间',
    `uin`                                                 BIGINT       COMMENT '提交工单的客户uin',
    `related_region_code`                                 STRING       COMMENT '关联该工单的手机号地区代码',
    `related_phone_number`                                STRING       COMMENT '关联该工单的手机号加密',
    `service_channel`                                     BIGINT       COMMENT '服务通道：1|IMCC,2|CallCenter,3|MC,4|腾讯云小助手,5|邮件,6|备勤手机,7|QQ群,8|外呼,9|官网论坛,10|回访,11|舆情监控,12|投诉/建议,13|不满意,14|duty(工单告警),15|星云CBS, 27|KA',
    `service_scene`                                       BIGINT       COMMENT '客户/客服提交的服务场景',
    `service_scene_checked`                               BIGINT       COMMENT '客服修改后的服务场景,当客户提交的服务场景不合适时使用,初始与service_scene一致',
    `archive_type`                                        BIGINT       COMMENT '归档类型：1|故障,2|产品建议,3|运维需求,4|产品咨询,5|技术咨询,6|购买需求,7|投诉,8|表扬,9|举报',
    `priority`                                            BIGINT       COMMENT '工单优先级：1|非常紧急,2|紧急,3|一般',
    `creator`                                             STRING       COMMENT '工单创建人',
    `current_operator`                                    STRING       COMMENT '当前处理人',
    `last_operator`                                       STRING       COMMENT '上一个处理人',
    `last_staff`                                          STRING       COMMENT '上一个处理该工单的客服（这里其实是最近处理工单的客服）',
    `last_operate_time`                                   DATETIME     COMMENT '上一次处理时间',
    `question`                                            STRING       COMMENT '客户问题内容',
    `last_inner_reply`                                    STRING       COMMENT '内部最近回复内容',
    `status`                                              BIGINT       COMMENT '工单状态：1|一线待处理,2|一线处理中,3|已结单,4|待客户补充,5|待确认结单,6|二线待处理,7|转运维,8|二线处理中,9|运维处理中,10|已撤销,11|用户已删除,12|Oracle处理中',
    `operation_ticket_id`                                 STRING       COMMENT '星云工单ID，新版tcs系统已经不会再用到，后续可以删掉',
    `responsible`                                         STRING       COMMENT '总负责人：服务侧最后一次处理人',
    `first_line_responsible`                              STRING       COMMENT '一线负责人：MC工单指一线首次外部回复的人,若没有外部回复(如转单),则为空;投诉单/不满意单为首次派单的人;400的单转"客服或技服"时是派单的人;其他通道是建单人',
    `second_line_responsible`                             STRING       COMMENT '二线负责人：MC工单指二线首次外部回复的人,若没有外部回复(如转单),则为空;非MC指一线转二线的第一个接收人,不管有没有处理',
    `solve_status`                                        BIGINT       COMMENT '官网工单解决状态：-1|未评价,1|已解决,2|未解决',
    `service_rate`                                        BIGINT       COMMENT '官网工单星级评价：0|未评价,1|一星,2|二星,3|三星,4|四星,5|五星',
    `satisfaction`                                        BIGINT       COMMENT '官网工单满意度：0|未评价,1|非常满意,2|满意,3|一般,4|不满意',
    `unsatisfy_reason`                                    BIGINT       COMMENT '官网工单不满意原因：0|未评价,1|产品功能,2|产品稳定性,3|服务态度,4|服务专业性,5|工单处理效率',
    `first_should_assign`                                 BIGINT       COMMENT '记录首次应派队列',
    `first_fact_assign`                                   BIGINT       COMMENT '记录首次实派队列',
    `first_fact_assign_time`                              DATETIME     COMMENT '首次实派队列时间',
    `should_assign`                                       BIGINT       COMMENT '当前应派队列',
    `fact_assign`                                         BIGINT       COMMENT '当前实派队列',
    `fact_assign_time`                                    DATETIME     COMMENT '最近一次实派队列时间',
    `unsatisfy_ticket_id`                                 BIGINT       COMMENT '不满意的工单ID(本工单是“不满意单”)',
    `related_unsatisfied_ticket_id`                       BIGINT       COMMENT '关联的“不满意单”',
    `related_qcloud_ticket_id`                            STRING       COMMENT '“不满意单”关联的官网工单ID',
    `qcloud_ticket_id`                                    STRING       COMMENT '官网工单ID',
    `qcloud_category_id`                                  BIGINT       COMMENT '官网工单分类',
    `qcloud_receive_notice_flag`                          BIGINT       COMMENT 'MC前端设置是否接收通知的标识位,在MC工单状态发生变化时通知用户(微信和短信),1|7-18点,2|一直接收,3|从不接收',
    `instance_id`                                         STRING       COMMENT '实例ID,一个或多个,分号分隔',
    `woodpecker_jobid`                                    STRING       COMMENT '调用啄木鸟返回的任务id',
    `appraise`                                            STRING       COMMENT '评价内容',
    `appraise_time`                                       DATETIME     COMMENT '评价时间',
    `next_follow_time`                                    DATETIME     COMMENT '下次跟进时间',
    `qcloud_complaint_id`                                 BIGINT       COMMENT '官网的投诉单ID',
    `complainted_ticket_id`                               BIGINT       COMMENT '被投诉的工单ID',
    `language`                                            BIGINT       COMMENT '工单语言类型：1|中文,2|英文',
    `callcenter_answer_time`                              DATETIME     COMMENT '400来电接听时间',
    `callcenter_hand_up_time`                             DATETIME     COMMENT '400来电挂机时间',
    `callcenter_talk_duration`                            BIGINT       COMMENT '400来电通话时长',
    `callcenter_queue_duration`                           BIGINT       COMMENT '400来电排队时长',
    `callcenter_session_id`                               STRING       COMMENT '400来电会话编号',
    `callcenter_agent_id`                                 STRING       COMMENT 'callcenter客服id',
    `callcenter_agent_name`                               STRING       COMMENT 'callcenter客服名称',
    `callcenter_session_filename`                         STRING       COMMENT '400来电会话录音文件名',
    `qqgroup_start_time`                                  DATETIME     COMMENT 'QQ群开始时间',
    `qqgroup_end_time`                                    DATETIME     COMMENT 'QQ群结束时间',
    `qqgroup_response_time`                               DATETIME     COMMENT 'QQ群响应时间',
    `qqgroup_response_duration`                           BIGINT       COMMENT 'QQ群响应时长,响应时间-开始时间',
    `qqgroup_number`                                      BIGINT       COMMENT 'QQ群号',
    `qqgroup_responsable`                                 STRING       COMMENT 'QQ群责任人',
    `qqgroup_big_customer_operator`                       STRING       COMMENT 'QQ群大客户处理人',
    `secret_content`                                      STRING       COMMENT '敏感信息字段',
    `update_time`                                         DATETIME     COMMENT '表最近更新时间',
    `qcloud_last_customer_reply_time`                     DATETIME     COMMENT '客户最近回复时间',
    `vip_ask_callback_id`                                 BIGINT       COMMENT 'VIP要求回访 mc_ticket_backend 记录ID',
    `callback_time_start`                                 DATETIME     COMMENT '回访时间起始',
    `callback_time_end`                                   DATETIME     COMMENT '回访时间截止',
    `owner_uin`                                           BIGINT       COMMENT '创建者QQ',
    `is_deleted`                                          BIGINT       COMMENT '用户删除工单：1是|0否',
    `is_ever_to_wan`                                      BIGINT       COMMENT '是否转过外网：1是|0否',
    `company_id`                                          BIGINT       COMMENT '当前工单所在的公司ID',
    `is_claim`                                            BIGINT       COMMENT '是否索赔',
    `is_fault_report`                                     BIGINT       COMMENT '是否需要故障报告',
    `is_internal_go_up`                                   BIGINT       COMMENT '是否需要内部升级',
    `post`                                                BIGINT       COMMENT '当前岗位',
    `next_up_responsor`                                   STRING       COMMENT '下一个上升人',
    `next_up_time`                                        DATETIME     COMMENT '下一次上升时间',
    `cc_person`                                           STRING       COMMENT '抄送人',
    `operation_service_scene`                             STRING       COMMENT '转运维的服务场景',
    `responsible_should_assign`                           STRING       COMMENT '转总负责人的队列，（总负责人的应派队列，应派队列必须24小时有人，否则需要有兜底功能的实派队列）',
    `chat_group_id`                                       STRING       COMMENT '关联一键拉群的群号',
    `first_assign_company_id`                             BIGINT       COMMENT '第一次工单派单所属公司ID',
    `feedback_channel`                                    BIGINT       COMMENT '反馈渠道:1(QQ群)|2(Slack群)|3(企业微信群)|4(微信群)|5(其他)',
    `group_id`                                            STRING       COMMENT '群号(一个uin可能有多个群)',
    `source_channel`                                      BIGINT       COMMENT '建单来源:0(TSS)|1(QQ群)|2(Slack群)|3(企业微信)',
    `customer_contact_info`                               STRING       COMMENT '客户联系人qq号,多个qq要用;分开',
    `question_category`                                   BIGINT       COMMENT '问题分类:1(客户报障类)|2(客户需求类)|3(客户咨询类)|4(主动服务类)',
    `reason_category`                                     BIGINT       COMMENT '原因分类:1(我方产品或服务的原因)|2(客户自身原因如姿势不当等)|3(两方皆有责任皆可做优化)|4(第三方原因造成(如运营商))|5(未确定原因)',
    `question_start_time`                                 DATETIME     COMMENT '问题发生时间',
    `question_end_time`                                   DATETIME     COMMENT '问题结束时间',
    `title`                                               STRING       COMMENT '问题标题',
    `reason`                                              STRING       COMMENT '原因分析',
    `upgrade_time`                                        DATETIME     COMMENT '升级时间',
    `sales_supportor`                                     STRING       COMMENT '售后支持',
    `trade`                                               STRING       COMMENT '行业',
    `name`                                                STRING       COMMENT '客户名称',
    `event_tss_id`                                        BIGINT       COMMENT 'tss_id',
    `add_info_application_post`                           BIGINT       COMMENT '申请补充时岗位',
    `add_info_application_should_assign`                  BIGINT       COMMENT '申请补充时应派队列',
    `add_info_application_operator`                       STRING       COMMENT '申请补充人',
    `tapd_story_id`                                       STRING       COMMENT 'tapd需求单id',
    `callcenter_skill_group`                              STRING       COMMENT 'callcenter技能组',
    `keyissues_id`                                        BIGINT       COMMENT '重点问题id',
    `complaint`                                           BIGINT       COMMENT '投诉',
    `complaint_content`                                   STRING       COMMENT '投诉内容',
    `ltc_name`                                            STRING       COMMENT '项目名',
    `product_version`                                     STRING       COMMENT '发现版本',
    `severity`                                            STRING       COMMENT '严重程度',
    `short`                                               STRING       COMMENT '产品简称',
    `extern_status`                                       BIGINT       COMMENT '外部状态',
    `caller`                                              STRING       COMMENT '建单调用方',
    `affected_customers`                                  STRING       COMMENT '受影响客户',
    `customer_uid`                                        STRING       COMMENT '非腾讯云客户唯一标识',
    `incident_manager`                                    STRING       COMMENT '事件经理',
    `incident_manager_should_assign`                      BIGINT       COMMENT '事件经理应派队列',
    `risk_control_first_time`                             DATETIME     COMMENT '首次触发风控派单时间',
    `risk_control_num`                                    BIGINT       COMMENT '风控推送次数',
    `is_incident_manager_into`                            BIGINT       COMMENT '事件经理是否查看工单详情 1-已查看 ，2-未查看',
--     `order_id`                                            STRING       COMMENT '订单编号',
--     `type`                                                STRING       COMMENT '工单类型',
--     `service_type`                                        STRING       COMMENT '服务子类',
--     `ltc_id`                                              STRING       COMMENT 'LTC编码',
--     `service_stage`                                       STRING       COMMENT '服务大类',
--     `product_name`                                        STRING       COMMENT '产品名称',
--     `service_mode`                                        BIGINT       COMMENT '服务模式:1(交付中心)|2(非交付中心)',
--     `region`                                              STRING       COMMENT '区域',
--     `project_stage`                                       BIGINT       COMMENT '项目阶段:1(交付阶段)|2(运维阶段)',
--     `project_id`                                          BIGINT       COMMENT '私有化售后QQ群项目ID',
--     `judian_name`                                         STRING       COMMENT '局点名称',
--     `jid`                                                 STRING       COMMENT '局点id',
--     `judian_status`                                       BIGINT       COMMENT '局点状态(-1:默认,0:交付阶段,1:转维中,2:售后阶段,3:已过保,4:已裁撤)',
--     `industry`                                            STRING       COMMENT '所属行业',
    `url`                                                 STRING       COMMENT '工单长链接',
    `short_url`                                           STRING       COMMENT '工单短链接',
    `operators`                                           STRING       COMMENT '工单经手人',
    `close_time`                                          DATETIME     COMMENT '工单结单时间',
    `close_type`                                          BIGINT       COMMENT '工单结单类型',
    `reply_times`                                         BIGINT       COMMENT '合计对外回复次数',
    `is_reported`                                         BIGINT       COMMENT '是否报备过',
    `last_reporter`                                       STRING       COMMENT '最近一次报备人',
    `transfer_times`                                      BIGINT       COMMENT '工单转单次数',
    `close_operator`                                      STRING       COMMENT '工单结单人',
    `interaction_times`                                   BIGINT       COMMENT '工单交互次数',
    `customer_reply_times`                                BIGINT       COMMENT '用户回复次数',
    `first_line_reply_times`                              BIGINT       COMMENT '一线对外回复次数',
    `first_response_stuff`                                STRING       COMMENT '工单首次响应人',
    `second_line_reply_times`                             BIGINT       COMMENT '1.5线对外回复次数',
    `agent_reply_times`                                   BIGINT       COMMENT '供应商对外回复次数',
    `last_reported_time`                                  DATETIME     COMMENT '最近一次报备时间',
    `ticket_solved_time`                                  DATETIME     COMMENT '问题解决时间',
    `inner_creator`                                       STRING       COMMENT '内部建单人',
    `request_source`                                      STRING       COMMENT '建单来源: MC-官网控制台 API-API接口 MP-小程序',
    `is_inner_created`                                    BIGINT       COMMENT '是否内部建单: 1-是 0-否',
    `is_complaint`                                        BIGINT       COMMENT '是否投诉单',
    `is_satisfy`                                          STRING       COMMENT '是否满意',
    `first_response_queue`                                BIGINT       COMMENT '首次响应队列',
    `operation_deny_reason`                               STRING       COMMENT '运维拒绝原因',
    `second_line_deny_reason`                             STRING       COMMENT '1.5线拒绝原因',
    `backend_first_deny_operator`                         STRING       COMMENT '后端首次拒单人',
    `product_research_deny_reason`                        STRING       COMMENT '产研拒绝原因',
    `vertical_product_research_deny_reason`               STRING       COMMENT '垂直产研拒绝原因',
    `agent_duration`                                      BIGINT       COMMENT '供应商处理时长(秒)',
    `unknown_duration`                                    BIGINT       COMMENT '其他角色处理时长(秒)',
    `customer_duration`                                   BIGINT       COMMENT '客户停留总时长(秒)',
    `operation_duration`                                  BIGINT       COMMENT '运维处理时长(秒)',
    `first_line_duration`                                 BIGINT       COMMENT '1线处理时长(秒)',
    `second_line_duration`                                BIGINT       COMMENT '1.5线处理时长(秒)',
    `ticket_deal_duration`                                BIGINT       COMMENT '工单总处理时长(秒)',
    `agent_customer_duration`                             BIGINT       COMMENT '客户在供应商停留时长(秒)',
    `product_research_duration`                           BIGINT       COMMENT '产研处理时长(秒)',
    `unknown_customer_duration`                           BIGINT       COMMENT '客户在其他停留时长(秒)',
    `first_line_customer_duration`                        BIGINT       COMMENT '客户在1线停留时长(秒)',
    `second_line_customer_duration`                       BIGINT       COMMENT '客户在1.5线停留时长(秒)',
    `vertical_product_research_duration`                  BIGINT       COMMENT '垂直产研处理时长(秒)',
    `agent_engineer_duration`                             BIGINT       COMMENT '供应商处理时长(秒)',
    `ticket_staff_deal_duration`                          BIGINT       COMMENT '客服处理工单总时长(秒)',
    `unknown_engineer_duration`                           BIGINT       COMMENT '其他角色处理时长(秒)',
    `first_line_engineer_duration`                        BIGINT       COMMENT '1线工程师处理时长(秒)',
    `second_line_engineer_duration`                       BIGINT       COMMENT '1.5线工程师处理时长(秒)',
    `is_transfer_first_line`                              BIGINT       COMMENT '是否过转1线',
    `is_denied_by_operation`                              BIGINT       COMMENT '是否被运维拒绝过',
    `is_denied_by_second_line`                            BIGINT       COMMENT '是否被1.5线拒绝过',
    `transfer_first_line_time`                            DATETIME     COMMENT '转1线时间',
    `transfer_first_line_times`                           BIGINT       COMMENT '转1线次数',
    `transfer_operation_times`                            BIGINT       COMMENT '转运维次数',
    `transfer_first_line_queue`                           BIGINT       COMMENT '转1线队列',
    `transfer_first_line_staff`                           STRING       COMMENT '转1线客服',
    `transfer_second_line_times`                          BIGINT       COMMENT '转1.5线次数',
    `transfer_type`                                       STRING       COMMENT '工单流转类型',
    `is_denied_by_production_research`                    BIGINT       COMMENT '是否被产研拒绝过',
    `transfer_production_research_times`                  BIGINT       COMMENT '转产研次数',
    `first_transfer_operation_staff_post`                 BIGINT       COMMENT '首次操作转运维的人的岗位',
    `first_transfer_operation_staff`                      STRING       COMMENT '首次操作转运维的人',
    `transfer_vertical_production_research_times`         BIGINT       COMMENT '转垂直产研次数',
    `first_transfer_production_research_staff_post`       BIGINT       COMMENT '首次操作转产研的人的岗位',
    `first_transfer_Vert_production_research_staff_post`  BIGINT       COMMENT '首次操作转垂直产研的人的岗位',
    `first_transfer_operation_time`                       DATETIME     COMMENT '首次操作转运维的时间',
    `first_second_line_service_time`                      DATETIME     COMMENT '1.5线首次服务时间',
    `first_second_line_operator`                          STRING       COMMENT '1.5线首次接单人',
    `current_second_line_operator`                        STRING       COMMENT '1.5线当前处理人',
    `first_transfer_second_line_staff`                    STRING       COMMENT '首次操作转1.5线的人',
    `first_second_line_assign`                            BIGINT       COMMENT '1.5线首次接单队列',
    `first_second_line_fact_assign`                       BIGINT       COMMENT '1.5线首次实派队列，对应旧数仓中的second_line_fact_assign',
    `second_line_solved_duration`                         BIGINT       COMMENT '1.5线解决问题时长',
    `first_second_line_response_duration`                 BIGINT       COMMENT '1.5线首次响应时长',
    `first_second_line_fact_operator`                     STRING       COMMENT '1.5线首次真实受理人',
    `first_transfer_complaint_queue_time`                 DATETIME     COMMENT '首次转投诉队列时间',
    `measures`                                            STRING        COMMENT '处理结果',
    `customer_waiting_impatience_duration`                BIGINT        COMMENT '用户等待的不耐烦时长(秒)',
    `last_operation_fact_assign`                          BIGINT       COMMENT '最新运维实派队列',
    `last_production_research_fact_assign`                BIGINT       COMMENT '最新产研实派队列',
    `second_line_operators`                               STRING       COMMENT '1.5线处理人列表',
    `current_second_line_fact_assign`                     STRING       COMMENT '当前1.5线实派队列',
    `first_transfer_production_research_time`             DATETIME     COMMENT '首次转转产研时间',
    `first_second_line_operator_company_id`               BIGINT       COMMENT '1.5线首次接单人所属公司',
    `current_second_line_operator_company_id`             BIGINT       COMMENT '1.5线当前处理人所属公司',
    `second_line_operator_company_ids`                    STRING       COMMENT '1.5线经手人所属公司',
    `is_transfer_company_in_second_line`                  BIGINT       COMMENT '1.5线是否转供应商',
    `is_transfer_inner_staff_in_second_line`              BIGINT       COMMENT '1.5线是否转正式员工',
    `second_line_last_refuser`                            STRING       COMMENT '1.5线最后拒单人',
    `last_customer_operator`                              STRING       COMMENT '最后一次客服处理人',
    `second_line_last_company_operator`                   STRING       COMMENT '1.5线供应商最终处理人'
  ) ENGINE=OLAP
PRIMARY KEY(`ticket_id`, `create_time`)
COMMENT "工单大宽表"
PARTITION BY RANGE (`create_time`) (
    START ('2016-01-01') END ('2030-01-01') EVERY (INTERVAL 1 MONTH)
)
DISTRIBUTED BY HASH(`ticket_id`, `create_time`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "enable_persistent_index" = "true",
    "dynamic_partition.enable" = "true",
    "dynamic_partition.buckets" = "8",
    "dynamic_partition.time_unit" = "MONTH",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.history_partition_num" = "0",
    "bloom_filter_columns" = "ticket_id"
);



 -- add column by julioguo 2023-08-29
ALTER TABLE dwm_incident_ticket_statistic ADD COLUMN (
    second_line_refuse_times                    BIGINT       COMMENT '被1.5线拒绝次数',
    to_be_confirm_close_times                   BIGINT       COMMENT '待客户确认结单次数',
    average_reply_duration                      BIGINT       COMMENT '平均回复时长(秒)',
    longest_reply_duration                      BIGINT       COMMENT '最长对外回复时间(秒)',
    longest_reply_staff                         STRING       COMMENT '最长回复时间对应的人',
    stuff_counts                                BIGINT       COMMENT '经手人数',
    customer_urge_times                         BIGINT       COMMENT '客户催单次数',
    connect_customer_times                      BIGINT       COMMENT '外呼次数',
    is_transfer_hotline_first_line              BIGINT       COMMENT '是否转热线一线 1-是；0-否',
    hotline_first_line_queue                    BIGINT       COMMENT '转热线1线队列',
    last_to_be_added_time                       DATETIME     COMMENT '最后一次操作“待客户补充”的时间'
);


-- add column by julioguo 2023-09-27
ALTER TABLE dwm_incident_ticket_statistic ADD COLUMN (
    current_first_line_operator                 STRING       COMMENT '1线当前（最后）处理人'
);

-- add column by julioguo 2023-10-19
ALTER TABLE dwm_incident_ticket_statistic ADD COLUMN (
    first_response_time                        STRING       COMMENT '客服首次响应时间',
    first_response_duration                    BIGINT       COMMENT '客服首次响应时长',
    hotline_first_transfer_deal_duration        BIGINT       COMMENT '热线首次转单后总处理时长',
    hotline_first_transfer_line_time            STRING       COMMENT '热线首次转单开始时间',
    hotline_first_transfer_response_time        STRING       COMMENT '热线首次转单后首次响应时间',
    hotline_first_transfer_response_duration    BIGINT       COMMENT '热线首次转单后总处理时长'
);


-- add column by julioguo 2023-11-11
ALTER TABLE dwm_incident_ticket_statistic ADD COLUMN (
    operation_handler                           STRING       COMMENT '运维处理人',
    operation_handler_company_id                BIGINT       COMMENT '运维处理人所属公司'
);
