CREATE TABLE dim_incident_ticket_operator_post
(
    dim_id                         BIGINT            COMMENT '工单操作人岗位id',
    operator_post_eng              STRING            COMMENT '工单操作人岗位英文',
    operator_post_chin             STRING            COMMENT '工单操作人岗位中文'
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "工单操作人岗位维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);



INSERT INTO dim_incident_ticket_operator_post(dim_id, operator_post_eng, operator_post_chin) VALUES
(0, 'UNKNOW', '其它'),
(1, 'SUPPLIER', '供应商'),
(2, 'FIRST_LINE', '一线'),
(3, 'SECOND_LINE', '1.5线'),
(4, 'OPERATION', '运维'),
(5, 'PRODUCTION_RESEARCH', '产研'),
(8, 'P_OPERATION', '售后运维'),
(9, 'P_TCE_PRODUCTION_RESEARCH', 'TCE产研'),
(10, 'P_TBDS_PRODUCTION_RESEARCH', 'TBDS产研'),
(11, 'P_VERTICAL_PRODUCTION_RESEARCH', '垂直产研'),
(12, 'P_TCE_ERXIAN', '二线');
