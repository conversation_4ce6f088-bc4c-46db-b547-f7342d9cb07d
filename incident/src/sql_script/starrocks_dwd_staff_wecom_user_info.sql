CREATE TABLE dwd_staff_wecom_user_info
(
     userid               STRING    COMMENT  '客服id'
    ,value_of_primary_key STRING    COMMENT  'uuid生成的主键'
    ,record_update_time   DATETIME  COMMENT  '数据上报时间'
    ,name                 STRING    COMMENT  '客服名字'
    ,department           STRING    COMMENT  '部门id'
    ,position             STRING    COMMENT  '岗位'
    ,status               INT       COMMENT  '状态'
    ,enable               INT       COMMENT  '是否有效'
    ,isleader             INT       COMMENT  '是否是leader'
    ,job_level            STRING    COMMENT  '职级'
    ,id_number            STRING    COMMENT  '证件号码'
    ,company              STRING    COMMENT  '所属公司'
    ,hire_date            DATETIME  COMMENT  '入职日期'
    ,mentor               STRING    COMMENT  '导师'
    ,remarks              STRING    COMMENT  '备注'
    ,tencent_account      STRING    COMMENT  '腾讯账号'
    ,qidian_account       STRING    COMMENT  '企点账号'
    ,vpn_access           STRING    COMMENT  'VPN权限'
    ,desktop_cloud_id     STRING    COMMENT  '桌面云号'
    ,mobile_qiwei         STRING    COMMENT  '手机企微'
    ,office_city          STRING    COMMENT  '办公城市'
    ,entry_time           DATETIME  COMMENT  '入场时间'
    ,exit_time            DATETIME  COMMENT  '离场时间'
    ,contract_property    STRING    COMMENT  '合同属性'
    ,business             STRING    COMMENT  '业务'
    ,resignation_date     DATETIME  COMMENT  '离职时间'
    ,dept_name            STRING    COMMENT  '部门名称'
    ,custom_fields        STRING    COMMENT  '自定义字段'
) ENGINE=OLAP
PRIMARY KEY(`userid`)
COMMENT "供应商客服员工企业信息"
DISTRIBUTED BY HASH(`userid`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "userid"
);


-- add column by julioguo 2023-11-11
ALTER TABLE dwd_staff_wecom_user_info ADD COLUMN (
    exit_reason                           STRING       COMMENT '离场原因'
);
