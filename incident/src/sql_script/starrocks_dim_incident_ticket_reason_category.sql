CREATE TABLE dim_incident_ticket_reason_category
(
  dim_id                         BIGINT            COMMENT '问题原因分类id',
  reason_category                STRING            COMMENT '问题原因分类英文',
  extern_status_chin             STRING            COMMENT '问题原因分类中文'
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "工单大客户问题原因分类维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);


INSERT INTO dim_incident_ticket_reason_category(dim_id, reason_category, extern_status_chin) VALUES
(1, 'OUR_SIDE', '我方产品或服务的原因'),
(2, 'CUSTOMER_SIDE', '客户自身原因如姿势不当等'),
(3, 'TWO_SIDE', '两方皆有责任皆可做优化'),
(4, 'THIRD_SIDE', '第三方原因造成(如运营商)'),
(5, 'UNCERTAINTY', '未确定原因'),
(6, 'BUG', '产品bug'),
(7, 'SAFE_HOLE', '安全问题'),
(8, 'PERFORMANCE_ISSUES', '性能问题'),
(9, 'CUSTOMER_REQUIREMENT', '产品需求'),
(10, 'DATA_DOCUMENT', '资料文档问题'),
(11, 'DEPLOYMENT_PLAN', '架构规划问题'),
(12, 'AFTER_SERVICE', '售后服务问题'),
(13, 'USER_OPERATE', '客户问题'),
(14, 'INFRASTRUCTURE_ISSUES', '基础设施问题'),
(15, 'SYSTEM_ISSUES', '操作系统问题'),
(16, 'THIRD_PARTY_ISSUES', '第三方软件问题'),
(17, 'OTHER', '其他'),
(18, 'PRODUCT_CONSULTATION', '产品咨询'),
(19, 'THIRD_OPERATION_ISSUES', '第三方运维问题'),
(20, 'RELEASE_OPERATION_ISSUES', '[运营]发布变更操作错误'),
(21, 'TOOL_MODULE_PROBLEM', '工具组件bug'),
(22, 'THIRD_HARDWARE_PROBLEM', '第三方硬件问题'),
(23, 'QCLOUD_OPERATE_PROBLEM', '误操作问题(腾讯)'),
(24, 'PRODUCT_USE_CONSULT', '产品使用咨询'),
(25, 'PRODUCT_TECH_CONSULT', '产品技术咨询'),
(26, 'PRODUCT_DEPOLY_CONSULT', '产品部署咨询'),
(27, 'PARTNER_OPERATE_PROBLEM', '合作伙伴操作问题'),
(28, 'PRODUCT_SALE_CONSULT', '产品售后咨询'),
(29, 'PARTNER_SKILL_PROBLEM', '合作伙伴技能问题'),
(30, 'OTHER_CONSULTATION', '其他咨询'),
(31, 'THIRD_OTHER_ISSUES', '第三方其他问题'),
(32, 'SERVICE_ISSUES', '产品或服务问题'),
(33, 'EACH_ISSUES', '客我双方的问题'),
(34, 'NOT_FOUND_REASON', '暂时未确定原因'),
(35, 'CUSTOMER_SERVICE_REQUIREMENT', '客户服务需求'),
(36, 'OTHER_REQUIREMENT', '其他需求'),
(37, 'PRODUCT_TIME_OUT', '产品资源到期提醒'),
(38, 'PRODUCT_UPGRADE', '产品需授权维护升级'),
(39, 'PRODUCT_CHANGE_NOTICE', '产品平台变更通告'),
(40, 'PRODUCT_RISK_NOTICE', '产品侧主动风险告警'),
(41, 'PRODUCT_DISCOUNT_NOTICE', '产品优惠信息通知'),
(42, 'NEW_FUNCTION_NOTICE', '产品新功能实现通知'),
(43, 'ACTIVE_DEVICE_CHECK', '主动设备巡检巡查'),
(44, 'ACTIVE_CUSTOMER_VISIT', '主动客户拜访'),
(45, 'ACTIVE_OTHER_SERVICE', '其他主动服务');
