CREATE TABLE dwm_company_service_schedule(
  value_of_primary_key   STRING          COMMENT '主键',
  `date`                 DATE            COMMENT '排班日期',
  duty_name              STRING          COMMENT '队列名称',
  user_name              STRING          COMMENT '用户名称',
  company_id             BIGINT          COMMENT '供应商id',
  work_id                STRING          COMMENT '班次ID',
  time_start1            STRING          COMMENT '阶段1开始时间',
  time_end1              STRING          COMMENT '阶段1结束时间',
  time_start2            STRING          COMMENT '阶段2开始时间',
  time_end2              STRING          COMMENT '阶段2结束时间'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`, `date`)
COMMENT "供应商排班表"
PARTITION BY RANGE (`date` ) (
    START ('2016-01-01') END ('2030-01-01') EVERY (INTERVAL 1 MONTH)
)
DISTRIBUTED BY HASH(`value_of_primary_key`, `date`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "enable_persistent_index" = "true",
    "dynamic_partition.enable" = "true",
    "dynamic_partition.buckets" = "8",
    "dynamic_partition.time_unit" = "MONTH",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.history_partition_num" = "0",
    "bloom_filter_columns" = "user_name"
);