CREATE TABLE dim_company_info
(
    `company_id`                 BIGINT      COMMENT  '公司ID',
    `company_short_name`         STRING      COMMENT  '公司枚举',
    `company_name`               STRING      COMMENT  '公司中文名'
) ENGINE=OLAP
PRIMARY KEY(`company_id`)
COMMENT "公司纬表"
DISTRIBUTED BY HASH(`company_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "company_id"
);
