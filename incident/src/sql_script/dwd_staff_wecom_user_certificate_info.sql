CREATE TABLE dwd_staff_wecom_user_certificate_info
(
     userid               STRING    COMMENT  '客服id'
    ,id                   STRING    COMMENT  '证书id'
    ,value_of_primary_key STRING    COMMENT  'uuid生成的主键'
    ,record_update_time   DATETIME  COMMENT  '数据上报时间'
    ,title                STRING    COMMENT  '证书标题'
    ,code                 STRING    COMMENT  '证书完整编号'
    ,source               STRING    COMMENT  '证书颁发来源 admin:管理后台 class:课堂应用 learning:学习地图应用 exam:考试应用'
    ,created_at           DATETIME  COMMENT  '证书创建日期'
    ,image                STRING    COMMENT  '证书图片'
    ,status               STRING    COMMENT  '证书状态'
    ,expired_at           DATETIME  COMMENT  '证书有效期'
    ,source_text          STRING    COMMENT  '证书颁发来源中文标识'
) ENGINE=OLAP
PRIMARY KEY(`userid`, `id`)
COMMENT "供应商客服人员持证信息"
DISTRIBUTED BY HASH(`userid`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "userid"
);
