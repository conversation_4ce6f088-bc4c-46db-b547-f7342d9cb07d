CREATE TABLE dwd_incident_ticket_alarm_record
(
    `id`                      BIGINT COMMENT '主键',
    `ticket_id`               BIGINT COMMENT '工单ID',
    `create_time`             DATETIME COMMENT '创建时间',
    `cnt`                     BIGINT COMMENT '',
    `user_list`               STRING COMMENT '告警人员，人员ID通过t903_company_user业务逻辑获取',
    `type`                    BIGINT COMMENT '告警类型。1: 派单超时告警, 9:催单告警, 21:未回复客户超时告警, 22:待处理超时告警, 23:工单总处理耗时告警, 24:未响应告警, 25:超时未同步最新进展, 26:客户投诉响应超时告警, 27:会话超时告警, 28:风险次数告警, 29:风险响应超时告警',
    `has_notice`              STRING COMMENT '',
    `post`                    BIGINT COMMENT '岗位。0: 其他, 1: 供应商, 2:一线, 3: 1.5线, 4: 运维, 5:产研; 专有云相关: 8:售后运维, 9:TCE产研, 10:TBDS产研, 11:垂直产研; TCE售中二线: 12:二线',
    `priority`                BIGINT COMMENT '优先级。-1: L1, 0: L2, 1: L3, 2: L4, 3: L5',
    `ticket`                  STRING COMMENT '',
    `service_channel`         BIGINT COMMENT '服务通道',
    `service_scene_checked`   BIGINT COMMENT 't217_ticket_scenes',
    `operation_service_scene` BIGINT COMMENT 't243_operate_scene',
    `level`                   BIGINT COMMENT '告警等级',
    `current_operator`        BIGINT COMMENT '当前处理人，t903_company_user',
    `company`                 BIGINT COMMENT '供应商。0:腾讯云, 1:忆享云, 2:安畅, 3:区技-南区-腰部(原悦智), 4:区技-西区-腰部(原云腾未来), 5:安畅-腰部(原安畅ka), 6:华南-区技, 7:华东-区技, 8:华西-区技, 9:安畅专项, 10:华北-区技, 11:infosys, 12:腾讯云KA, 13:叁玖叁, 14:悦智企点, 15:上海电销, 18:私有云华云, 23:私有云安畅, 24:中小企业服务, 29:中国外运股份有限公司, 30:北京中讯汉扬科技发展有限公司, 31:北京轻元科技有限公司, 32:东华云计算有限公司, 45:海云捷讯tstack',
    `fact_assign`             BIGINT COMMENT '实派队列',
    `responsible`             BIGINT COMMENT '责任人，t903_company_user',
    `uin`                     STRING COMMENT '客户UIN',
    `level_user_list`         STRING COMMENT '当前告警人',
    `next_level_user_list`    STRING COMMENT '下一轮告警至',
    `alarm_title`             STRING COMMENT '告警标题',
    `ticket_operation`        STRING COMMENT '',
    `assign_error`            STRING COMMENT ''
) ENGINE = OLAP
PRIMARY KEY (`id`, `ticket_id`,`create_time`)
COMMENT "工单告警记录表"
PARTITION BY RANGE (`create_time`) (
    START ('2016-01-01') END ('2030-01-01') EVERY (INTERVAL 1 MONTH)
)
DISTRIBUTED BY HASH (`ticket_id`,`create_time`)
PROPERTIES (
     "dynamic_partition.enable" = "true",
    "dynamic_partition.buckets" = "8",
    "dynamic_partition.time_unit" = "MONTH",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.history_partition_num" = "0",
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "ticket_id"
);
