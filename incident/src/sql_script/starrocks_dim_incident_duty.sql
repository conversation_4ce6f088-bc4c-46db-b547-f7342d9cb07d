CREATE TABLE dim_incident_duty
(
    duty_id                             BIGINT            COMMENT '职责ID/队列ID',
    duty_name                           STRING            COMMENT '职责/队列名称,如：流程队列',
    duty_description                    STRING            COMMENT '职责/队列描述,如：MC工单流程队列',
    is_valid                            BIGINT            COMMENT '是否可用：1 可用，0 不可用',
    is_poc                              BIGINT            COMMENT '是否固定POC：1 是，0 否',
    poc_main                            STRING            COMMENT '主poc',
    poc_backup                          STRING            COMMENT '副poc',
    create_time                         DATETIME          COMMENT '创建时间',
    creator                             STRING            COMMENT '创建人',
    modify_time                         DATETIME          COMMENT '更新时间',
    modifier                            STRING            COMMENT '更新人',
    duty_en_name                        STRING            COMMENT '职责/队列英文名称',
    backup_duties                       STRING            COMMENT '备选队列',
    leader                              STRING            COMMENT 'leader',
    director                            STRING            COMMENT '总监',
    gm                                  STRING            COMMENT 'gm',
    base_duty                           STRING            COMMENT '兜底队列',
    responsible                         STRING            COMMENT '队列负责人',
    duty_type                           STRING            COMMENT '队列类型',
    time_config                         STRING            COMMENT '时间配置(给大客户值班队列用),json格式:{"is_holiday":0|1|-1(忽略节假日判断),"begin_time":"xxx","end_time":"xxx"}',
    companies                           STRING            COMMENT '队列公司',
    is_responsible                      BIGINT            COMMENT '是否客户代表队列：1|是，0|否',
    assign_type                         BIGINT            COMMENT '派单类型: 0|无，1|在线&值班人员，2|在线人员，3|POC派单',
    svip_director                       STRING            COMMENT 'svip紧急接单人',
    duty_admin                          STRING            COMMENT '管理员',
    notice_person                       STRING            COMMENT '关注人',
    xingyun_class_id                    BIGINT            COMMENT '星云分类ID',
    xingyun_duty_group_id               BIGINT            COMMENT '星云值班组id',
    base_user                           STRING            COMMENT '兜底接单人',
    duty_language                       STRING            COMMENT '队列语言属性',
    problem_operator                    STRING            COMMENT '问题管理接单人',
    private_cloud_product               STRING            COMMENT '私有云出包产品',
    dispatch                            STRING            COMMENT '是否需要分发',
    pcloud_role                         STRING            COMMENT '私有云队列角色 1：区技专家 2：技术专家 3：SPMO',
    pcloud_dimension                    STRING            COMMENT '映射关系维度，1：产品维度 2：区域维度',
    pcloud_area                         STRING            COMMENT '映射关系区域，1：华东 2：华南 3：华北 4：西南',
    service_problem_manager             STRING            COMMENT '服务问题经理',
    service_problem_creator             STRING            COMMENT '服务问题处理人',
    is_schedule_notify_on               STRING            COMMENT '是否开启排班短信通知，yes：开启 no: 关闭',
    is_use_new_assign_engine            BIGINT            COMMENT '是否启用新派单引擎：0=否，1=是',
    users_from                          BIGINT            COMMENT '队列用户来源',
    assign_strategy_id                  BIGINT            COMMENT '派单策略id',
    fixed_users                         STRING            COMMENT '固定人员',
    use_new_assign_strategy_time        DATETIME          COMMENT '关联新派单策略时间',
    service_provider_assign_percent     STRING            COMMENT '服务商派单比例配置',
    is_sp_assign_config_on              BIGINT            COMMENT '是否启用服务商比例派单：0=否，1=是'
) ENGINE=OLAP
PRIMARY KEY(`duty_id`)
COMMENT "事件管理-队列/职责维表"
DISTRIBUTED BY HASH(`duty_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "duty_id"
);
