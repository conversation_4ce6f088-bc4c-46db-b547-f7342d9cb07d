CREATE TABLE dim_incident_ticket_operation_type
(
    dim_id                          BIGINT            COMMENT '工单操作类型id',
    operation_type_eng              STRING            COMMENT '工单操作类型英文',
    operation_type_chin             STRING            COMMENT '工单操作类型中文'
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "工单操作人岗位维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);



INSERT INTO dim_incident_ticket_operation_type(dim_id, operation_type_eng, operation_type_chin) VALUES
(0, 'UNKNOW', '其它'),
(1, 'CREATE', '建单'),
(2, 'RETRIEVE', '进入详情'),
(3, 'PULL', '认领'),
(4, 'URGE', '催单'),
(5, 'TRANSFER', '转单'),
(6, 'DISPATCH', '派单'),
(7, 'SEPARATE', '拆单'),
(8, 'REPLY', '回复'),
(9, 'CUSTOMER_REPLY', '客户回复'),
(10, 'TRANSFER_RESPONSIBLE', '交接'),
(11, 'WAIT_CUSTOMER_ADD_INFO', '待客户补充'),
(12, 'CLOSE_APPLICATION', '申请结单'),
(13, 'AGREE_CLOSE_APPLICATION', '同意并继续处理'),
(14, 'DISAGREE_CLOSE_APPLICATION', '不同意申请结单'),
(15, 'WAIT_CUSTOMER_CLOSE', '待客户确认结单'),
(16, 'CLOSE', '结单'),
(17, 'CANCEL', '撤销'),
(18, 'DELETE', '删除'),
(19, 'EDIT', '编辑'),
(21, 'COMMENT', '评论'),
(22, 'REFUSE', '拒单'),
(23, 'REPORT_FAILURE', '上报故障'),
(24, 'BATCH_REPLY_FAILURE', '故障单批量回复'),
(25, 'VNC_AUTH_REQUEST', 'VNC授权'),
(26, 'VNC_VIEW_URL', 'VNC查看链接'),
(27, 'APPRAISE', '评价'),
(28, 'ADD_INFO_APPLICATION', '申请补充或待复现'),
(29, 'TRANSFER_TAPD_BUG', '转TAPD Bug'),
(30, 'SET_DELAY_DISPATCH', '设置延迟派单'),
(31, 'COMPLAINT', '投诉'),
(32, 'TRANSFER_TAPD_STORY', '转需求'),
(33, 'PULL_RESPONSIBLE', '认领客户代表'),
(34, 'TRANSFER_POINT_STONE', '转点石'),
(35, 'TO_CONFIRM_RESTORE', '待确认业务恢复'),
(36, 'RESTORED', '已恢复分析根因'),
(37, 'RESET_STATUS', '重新打开'),
(38, 'NOT_RESTORED', '未恢复'),
(39, 'TRANSFER_TAPD', '转TAPD'),
(40, 'AGREE_ADD_INFO_APPLICATION', '同意申请补充'),
(41, 'DISAGREE_ADD_INFO_APPLICATION', '不同意申请补充'),
(42, 'PENDING_CHANGE', '待变更(需出包修复)'),
(43, 'PULL_INCIDENT_MANAGER', '认领事件经理'),
(44, 'EFFECTIVE_CALL', '有效外呼'),
(45, 'NONEFFECTIVE_CALL', '无效外呼'),
(46, 'CHANGE_TICKET_RELATION', '关联变更单'),
(47, 'NOT_RESTORED_ANALYSIS', '待查根因'),
(48, 'INSURE_ARCHIVE', '确认归档'),
(49, 'WITH_DRAW', '撤回'),
(50, 'BUILD_GROUP_CHAT', '拉群'),
(51, 'INCIDENT_MANAGER_ASSIGN', '事件经理派单'),
(52, 'PROGRESS_UPDATE', '更新'),
(53, 'PROGRESS_REPORT', '报备'),
(54, 'SYSTEM_REPLY', '系统回复'),
(55, 'FSM_UPDATE_PLAN_COMPLETE_TIME', '修改预计完成时间'),
(56, 'AUTO_DIAGNOSE', '自动诊断'),
(57, 'DIAGNOSE_FAILED', '诊断失败'),
(58, 'DIAGNOSE_SUCCESS', '诊断成功'),
(59, 'DIAGNOSE_ARTICLE', '精选'),
(60, 'DIAGNOSE_MANUAL', '手动诊断'),
(61, 'REFUSE_TEG_FIRST_TIME', '拒单');
