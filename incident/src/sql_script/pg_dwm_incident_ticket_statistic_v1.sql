drop table  if exists dwm_incident_ticket_statistic_v1;
CREATE TABLE dwm_incident_ticket_statistic_v1
(
    ticket_id                                          bigint primary key ,
    uin                                                bigint,
    related_region_code                                text,
    related_phone_number                               text,
    service_channel                                    bigint,
    service_scene                                      bigint,
    service_scene_checked                              bigint,
    priority                                           bigint,
    creator                                            text,
    create_time                                        timestamp(6) without time zone default null,
    current_operator                                   text,
    last_operator                                      text,
    last_staff                                         text,
    last_operate_time                                  timestamp(6) without time zone default null,
    question                                           text,
    last_inner_reply                                   text,
    status                                             bigint,
    operation_ticket_id                                text,
    responsible                                        text,
    first_line_responsible                             text,
    second_line_responsible                            text,
    solve_status                                       bigint,
    service_rate                                       bigint,
    satisfaction                                       bigint,
    unsatisfy_reason                                   bigint,
    first_should_assign                                bigint,
    first_fact_assign                                  bigint,
    first_fact_assign_time                             timestamp(6) without time zone default null,
    should_assign                                      bigint,
    fact_assign                                        bigint,
    fact_assign_time                                   timestamp(6) without time zone default null,
    unsatisfy_ticket_id                                bigint,
    related_unsatisfied_ticket_id                      bigint,
    related_qcloud_ticket_id                           text,
    qcloud_ticket_id                                   text,
    qcloud_category_id                                 bigint,
    qcloud_receive_notice_flag                         bigint,
    instance_id                                        text,
    woodpecker_jobid                                   text,
    appraise                                           text,
    appraise_time                                      timestamp(6) without time zone default null,
    next_follow_time                                   timestamp(6) without time zone default null,
    qcloud_complaint_id                                bigint,
    complainted_ticket_id                              bigint,
    language                                           bigint,
    callcenter_answer_time                             timestamp(6) without time zone default null,
    callcenter_hand_up_time                            timestamp(6) without time zone default null,
    callcenter_talk_duration                           bigint,
    callcenter_queue_duration                          bigint,
    callcenter_session_id                              text,
    callcenter_agent_id                                text,
    callcenter_agent_name                              text,
    callcenter_session_filename                        text,
    qqgroup_start_time                                 timestamp(6) without time zone default null,
    qqgroup_end_time                                   timestamp(6) without time zone default null,
    qqgroup_response_time                              timestamp(6) without time zone default null,
    qqgroup_response_duration                          bigint,
    qqgroup_number                                     bigint,
    qqgroup_responsable                                text,
    qqgroup_big_customer_operator                      text,
    secret_content                                     text,
    update_time                                        timestamp(6) without time zone default null,
    qcloud_last_customer_reply_time                    timestamp(6) without time zone default null,
    vip_ask_callback_id                                bigint,
    callback_time_start                                timestamp(6) without time zone default null,
    callback_time_end                                  timestamp(6) without time zone default null,
    owner_uin                                          bigint,
    is_deleted                                         bigint,
    is_ever_to_wan                                     bigint,
    company_id                                         bigint,
    is_claim                                           bigint,
    is_fault_report                                    bigint,
    is_internal_go_up                                  bigint,
    post                                               bigint,
    next_up_responsor                                  text,
    next_up_time                                       timestamp(6) without time zone default null,
    cc_person                                          text,
    operation_service_scene                            text,
    responsible_should_assign                          text,
    chat_group_id                                      text,
    first_assign_company_id                            bigint,
    feedback_channel                                   bigint,
    group_id                                           text,
    source_channel                                     bigint,
    customer_contact_info                              text,
    question_category                                  bigint,
    reason_category                                    bigint,
    question_start_time                                timestamp(6) without time zone default null,
    question_end_time                                  timestamp(6) without time zone default null,
    title                                              text,
    reason                                             text,
    upgrade_time                                       timestamp(6) without time zone default null,
    sales_supportor                                    text,
    trade                                              text,
    name                                               text,
    event_tss_id                                       bigint,
    add_info_application_post                          bigint,
    add_info_application_should_assign                 bigint,
    add_info_application_operator                      text,
    tapd_story_id                                      text,
    callcenter_skill_group                             text,
    keyissues_id                                       bigint,
    complaint                                          bigint,
    complaint_content                                  text,
    ltc_name                                           text,
    product_version                                    text,
    severity                                           text,
    short                                              text,
    extern_status                                      bigint,
    caller                                             text,
    affected_customers                                 text,
    customer_uid                                       text,
    incident_manager                                   text,
    incident_manager_should_assign                     bigint,
    risk_control_first_time                            timestamp(6) without time zone default null,
    risk_control_num                                   bigint,
    is_incident_manager_into                           bigint,
    url                                                text,
    short_url                                          text,
    operators                                          text,
    close_time                                         timestamp(6) without time zone default null,
    close_type                                         bigint,
    reply_times                                        bigint,
    is_reported                                        bigint,
    last_reporter                                      text,
    transfer_times                                     bigint,
    close_operator                                     text,
    interaction_times                                  bigint,
    customer_reply_times                               bigint,
    first_line_reply_times                             bigint,
    first_response_stuff                               text,
    second_line_reply_times                            bigint,
    agent_reply_times                                  bigint,
    last_reported_time                                 timestamp(6) without time zone default null,
    ticket_solved_time                                 timestamp(6) without time zone default null,
    inner_creator                                      text,
    request_source                                     text,
    is_inner_created                                   bigint,
    is_complaint                                       bigint,
    is_satisfy                                         text,
    first_response_queue                               bigint,
    operation_deny_reason                              text,
    second_line_deny_reason                            text,
    backend_first_deny_operator                        text,
    product_research_deny_reason                       text,
    vertical_product_research_deny_reason              text,
    agent_duration                                     bigint,
    unknown_duration                                   bigint,
    customer_duration                                  bigint,
    operation_duration                                 bigint,
    first_line_duration                                bigint,
    second_line_duration                               bigint,
    ticket_deal_duration                               bigint,
    agent_customer_duration                            bigint,
    product_research_duration                          bigint,
    unknown_customer_duration                          bigint,
    first_line_customer_duration                       bigint,
    second_line_customer_duration                      bigint,
    vertical_product_research_duration                 bigint,
    agent_engineer_duration                            bigint,
    ticket_staff_deal_duration                         bigint,
    unknown_engineer_duration                          bigint,
    first_line_engineer_duration                       bigint,
    second_line_engineer_duration                      bigint,
    is_transfer_first_line                             bigint,
    is_denied_by_operation                             bigint,
    is_denied_by_second_line                           bigint,
    transfer_first_line_time                           timestamp(6) without time zone default null,
    transfer_first_line_times                          bigint,
    transfer_operation_times                           bigint,
    transfer_first_line_queue                          bigint,
    transfer_first_line_staff                          string,
    transfer_second_line_times                         bigint,
    transfer_type                                      text,
    is_denied_by_production_research                   bigint,
    transfer_production_research_times                 bigint,
    first_transfer_operation_staff_post                bigint,
    first_transfer_operation_staff                     text,
    transfer_vertical_production_research_times        bigint,
    first_transfer_production_research_staff_post      bigint,
    first_transfer_vert_production_research_staff_post bigint,
    first_transfer_operation_time                      timestamp(6) without time zone default null,
    first_second_line_service_time                     timestamp(6) without time zone default null,
    first_second_line_operator                         text,
    current_second_line_operator                       text,
    first_transfer_second_line_staff                   text,
    first_second_line_assign                           bigint,
    first_second_line_fact_assign                      bigint,
    second_line_solved_duration                        bigint,
    first_second_line_response_duration                bigint,
    first_second_line_fact_operator                    text,
    first_transfer_complaint_queue_time                timestamp(6) without time zone default null,
    measures                                           text,
    customer_waiting_impatience_duration               bigint,
    last_operation_fact_assign                         bigint,
    last_production_research_fact_assign               bigint,
    second_line_operators                              text,
    current_second_line_fact_assign                    text,
    first_transfer_production_research_time            timestamp(6) without time zone default null,
    first_second_line_operator_company_id              bigint,
    current_second_line_operator_company_id            bigint,
    second_line_operator_company_ids                   text,
    is_transfer_company_in_second_line                 bigint,
    is_transfer_inner_staff_in_second_line             bigint,
    second_line_last_refuser                           text,
    last_customer_operator                             text,
    second_line_last_company_operator                  text,
    second_line_refuse_times                           bigint,
    to_be_confirm_close_times                          bigint,
    average_reply_duration                             bigint,
    longest_reply_duration                             bigint,
    longest_reply_staff                                text,
    stuff_counts                                       bigint,
    customer_urge_times                                bigint,
    connect_customer_times                             bigint,
    is_transfer_hotline_first_line                     bigint,
    hotline_first_line_queue                           bigint,
    last_to_be_added_time                              timestamp(6) without time zone default null,
    current_first_line_operator                        text,
    first_response_time                                text,
    first_response_duration                            bigint,
    hotline_first_transfer_deal_duration               bigint,
    hotline_first_transfer_line_time                   text,
    hotline_first_transfer_response_time               text,
    hotline_first_transfer_response_duration           bigint,
    operation_handler                                  text,
    operation_handler_company_id                       bigint,
    second_line_last_company_operator_company_id       bigint,
    first_line_last_operator                           text,
    first_line_last_operator_company_id                bigint,
    first_line_last_operator_fact_assign               bigint,
    production_research_handler                        text,
    first_transfer_second_line_time                    timestamp(6) without time zone default null,
    is_transfer_tgw                                    bigint,
    teg_queue_duration                                 bigint,
    is_phone_callback                                  bigint,
    out_times                                          int,
    last_ofc_operator                                  text,
    last_ofc_operator_post                             int,
    over_3h_no_reply_count                             int
);
CREATE INDEX index_dwm_ticket_statistic_create_time ON dwm_incident_ticket_statistic_v1(create_time);
CREATE INDEX index_dwm_ticket_statistic_close_time ON dwm_incident_ticket_statistic_v1(close_time);
CREATE INDEX index_dwm_ticket_statistic_uin ON dwm_incident_ticket_statistic_v1(uin);
CREATE INDEX index_dwm_ticket_service_channel_index ON dwm_incident_ticket_statistic_v1(service_channel);

alter table dwm_incident_ticket_statistic_v1
    add column reply_over_2h_times int not null default 0,
comment on column dwm_incident_ticket_statistic_v1.reply_over_2h_times is '超2h未回复客户次数';
