CREATE TABLE `dim_service_scenes`
(
    `dim_id` BIGINT COMMENT "主键id",
    `service_scene_level1_id`   BIGINT COMMENT "TSS一级归档ID",
    `service_scene_level2_id`   BIGINT COMMENT "TSS二级归档ID",
    `service_scene_level3_id`   BIGINT COMMENT "TSS三级归档ID",
    `service_scene_level4_id`   BIGINT COMMENT "TSS四级归档ID",
    `service_scene_level1_name` STRING COMMENT "TSS一级归档名称",
    `service_scene_level2_name` STRING COMMENT "TSS二级归档名称",
    `service_scene_level3_name` STRING COMMENT "TSS三级归档名称",
    `service_scene_level4_name` STRING COMMENT "TSS四级归档名称",
    `obs_id` STRING COMMENT "成本ID",
    `status` bigint(20) NULL COMMENT "状态：1-有效，0-无效"
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "工单归档维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);




ALTER TABLE dim_service_scenes ADD COLUMN disabled INT COMMENT '是否删除' AFTER status;

ALTER TABLE dim_service_scenes ADD COLUMN (
    update_time DATETIME COMMENT '更新时间',
    modify_time DATETIME COMMENT '更改时间'
);
