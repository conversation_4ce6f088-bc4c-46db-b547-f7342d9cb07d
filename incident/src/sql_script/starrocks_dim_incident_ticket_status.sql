CREATE TABLE dim_incident_ticket_status
(
    dim_id                  BIGINT            COMMENT '工单状态id',
    status_eng              STRING            COMMENT '工单状态值英文',
    status_chin             STRING            COMMENT '工单状态值中文'
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "工单状态维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);



INSERT INTO dim_incident_ticket_status(dim_id, status_eng, status_chin) VALUES
(3, 'CLOSED', '已结单'),
(4, 'TO_BE_ADDED_BY_CUSTOMER', '待补充'),
(5, 'CLOSE_CONFIRMATION_PENDING', '待确认结单'),
(10, 'CANCELLED', '已撤销'),
(11, 'DELETED', '已删除'),
(21, 'PENDING', '待处理'),
(22, 'IN_HAND', '处理中'),
(23, 'CLOSE_APPLICATION', '申请结单'),
(24, 'ADD_INFO_APPLICATION', '申请补充或待复现'),
(25, 'TO_CONFIRM_RESTORE', '待确认业务恢复'),
(26, 'RESTORED', '已恢复分析根因'),
(27, 'PENDING_CHANGE', '待变更（需出包修复）'),
(28, 'NOT_RESTORED_ANALYSIS', '待查根因');
