CREATE TABLE dwd_incident_ticket_public_opinion
(

    `id`                            BIGINT              COMMENT '主键id',
    `ticket_id`                     BIGINT              COMMENT '工单id',
    `grade`                         STRING              COMMENT '舆情等级(ss,s,a,b)',
    `click_count`                   BIGINT              COMMENT '舆情点击量',
    `comment_count`                 BIGINT              COMMENT '舆情评论量',
    `source`                        STRING              COMMENT '舆情反馈来源',
    `check_time`                    DATETIME            COMMENT '舆情通检测时间',
    `deal_time`                     DATETIME            COMMENT '舆情介入处置时间',
    `opinion_type`                  BIGINT              COMMENT '风险类型，1-产品问题、2-客户端原因、3-服务问题、4-安全入侵类、5-违规封禁、6-其他',
    `child_type`                    BIGINT              COMMENT '风险子类，1-产品功能/易用性、2-产品规则和流程、3-客户自身原因、4-服务专业性、5-原因不明、6-产品稳定性、7-服务态度、8-甲方通报、9-内部发现',
    `compensate_amount`             DECIMAL(12, 2)      COMMENT '赔偿金额',
    `compensate_way`                BIGINT              COMMENT '赔偿方式 1-代金券，2-赠送金，3-现金，4-其他',
    `linked_tickets`                STRING              COMMENT '关联工单',
    `is_sensitive_customer`         BIGINT              COMMENT '是否敏感客户 0-否，1-是',
    `sensitive_type`                BIGINT              COMMENT '敏感客户类型 1、媒体背景2、政府背景 3、法律专业人士 4、高频舆情客户 5、高频投诉客户 6、大额索赔客户 7、弱势群体 8、其他',
    `remark`                        STRING              COMMENT '备注 ',
    `status`                        BIGINT              COMMENT '状态',
    `creator`                       STRING              COMMENT '创建人',
    `create_time`                   DATETIME            COMMENT '创建时间',
    `modifier`                      STRING              COMMENT '最近修改人',
    `modify_time`                   DATETIME            COMMENT '最近修改时间',
    `specific_reason`               STRING              COMMENT '具体原因分析',
    `source_post_url`               STRING              COMMENT '原贴链接',
    `progress`                      STRING              COMMENT '处理进展',
    `public_opinion_operator`       STRING              COMMENT '当前处理人，多个用；隔开',
    `final_conclusion`              STRING              COMMENT '最终结论'
) ENGINE=OLAP
PRIMARY KEY(`id`, `ticket_id`)
COMMENT "DWD层工单舆情表"
DISTRIBUTED BY HASH(`ticket_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "ticket_id"
);


-- add column by julioguo 2021-08-29
ALTER TABLE dwd_incident_ticket_public_opinion ADD COLUMN (
    `is_service_failure`            BIGINT COMMENT '是否服务过失 0-否 1-是',
    `service_company`               BIGINT COMMENT '服务商',
    `service_support`               STRING COMMENT '服务支持人员'
);
