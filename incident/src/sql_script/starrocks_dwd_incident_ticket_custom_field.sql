CREATE TABLE dwd_incident_ticket_custom_field
(
    id                          BIGINT          COMMENT 'id',
    ticket_id                   BIGINT          COMMENT '工单id',
    custom_field_key            STRING          COMMENT '自定义字段键',
    custom_field_value          STRING          COMMENT '自定义字段值',
    custom_field_id             BIGINT          COMMENT '自定义字段id对应官网分类下的自定义字段id',
    custom_field_display        STRING          COMMENT '自定义字段显示名'
) ENGINE=OLAP
PRIMARY KEY(`id`, `ticket_id`)
COMMENT "DWD层工自定义字段表"
DISTRIBUTED BY HASH(`ticket_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "ticket_id"
);
