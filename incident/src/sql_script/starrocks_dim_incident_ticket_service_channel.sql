CREATE TABLE dim_incident_ticket_service_channel
(
    dim_id                           BIGINT            COMMENT '工单服务通道id',
    service_channel_eng              STRING            COMMENT '工单服务通道英文',
    service_channel_chin             STRING            COMMENT '工单服务通道中文'
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "工单服务通道维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);




INSERT INTO dim_incident_ticket_service_channel(dim_id, service_channel_eng, service_channel_chin) VALUES
(1, 'IMCC', 'IMCC'),
(2, 'CALLCENTER', 'CallCenter'),
(3, 'MC', 'MC'),
(4, 'RTX', '腾讯云小助手'),
(5, 'MAIL', '邮件'),
(6, 'MOBIL_PHONE', '备勤手机'),
(7, 'QQ_GROUP', 'QQ群'),
(9, 'BBS', '官网论坛'),
(10, 'RETURN_VISIT', '回访'),
(11, 'PUBLIC_SENTIMENT', '舆情'),
(12, 'COMPLAINT', '投诉/建议'),
(13, 'UNSATISFIED', '不满意'),
(14, 'DUTY', 'duty(工单告警)'),
(15, 'XINGYUNCBS', '母机故障'),
(16, 'FAULT_NOTICE', '故障通知'),
(17, 'CUSTOMER_AUTHORIZATION', '客户授权'),
(18, 'BIG_CUSTOMER_QUESTION', '大客户问题'),
(19, 'QQ_GROUP_AUTO', 'QQ群自动录单'),
(20, 'MC_VIP_ASK_CALLBACK', '预约回访'),
(21, 'QQGROUP_MSG_MONITOR', '大客户工作台'),
(22, 'SC_TICKET', '管局指令通知'),
(23, 'ORACLE', 'Oracle'),
(24, 'SPECIAL_SERVICE', '专项服务组'),
(25, 'QQGROUP_BEIAN', '大客户备案支持'),
(26, 'BEIAN', '备案专项支持'),
(27, 'KA', '大客户群'),
(28, 'P_CLOUD', '私有化售后'),
(29, 'P_CLOUD_INSALES', '私有化售中/POC测试'),
(30, 'TENCENT_ON_QCLOUD', '内部服务台'),
(31, 'CLOUD_ARCHITECTURE_PLATFORM', '云架平'),
(32, 'TKE_CONTAINER_OR_BEIAN', '内部建单(TKE容器/备案等)'),
(33, 'SAFE_P_CLOUD', '安全内部建单'),
(34, 'PUBLIC_CLOUD_SALE', '公有云售中'),
(35, 'CSIG_KNOWLEDGE_PLATFORM', 'CSIG知识平台'),
(36, 'AMP_MONITOR', 'AMP监控'),
(37, 'SAAS_PRODUCT', 'SaaS产品'),
(38, 'REPORTING_PLATFORM', '举报平台'),
(39, 'MC_CONVERSATION', '在线售后'),
(40, 'P_CLOUD_COMPANY', '私有化售后(供应商)'),
(41, 'P_CLOUD_INSALES_COMPANY', '私有化售中/POC测试(供应商)'),
(42, 'P_CLOUD_ASP', '私有云ASP'),
(43, 'E_GOVERNMENT_CLOUD', '政务云'),
(44, 'QIDIAN_BOSS', '企点BOSS'),
(45, 'OMP', '经营平台'),
(46, 'WECHAT_PAY_MLT', '微信支付中长尾商户'),
(47, 'WECHAT_PAY_KA', '微信支付KA商户'),
(48, 'CONVERSATION_PRESALE', '在线售前'),
(49, 'CONVERSATION_TENCENT_MEETING', '腾讯会议(售后)'),
(50, 'CONVERSATION_TENCENT_MEETING_PRESALE', '腾讯会议(售前)'),
(51, 'PARTNER_SUPPORT', '合作伙伴支持'),
(52, 'WECHAT_FACE_PAY_KA', '微信支付刷脸KA商户群'),
(53, 'CONVERSATION_MEETING_SECRETARY', '腾讯会议小秘书'),
(60, 'FSM', '服务请求'),
(61, 'XINGYUN_ALERT', '星云平台告警'),
(62, 'TRTC', '终端专家服务'),
(63, 'CLOUD_PRODUCT_SUPPORT_ASSISTANT', '云产品技术支持助手'),
(64, 'SELFBUILT_FOR_SECURITY', '安全行业自建'),
(65, 'QIDIAN_BIG_CUSTOMER', '企点大客户通道'),
(66, 'PARTNER_CPT', '伙伴CPT'),
(69, 'P_CLOUD_CONVERSATION', '私有云在线'),
(72, 'QIDIAN_P_CLOUD', '企点私有化');
