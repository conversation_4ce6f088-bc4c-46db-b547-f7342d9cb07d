CREATE TABLE dim_incident_ticket_priority
(
    dim_id                    BIGINT            COMMENT '工单优先级id',
    priority_eng              STRING            COMMENT '工单优先级英文',
    priority_chin             STRING            COMMENT '工单优先级中文'
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "工单优先级维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);

INSERT INTO andata_rt.dim_incident_ticket_priority(dim_id, priority_eng, priority_chin) VALUES
(-1, 'L1', '故障'),
(0, 'L2', 'SVIP紧急'),
(1, 'L3', '非常紧急'),
(2, 'L4', '紧急'),
(3, 'L5', '标准');
