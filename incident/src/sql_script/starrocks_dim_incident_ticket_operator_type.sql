CREATE TABLE dim_incident_ticket_operator_type
(
    dim_id                         BIGINT            COMMENT '工单操作类型id',
    operator_type_eng              STRING            COMMENT '工单操作人类型英文',
    operator_type_chin             STRING            COMMENT '工单操作人类型中文'
) ENGINE=OLAP
PRIMARY KEY(`dim_id`)
COMMENT "工单操作人类型维表"
DISTRIBUTED BY HASH(`dim_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "true",
    "bloom_filter_columns" = "dim_id"
);


INSERT INTO dim_incident_ticket_operator_type(dim_id, operator_type_eng, operator_type_chin) VALUES
(1, 'CUSTOMER', '客户'),
(2, 'STAFF', '客服'),
(3, 'SYSTEM', '系统'),
(4, 'XINGYUN_SYSTEM', '星云'),
(5, 'FAULT_NOTIFY', '故障'),
(6, 'INCIDENT_MANAGER', '事件经理'),
(7, 'TOOL', '工具');
