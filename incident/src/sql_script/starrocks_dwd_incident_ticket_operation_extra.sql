CREATE TABLE dwd_incident_ticket_operation_extra
(
    ticket_id                                   BIGINT                  COMMENT '工单ID',
    is_transfer_first_line                      BIGINT                  COMMENT '是否转一线',
    transfer_first_line_time                    DATETIME                COMMENT '转一线时间',
    first_line_queue                            BIGINT                  COMMENT '转一线队列',
    first_line_first_response_time              DATETIME                COMMENT '一线首次响应时间(首次外部回复时间)',
    first_line_first_response_duration          BIGINT                  COMMENT '一线首次响应时长(一线首次响应时间-派单时间)',
    first_line_finish_time                      DATETIME                COMMENT '一线服务结束时间(一线转转二线的时间或一线确认结单时间)',
    first_line_duration                         BIGINT                  COMMENT '一线服务时长(不含运维时间)',
    first_line_first_transfer                   STRING                  COMMENT '一线转单人,首次转一线队列内部、转二线、转运维的人',
    is_transfer_second_line                     BIGINT                  COMMENT '是否转二线',
    first_transfer_second_line_time             DATETIME                COMMENT '首次转二线时间(二线服务开始时间)',
    second_line_queue                           BIGINT                  COMMENT '转二线队列',
    second_line_first_response_time             DATETIME                COMMENT '二线首次响应时间(首次外部回复时间)',
    second_line_duration                        BIGINT                  COMMENT '二线工程师服务时长（不含运维时间）',
    second_line_finish_time                     DATETIME                COMMENT '二线服务结束时间(二线结单时间)',
    second_line_deny_reason                     BIGINT                  COMMENT '二线拒绝一线原因',
    second_line_first_transfer                  STRING                  COMMENT '二线转单人,首次转队列内部、转运维的人',
    is_transfer_reasonable                      BIGINT                  COMMENT '一线转单是否合理',
    is_transfer_operation                       BIGINT                  COMMENT '是否转运维',
    first_transfer_operation                    STRING                  COMMENT '首次转运维的人',
    first_transfer_operation_time               DATETIME                COMMENT '首次转运维时间',
    transfer_operation_time                     DATETIME                COMMENT '最近一次转运维时间',
    operation_duration                          BIGINT                  COMMENT '运维服务时长',
    operation_deny_reason                       STRING                  COMMENT '运维拒绝原因',
    last_to_be_added_operator                   STRING                  COMMENT '最后一次选择“待补充”的操作人',
    last_to_be_added_time                       DATETIME                COMMENT '最后一次操作“待补充”时间',
    to_be_added_duration                        BIGINT                  COMMENT '待补充客户处理时长,客户回复时间-转待补充的时间,累计每一次客户处理时长',
    last_close_confirmation_operator            STRING                  COMMENT '最后一次选择“待确认结单”的操作人',
    last_close_confirmation_time                DATETIME                COMMENT '最后一次操作“待确认结单”时间',
    close_confirmation_duration                 BIGINT                  COMMENT '待确认结单客户处理时长,客户留言时间-转待确认结单时间,累计每一次客户处理时长',
    close_confirmation_type                     BIGINT                  COMMENT '待确认结单类型,2客服转|3系统转',
    close_time                                  DATETIME                COMMENT '结单时间',
    close_operator                              STRING                  COMMENT '结单人',
    close_type                                  BIGINT                  COMMENT '结单类型,1客户结单|2客服结单|3系统结单',
    operators                                   STRING                  COMMENT '工单经手人,格式 [operator1,operator2,...]',
    first_line_deny_reason                      BIGINT                  COMMENT '一线拒绝原因',
    is_transfer_tencentyun                      BIGINT                  COMMENT '是否转腾讯云',
    apply_close_reason                          STRING                  COMMENT '原因分析',
    apply_close_reason_category                 BIGINT                  COMMENT '原因分析分类',
    apply_close_deal_method                     STRING                  COMMENT '处理方法',
    apply_close_avoidance                       STRING                  COMMENT '避免方法',
    apply_close_is_monitor                      BIGINT                  COMMENT '是否监控',
    apply_close_alarm                           BIGINT                  COMMENT '告警来源',
    apply_close_belong_team                     BIGINT                  COMMENT '团队归属',
    apply_close_tester                          STRING                  COMMENT '测试人员',
    tapd_bug_id                                 STRING                  COMMENT '转tapd bug单号',
    tapd_bug_belong_team                        BIGINT                  COMMENT 'tapd bug归属团队',
    is_transfer_production_research             BIGINT                  COMMENT '是否转产研',
    is_priority_change                          BIGINT                  COMMENT '是否优先级变更',
    is_p_operation                              BIGINT                  COMMENT '是否转过售后运维',
    is_tce                                      BIGINT                  COMMENT '是否转过TCE产研',
    is_tbds                                     BIGINT                  COMMENT '是否转过TBDS产研',
    is_vertical                                 BIGINT                  COMMENT '是否转过垂直产研',
    point_stone                                 BIGINT                  COMMENT '点石单号',
    tapd_bug_workspace_id                       BIGINT                  COMMENT '转tapd bug项目id',
    apply_close_discovery_way                   BIGINT                  COMMENT '发现途径',
    apply_close_push_customer                   BIGINT                  COMMENT '监控是否及时push客户',
    effect_id                                   BIGINT                  COMMENT '影响度',
    effect_other_reason                         STRING                  COMMENT '影响度选择其它的原因',
    customer_emotion_id                         BIGINT                  COMMENT '客户情绪',
    customer_emotion_other_reason               STRING                  COMMENT '客户情绪选择其它的原因',
    test_analysis_bug_id                        BIGINT                  COMMENT '测试分析功能转TAPD项目缺陷ID',
    monitor_analysis_tapd_id                    BIGINT                  COMMENT '监控分析功能转TAPD项目质量运营ID',
    teg_ticket_receiver                         STRING                  COMMENT 'TEG工单接单人',
    mc_conversation_id                          STRING                  COMMENT 'MC会话ID',
    handled_companies                           STRING                  COMMENT '经手过的公司ID,格式:[id1][id2]',
    tapd_data                                   STRING                  COMMENT 'tapd单数据',
    front_and_back_end_responsibility           BIGINT                  COMMENT '前后端归属',
    apply_close_reason_category_supplement      STRING                  COMMENT '原因分类其他补充',
    channel_type                                BIGINT                  COMMENT '渠道类型',
    duty_groups                                 STRING                  COMMENT '责任团队'
) ENGINE=OLAP
PRIMARY KEY(`ticket_id`)
COMMENT "DWD层工单流转过程产生的附加信息"
DISTRIBUTED BY HASH(`ticket_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "ticket_id"
);
