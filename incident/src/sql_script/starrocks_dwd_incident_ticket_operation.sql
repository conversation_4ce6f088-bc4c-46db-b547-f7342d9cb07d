CREATE TABLE dwd_incident_ticket_operation
(
    operation_id            BIGINT          COMMENT '操作记录流水ID',
    ticket_id               BIGINT          COMMENT '工单ID',
    operate_time            DATETIME        COMMENT '处理时间',
    operator                STRING          COMMENT '处理人',
    operator_type           BIGINT          COMMENT '处理人类型：1|客户,2|客服,3|系统,4|星云系统,5|故障, 6|转服务侧总负责人',
    inner_reply             STRING          COMMENT '内部回复',
    extern_reply            STRING          COMMENT '外部回复(客户或客服)',
    qcloud_comment_id       BIGINT          COMMENT '官网回复ID',
    target_status           BIGINT          COMMENT '操作后的工单状态',
    next_operator           STRING          COMMENT '转单到该处理人',
    next_assign             BIGINT          COMMENT '转单到该队列',
    duration                BIGINT          COMMENT '处理时长(秒),等于当前时间减去上一次处理时间',
    remark                  STRING          COMMENT '备注',
    secret_content          STRING          COMMENT '敏感信息字段',
    is_undo                 BIGINT          COMMENT '是否撤销回复',
    company_id              BIGINT          COMMENT '合作公司ID',
    target_post             BIGINT          COMMENT '目标岗位',
    cc_person               STRING          COMMENT '抄送人员',
    operation_type          BIGINT          COMMENT '操作类型',
    status                  BIGINT          COMMENT '操作前的工单状态',
    current_operator        STRING          COMMENT '工单处理人',
    fact_assign             BIGINT          COMMENT '实派队列',
    post                    BIGINT          COMMENT '当前岗位',
    responsible             STRING          COMMENT '当前客户代表',
    next_responsible        STRING          COMMENT '下一个客户代表',
    customer_fields         STRING          COMMENT '自定义字段，存json格式数据记录|上报',
    request_source          STRING          COMMENT '官网工单来源：MC|API',
    target_extern_status    BIGINT          COMMENT '目标外网工单状态'
) ENGINE=OLAP
PRIMARY KEY(`operation_id`, `ticket_id`)
COMMENT "DWD层工单操作流水表"
DISTRIBUTED BY HASH(`ticket_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "ticket_id"
);
