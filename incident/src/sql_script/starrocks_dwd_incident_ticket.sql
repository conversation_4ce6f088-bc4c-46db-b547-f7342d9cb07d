CREATE TABLE dwd_incident_ticket
(
    ticket_id                                   BIGINT            COMMENT '工单ID,起始值1000001,旧工单系统的工单ID小于1000000',
    uin                                         BIGINT            COMMENT '提交工单的客户uin',
    related_region_code                         STRING            COMMENT '关联该工单的手机号地区代码',
    related_phone_number                        STRING            COMMENT '关联该工单的手机号加密',
    service_channel                             BIGINT            COMMENT '服务通道：1|IMCC,2|CallCenter,3|MC,4|腾讯云小助手,5|邮件,6|备勤手机,7|QQ群,8|外呼,9|官网论坛,10|回访,11|舆情监控,12|投诉/建议,13|不满意,14|duty(工单告警),15|星云CBS, 27|KA',
    service_scene                               BIGINT            COMMENT '客户/客服提交的服务场景',
    service_scene_checked                       BIGINT            COMMENT '客服修改后的服务场景,当客户提交的服务场景不合适时使用,初始与service_scene一致',
    archive_type                                BIGINT            COMMENT '归档类型：1|故障,2|产品建议,3|运维需求,4|产品咨询,5|技术咨询,6|购买需求,7|投诉,8|表扬,9|举报',
    priority                                    BIGINT            COMMENT '工单优先级：1|非常紧急,2|紧急,3|一般',
    creator                                     STRING            COMMENT '工单创建人',
    create_time                                 DATETIME          COMMENT '工单创建时间',
    current_operator                            STRING            COMMENT '当前处理人',
    last_operator                               STRING            COMMENT '上一个处理人',
    last_staff                                  STRING            COMMENT '上一个处理该工单的客服（这里其实是最近处理工单的客服）',
    last_operate_time                           DATETIME          COMMENT '上一次处理时间',
    question                                    STRING            COMMENT '客户问题内容',
    last_inner_reply                            STRING            COMMENT '内部最近回复内容',
    status                                      BIGINT            COMMENT '工单状态：1|一线待处理,2|一线处理中,3|已结单,4|待客户补充,5|待确认结单,6|二线待处理,7|转运维,8|二线处理中,9|运维处理中,10|已撤销,11|用户已删除,12|Oracle处理中',
    operation_ticket_id                         STRING            COMMENT '星云工单ID，新版tcs系统已经不会再用到，后续可以删掉',
    responsible                                 STRING            COMMENT '总负责人：服务侧最后一次处理人',
    first_line_responsible                      STRING            COMMENT '一线负责人：MC工单指一线首次外部回复的人,若没有外部回复(如转单),则为空;投诉单/不满意单为首次派单的人;400的单转"客服或技服"时是派单的人;其他通道是建单人',
    second_line_responsible                     STRING            COMMENT '二线负责人：MC工单指二线首次外部回复的人,若没有外部回复(如转单),则为空;非MC指一线转二线的第一个接收人,不管有没有处理',
    solve_status                                BIGINT            COMMENT '官网工单解决状态：-1|未评价,1|已解决,2|未解决',
    service_rate                                BIGINT            COMMENT '官网工单星级评价：0|未评价,1|一星,2|二星,3|三星,4|四星,5|五星',
    satisfaction                                BIGINT            COMMENT '官网工单满意度：0|未评价,1|非常满意,2|满意,3|一般,4|不满意',
    unsatisfy_reason                            BIGINT            COMMENT '官网工单不满意原因：0|未评价,1|产品功能,2|产品稳定性,3|服务态度,4|服务专业性,5|工单处理效率',
    first_should_assign                         BIGINT            COMMENT '记录首次应派队列',
    first_fact_assign                           BIGINT            COMMENT '记录首次实派队列',
    first_fact_assign_time                      DATETIME          COMMENT '首次实派队列时间',
    should_assign                               BIGINT            COMMENT '当前应派队列',
    fact_assign                                 BIGINT            COMMENT '当前实派队列',
    fact_assign_time                            DATETIME          COMMENT '最近一次实派队列时间',
    unsatisfy_ticket_id                         BIGINT            COMMENT '不满意的工单ID(本工单是“不满意单”)',
    related_unsatisfied_ticket_id               BIGINT            COMMENT '关联的“不满意单”',
    related_qcloud_ticket_id                    STRING            COMMENT '“不满意单”关联的官网工单ID',
    qcloud_ticket_id                            STRING            COMMENT '官网工单ID',
    qcloud_category_id                          BIGINT            COMMENT '官网工单分类',
    qcloud_receive_notice_flag                  BIGINT            COMMENT 'MC前端设置是否接收通知的标识位,在MC工单状态发生变化时通知用户(微信和短信),1|7-18点,2|一直接收,3|从不接收',
    instance_id                                 STRING            COMMENT '实例ID,一个或多个,分号分隔',
    woodpecker_jobid                            STRING            COMMENT '调用啄木鸟返回的任务id',
    appraise                                    STRING            COMMENT '评价内容',
    appraise_time                               DATETIME          COMMENT '评价时间',
    next_follow_time                            DATETIME          COMMENT '下次跟进时间',
    qcloud_complaint_id                         BIGINT            COMMENT '官网的投诉单ID',
    complainted_ticket_id                       BIGINT            COMMENT '被投诉的工单ID',
    language                                    BIGINT            COMMENT '工单语言类型：1|中文,2|英文',
    callcenter_answer_time                      DATETIME          COMMENT '400来电接听时间',
    callcenter_hand_up_time                     DATETIME          COMMENT '400来电挂机时间',
    callcenter_talk_duration                    BIGINT            COMMENT '400来电通话时长',
    callcenter_queue_duration                   BIGINT            COMMENT '400来电排队时长',
    callcenter_session_id                       STRING            COMMENT '400来电会话编号',
    callcenter_agent_id                         STRING            COMMENT 'callcenter客服id',
    callcenter_agent_name                       STRING            COMMENT 'callcenter客服名称',
    callcenter_session_filename                 STRING            COMMENT '400来电会话录音文件名',
    qqgroup_start_time                          DATETIME          COMMENT 'QQ群开始时间',
    qqgroup_end_time                            DATETIME          COMMENT 'QQ群结束时间',
    qqgroup_response_time                       DATETIME          COMMENT 'QQ群响应时间',
    qqgroup_response_duration                   BIGINT            COMMENT 'QQ群响应时长,响应时间-开始时间',
    qqgroup_number                              BIGINT            COMMENT 'QQ群号',
    qqgroup_responsable                         STRING            COMMENT 'QQ群责任人',
    qqgroup_big_customer_operator               STRING            COMMENT 'QQ群大客户处理人',
    secret_content                              STRING            COMMENT '敏感信息字段',
    update_time                                 DATETIME          COMMENT '表最近更新时间',
    qcloud_last_customer_reply_time             DATETIME          COMMENT '客户最近回复时间',
    vip_ask_callback_id                         BIGINT            COMMENT 'VIP要求回访 mc_ticket_backend 记录ID',
    callback_time_start                         DATETIME          COMMENT '回访时间起始',
    callback_time_end                           DATETIME          COMMENT '回访时间截止',
    owner_uin                                   BIGINT            COMMENT '创建者QQ',
    is_deleted                                  BIGINT            COMMENT '用户删除工单：1是|0否',
    is_ever_to_wan                              BIGINT            COMMENT '是否转过外网：1是|0否',
    company_id                                  BIGINT            COMMENT '当前工单所在的公司ID',
    is_claim                                    BIGINT            COMMENT '是否索赔',
    is_fault_report                             BIGINT            COMMENT '是否需要故障报告',
    is_internal_go_up                           BIGINT            COMMENT '是否需要内部升级',
    post                                        BIGINT            COMMENT '当前岗位',
    next_up_responsor                           STRING            COMMENT '下一个上升人',
    next_up_time                                DATETIME          COMMENT '下一次上升时间',
    cc_person                                   STRING            COMMENT '抄送人',
    operation_service_scene                     STRING            COMMENT '转运维的服务场景',
    responsible_should_assign                   STRING            COMMENT '转总负责人的队列，（总负责人的应派队列，应派队列必须24小时有人，否则需要有兜底功能的实派队列）',
    chat_group_id                               STRING            COMMENT '关联一键拉群的群号',
    first_assign_company_id                     BIGINT            COMMENT '第一次工单派单所属公司ID',
    feedback_channel                            BIGINT            COMMENT '反馈渠道:1(QQ群)|2(Slack群)|3(企业微信群)|4(微信群)|5(其他)',
    group_id                                    STRING            COMMENT '群号(一个uin可能有多个群)',
    source_channel                              BIGINT            COMMENT '建单来源:0(TSS)|1(QQ群)|2(Slack群)|3(企业微信)',
    customer_contact_info                       STRING            COMMENT '客户联系人qq号,多个qq要用;分开',
    question_category                           BIGINT            COMMENT '问题分类:1(客户报障类)|2(客户需求类)|3(客户咨询类)|4(主动服务类)',
    reason_category                             BIGINT            COMMENT '原因分类:1(我方产品或服务的原因)|2(客户自身原因如姿势不当等)|3(两方皆有责任皆可做优化)|4(第三方原因造成(如运营商))|5(未确定原因)',
    question_start_time                         DATETIME          COMMENT '问题发生时间',
    question_end_time                           DATETIME          COMMENT '问题结束时间',
    title                                       STRING            COMMENT '问题标题',
    reason                                      STRING            COMMENT '原因分析',
    upgrade_time                                DATETIME          COMMENT '升级时间',
    sales_supportor                             STRING            COMMENT '售后支持',
    trade                                       STRING            COMMENT '行业',
    name                                        STRING            COMMENT '客户名称',
    event_tss_id                                BIGINT            COMMENT 'tss_id',
    add_info_application_post                   BIGINT            COMMENT '申请补充时岗位',
    add_info_application_should_assign          BIGINT            COMMENT '申请补充时应派队列',
    add_info_application_operator               STRING            COMMENT '申请补充人',
    tapd_story_id                               STRING            COMMENT 'tapd需求单id',
    callcenter_skill_group                      STRING            COMMENT 'callcenter技能组',
    keyissues_id                                BIGINT            COMMENT '重点问题id',
    complaint                                   BIGINT            COMMENT '投诉',
    complaint_content                           STRING            COMMENT '投诉内容',
    ltc_name                                    STRING            COMMENT '项目名',
    product_version                             STRING            COMMENT '发现版本',
    severity                                    STRING            COMMENT '严重程度',
    short                                       STRING            COMMENT '产品简称',
    extern_status                               BIGINT            COMMENT '外部状态',
    caller                                      STRING            COMMENT '建单调用方',
    affected_customers                          STRING            COMMENT '受影响客户',
    customer_uid                                STRING            COMMENT '非腾讯云客户唯一标识',
    incident_manager                            STRING            COMMENT '事件经理',
    incident_manager_should_assign              BIGINT            COMMENT '事件经理应派队列',
    risk_control_first_time                     DATETIME          COMMENT '首次触发风控派单时间',
    risk_control_num                            BIGINT            COMMENT '风控推送次数',
    is_incident_manager_into                    BIGINT            COMMENT '事件经理是否查看工单详情 1-已查看 ，2-未查看'
) ENGINE=OLAP
PRIMARY KEY(`ticket_id`)
COMMENT "DWD层工单明细表"
DISTRIBUTED BY HASH(`ticket_id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "ticket_id"
);
