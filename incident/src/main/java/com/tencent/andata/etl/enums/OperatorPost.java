package com.tencent.andata.etl.enums;

import io.vavr.collection.List;

public enum OperatorPost {
    UNKNOWN(0, "其它"),
    SUPPLIER(1, "供应商"),
    FIRST_LINE(2, "一线"),
    SECOND_LINE(3, "1.5线"),
    OPERATION(4, "运维"),
    PRODUCTION_RESEARCH(5, "产研"),
    P_OPERATION(8, "售后运维"),
    P_TCE_PRODUCTION_RESEARCH(9, "TCE产研"),
    P_TBDS_PRODUCTION_RESEARCH(10, "TBDS产研"),
    P_VERTICAL_PRODUCTION_RESEARCH(11, "垂直产研"),
    P_TCE_ERXIAN(12, "二线");

    private final int code;
    private final String description;

    OperatorPost(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static OperatorPost of(int code) {
        return List.of(values())
                .filter(t -> t.code == code)
                .getOrElseThrow(() -> new IllegalArgumentException("Unknown operation type: " + code));
    }
}