package com.tencent.andata.etl.tablemap;

public class DimTIncidentTicketScenesMapping {

    public static String mysqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"t217_ticket_scenes\",\n"
            + "        \"fTable\":\"mysql_source_t217_ticket_scenes\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_t_incident_ticket_scenes\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_t_incident_ticket_scenes\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dim_incident_ticket_scenes\",\n"
            + "        \"fTable\":\"iceberg_sink_dim_incident_ticket_scenes\",\n"
            + "        \"primaryKey\":\"dim_id\"\n"
            + "    }\n"
            + "]";
}