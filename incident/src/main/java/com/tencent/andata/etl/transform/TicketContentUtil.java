package com.tencent.andata.etl.transform;


import com.tencent.andata.etl.utils.TicketOperationUtils;
import com.tencent.andata.utils.StreamUtils;
import java.io.IOException;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.functions.co.KeyedCoProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.jsoup.Jsoup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TicketContentUtil implements Serializable {


    private static final ObjectMapper mapper = new ObjectMapper();
    private static final String regex = "(?s)(猜您想问|猜您还想了解|猜您可能想了解).*<br />"
            + "|(?s)【发送者】.* 【时间】.*【内容】.*"
            + "|(?s)(服务场景从|申请结单|坐席操作结束会话).*?>.*?>.*?"
            + "|(?s)这是一条引用.*([:：])([\"“']).*([\"”'])-{6}"
            + "|(?s)「.*」-( -)*"
            + "|(?s)([\"“']).*([\"”'])*-{6}"
            + "|(?s)https*://[^一-龥@]*"
            + "|(?s)@.*\\(.*\\)"
            + "|(?s)@[^一-龥]*([  ])"
            + "|(?s)@[一-龥]*([  ])"
            + "|(?s)emoji表情"
            + "|(?s)\\[.{1,4}]"
            + "|(?s)\\[img src=.*?"
            + "|(?s)业务影响程度:";

    private static final Pattern pattern1 = Pattern.compile(regex);

    private static final String[] REGEX_LIST = {
            "客户结单",
            "同步客户进展",
            "长时间未回复",
            "会话还没有发言",
            "会话已生成工单",
            "客户不同意结单",
            "客户确认结束会话",
            "小助手已为您创建工单",
            "客户7天未结单，系统自动结单",
            "客户超3天未处理，系统自动结单",
            "客户3天未补充信息，系统转待确认",
            "已为你催单,我们的客服会尽快答复你!",
            "用户要求尽快处理，请及时响应，谢谢!",
            "您可以尝试用一句话描述下遇到的产品问题",
            "如果以上不是您所要咨询的问题，您可以尝试",
            "您也可以用产品名称+问题描述展开说说您的问题",
            "您好，如群内沟通，助手就先将工单待补充了哈，您有问题随时联系助手",
            "工程师在排查处理问题中，也可能会请求您先备份数据，还请您给予必要的协助和支持，谢谢！",
            "温馨提示：如果您在此单中有提供Sec.*Key或账号密码等机密信息，请您在结单后尽快修改，以免造成不必要的损失。",
            "尊敬的客户：您好，由于您长时间未反馈信息，我们暂时将您的问题修改为已结单。若有疑问您可以重新提交工单，谢谢。",
            "尊敬的客户：您好，由于您长时间未反馈信息，我们暂时将您的问题修改为已结单。若有疑问可以重新在您的专属QQ群中反馈问题，谢谢。",
            "尊敬的客户：您好，由于您长时间未反馈信息，我们暂时将您的问题修改为待您确认状态，如此问题重现您可以继续留言。如您不再回复，7天后工单会自动关闭。",
            "尊敬的用户，本次售后故障排查存在一定数据安全风险，为防止您的数据丢失，请您务必确认数据已经备份。建议您在腾讯云控制台制作实例的快照或镜像，以保护您的数据安全。",
            "尊敬的用户，本次售后故障排查存在一定数据安全风险，为防止您的数据丢失，建议您先将数据进行备份，以保护您的数据安全。工程师在排查处理问题中，也可能会请求您先备份数据，还请您给予必要的协助和支持，谢谢！",
            "尊敬的用户，本次售后故障排查存在一定数据安全风险，为防止您的数据丢失，请您务必确认数据已经备份。建议您在腾讯云控制台创建实例备份，以保护您的数据安全"
    };

    private static final Pattern[] pattern2 = new Pattern[REGEX_LIST.length];
    private static final Map<Class<?>, Object> DEFAULT_VALUES = new HashMap<>();
    private final static DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    static {
        for (int i = 0; i < REGEX_LIST.length; i++) {
            pattern2[i] = Pattern.compile(REGEX_LIST[i]);
        }
    }

    static {
        DEFAULT_VALUES.put(boolean.class, false);
        DEFAULT_VALUES.put(byte.class, (byte) 0);
        DEFAULT_VALUES.put(short.class, (short) 0);
        DEFAULT_VALUES.put(int.class, 0);
        DEFAULT_VALUES.put(long.class, 0L);
        DEFAULT_VALUES.put(float.class, 0.0f);
        DEFAULT_VALUES.put(double.class, 0.0);
        DEFAULT_VALUES.put(char.class, '\u0000');
        DEFAULT_VALUES.put(Boolean.class, false);
        DEFAULT_VALUES.put(Byte.class, (byte) 0);
        DEFAULT_VALUES.put(Short.class, (short) 0);
        DEFAULT_VALUES.put(Integer.class, 0);
        DEFAULT_VALUES.put(Long.class, 0L);
        DEFAULT_VALUES.put(Float.class, 0.0f);
        DEFAULT_VALUES.put(Double.class, 0.0);
        DEFAULT_VALUES.put(Character.class, '\u0000');
    }


    /**
     * 更新对象中未设置的字段
     *
     * @param o1 待更新对象
     * @param o2 待更新字段的对象
     * @throws IllegalAccessException 非法访问异常
     */
    public static void updateUnsetFields(Object o1, Object o2) throws IllegalAccessException {
        if (o1 == null || o2 == null) {
            throw new IllegalArgumentException("Objects must not be null");
        }

        Class<?> clazz = o1.getClass();
        if (!clazz.equals(o2.getClass())) {
            throw new IllegalArgumentException("Objects must be of the same type");
        }

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Object value1 = field.get(o1);
            Object value2 = field.get(o2);

            // Recursively update nested objects
            if (!field.getType().isPrimitive() && !DEFAULT_VALUES.containsKey(field.getType())) {
                if (value1 != null && value2 != null) {
                    updateUnsetFields(value1, value2);
                }
            }

            if (isDefaultValue(value1, field.getType()) && !isDefaultValue(value2, field.getType())) {
                field.set(o1, value2);
            }
        }
    }

    private static boolean isDefaultValue(Object value, Class<?> type) {
        if (value == null) {
            return true;
        }
        Object defaultValue = DEFAULT_VALUES.get(type);
        return value.equals(defaultValue);

    }

    public static String replaceHTMLTag(String message) {
        return Jsoup.parse(message).text();
    }

    public static String contentFilter(String input) {
        if (StringUtils.equals(input, "")) {
            return "";
        }

        // 正则匹配
        String out1 = replace(input);
        Matcher matcher = pattern1.matcher(out1);
        return replaceHTMLTag(matcher.replaceAll(""));
    }

    public static String replace(String input) {
        StringBuilder outputString = new StringBuilder(input);
        for (Pattern pattern : pattern2) {
            Matcher matcher = pattern.matcher(outputString);
            outputString = new StringBuilder(matcher.replaceAll(""));
        }
        return outputString.toString();
    }

    @Data
    @Builder
    public static class TicketItem {

        private String id;
        private String content;
        private String sourceId;
        private String title;
        private String createTime;
        private String updateTime;
        private boolean deleted;
        private String href;
        private String sourceType;
        private String sourceName;
        private Info info;
        private Extend extend;
    }

    @Data
    @Builder
    public static class Info {

        private int serviceChannels;
        private int serviceScenes;
        private int status;
        private String currentOperator;
        private int post;
        private long uin;
        private long ownerUin;
        private String appId;
        private int shouldAssign;
        private int factAssign;
        private int companyId;
        private String responsible;
        private int priority;
        private int language;
        private List<String> operators;
        private int serviceRate;

        private int firstAssignCompanyId;

        //204表相关字段
        private Integer isTransferProductionResearch;
        private List<Integer> handledCompanies;
        private String closeTime;
    }


    @Data
    @Builder
    public static class Extend {

        private String customerContactInfo;
        private String applyCloseTester;
        private String salesSupportor;
        private String ownerName;
        private String name;
        private String ltcName;
    }

    public static class CleanTicketOperationMapFun implements MapFunction<Row, Tuple2<String, TicketItem>> {

        @Override
        public Tuple2<String, TicketItem> map(Row row) throws Exception {
            Info info = Info
                    .builder()
                    .serviceChannels(row.getFieldAs("service_channel"))
                    .serviceScenes(row.getFieldAs("service_scene_checked"))
                    .status(row.getFieldAs("status"))
                    .currentOperator(row.getFieldAs("current_operator"))
                    .post(row.getFieldAs("post"))
                    .appId(row.getFieldAs("app_id"))
                    .uin(row.getFieldAs("uin"))
                    .ownerUin(row.getFieldAs("owner_uin"))
                    .shouldAssign(row.getFieldAs("should_assign"))
                    .factAssign(row.getFieldAs("fact_assign"))
                    .companyId(row.getFieldAs("company_id"))
                    .responsible(row.getFieldAs("responsible"))
                    .priority(row.getFieldAs("priority"))
                    .language(row.getFieldAs("language"))
                    .operators(Arrays.asList(row.<String>getFieldAs("operators").
                            replaceAll("[\\[\\]\"]", "")
                            .split(",")))
                    .serviceRate(row.getFieldAs("service_rate"))
                    .firstAssignCompanyId(row.getFieldAs("first_assign_company_id"))
                    .build();

            Extend extend = Extend
                    .builder()
                    .customerContactInfo(row.getFieldAs("customer_contact_info"))
                    .salesSupportor(row.getFieldAs("sales_supportor"))
                    .ltcName(row.getFieldAs("ltc_name"))
                    .name(row.getFieldAs("name"))
                    .ownerName(row.getFieldAs("owner_name"))
                    .build();

            TicketItem item = TicketItem
                    .builder()
                    .id(row.getFieldAs("id"))
                    .content(row.getFieldAs("content"))
                    .sourceId(row.getFieldAs("_ticket_id"))
                    .title(replaceHTMLTag(row.getFieldAs("title")))
                    .createTime(DATE_TIME_FORMATTER.format(row.getFieldAs("_create_time")))
                    .updateTime(DATE_TIME_FORMATTER.format(row.getFieldAs("_update_time")))
                    .deleted(false)
                    .href(row.getFieldAs("short_url"))
                    .sourceType("incident_ticket")
                    .sourceName("smart_assist")
                    .info(info)
                    .extend(extend)
                    .build();

            return new Tuple2<>(row.getFieldAs("id"), item);
        }
    }

    public static class ExtraDataTransMapFun implements MapFunction<Row, Tuple2<String, TicketItem>> {

        @Override
        public Tuple2<String, TicketItem> map(Row row) throws Exception {
            String handled = row.getFieldAs("handled_companies");
            String originHandle = StringUtils.equalsIgnoreCase(handled, "null") ? "" : handled;
            List<Integer> handledCompanies = new ArrayList<>();
            String[] splitList = originHandle.split("]\\[");
            if (splitList.length > 0) {
                handledCompanies = Arrays
                        .stream(splitList)
                        .filter(s -> !StringUtils.isBlank(s) && !s.equals("null"))
                        .map(s -> s.replaceAll("[\\[\\]]", "").trim())
                        .filter(s -> !StringUtils.isBlank(s))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            }

            Info info = Info
                    .builder()
                    .handledCompanies(handledCompanies)
                    .closeTime(DATE_TIME_FORMATTER.format(row.getFieldAs("close_time")))
                    .isTransferProductionResearch(row.<Integer>getFieldAs("is_transfer_production_research"))
                    .build();

            Extend extend = Extend
                    .builder()
                    .applyCloseTester(row.getFieldAs("apply_close_tester"))
                    .build();

            TicketItem item = TicketItem
                    .builder()
                    .id(row.getFieldAs("id"))
                    .sourceId(row.getFieldAs("_ticket_id"))
                    .sourceType("incident_ticket")
                    .sourceName("smart_assist")
                    .info(info)
                    .extend(extend)
                    .build();
            return new Tuple2<>(row.getFieldAs("id"), item);
        }
    }

    public static class TicketContentInfoMapFunction implements MapFunction<Row, Row> {

        private static final Logger LOG = LoggerFactory.getLogger(TicketContentInfoMapFunction.class);


        @Override
        public Row map(Row row) throws Exception {
            // 工单流水中交互内容
            StringBuilder content = new StringBuilder();

            Set<String> operators = new HashSet<>(); // 工单经手人

            List<JsonNode> ticketOperations = StreamUtils
                    .streamOf(mapper.readValue(row.<String>getFieldAs("operation_data"), ArrayNode.class))
                    .collect(Collectors.toList());

            for (JsonNode operation : ticketOperations) {
                final String responsible = operation.get("responsible").asText();
                final String nextOperator = operation.get("next_operator").asText();
                final String currentOperator = operation.get("current_operator").asText();
                final String nextResponsible = operation.get("next_responsible").asText();

                // 工单经手人计算逻辑
                operators.add(responsible);
                operators.add(nextOperator);
                operators.add(currentOperator);
                operators.add(nextResponsible);

                String innerReply = operation.get("inner_reply").asText().replaceAll("NULL|null", "");
                String externReply = operation.get("extern_reply").asText().replaceAll("NULL|null", "");

                // 直接处理内容过滤并拼接到content
                String filteredContent = contentFilter(innerReply + externReply);
                content.append(filteredContent);
            }

            // 去除空字符串和 SYSTEM
            operators = operators.stream()
                    .distinct()
                    .filter(StringUtils::isNotBlank)
                    .filter(x -> !x.equalsIgnoreCase("SYSTEM"))
                    .map(String::trim)
                    .collect(Collectors.toSet());

            row.setField("content", content.toString());
            row.setField("operators", mapper.writeValueAsString(operators));
            return row;
        }
    }

    public static class CompleteTicketItemProcessFunction extends KeyedCoProcessFunction<String, Tuple2<String, TicketItem>, Tuple2<String, TicketItem>, TicketItem> {

        private ValueState<TicketItem> ticketState;
        private ValueState<TicketItem> extraState;

        @Override
        public void open(Configuration parameters) {
            ticketState = getRuntimeContext().getState(new ValueStateDescriptor<>("ticket-state", TicketItem.class));

            extraState = getRuntimeContext().getState(new ValueStateDescriptor<>("extra-state", TicketItem.class));

        }

        @Override
        public void processElement1(Tuple2<String, TicketItem> ticketValue, Context ctx,
                Collector<TicketItem> out) throws IOException, IllegalAccessException {

            //在201工单主流中，判断204工单扩展流是否有数据
            if (extraState.value() == null) {
                //工单扩展数据未到，先把201工单数据放入状态， 并下发201工单数据
                ticketState.update(ticketValue.f1);
                //out.collect(ticketValue.f1);
            } else {
                //工单扩展数据已到，更新pojo
                //更新info相关字段
                TicketItem item = ticketValue.f1;
                updateUnsetFields(item, extraState.value());
                out.collect(item);
            }
        }


        @Override
        public void processElement2(Tuple2<String, TicketItem> extraValue, Context ctx,
                Collector<TicketItem> out) throws IOException, IllegalAccessException {
            //在204工单扩展流中，判断201工单主流是否有数据
            if (ticketState.value() == null) {
                //201工单主流数据未到，先把204工单扩展数据放入状态， 并下发204工单扩展数据
                extraState.update(extraValue.f1);
                //out.collect(extraValue.f1);
            } else {
                //工单主数据已到，更新pojo
                //更新item相关字段
                TicketItem item = extraValue.f1;
                updateUnsetFields(item, ticketState.value());
                out.collect(item);
                clearStateByStatus(item);
            }
        }

        private void clearStateByStatus(TicketItem item) {
            //已结单就清除状态
            if (3 == item.getInfo().getStatus()) {
                ticketState.clear();
                extraState.clear();
            }
        }
    }


    public static class TicketGetOperationMapFunction implements MapFunction<Tuple3<Row, String, String>, Row> {

        @Override
        public Row map(Tuple3<Row, String, String> row3) throws Exception {
            Row row = row3.f0;
            Long ticketId = Long.parseLong(row.getFieldAs("_ticket_id"));
            JsonNode TicketOperation = TicketOperationUtils.getTicketOperation(ticketId);
            row.setField("operation_data", mapper.writeValueAsString(TicketOperation));
            return row;
        }
    }
}