package com.tencent.andata.etl.sql;

public class DimTIncidentTicketScenesSql {

    public static final String FLATTEN_TICKET_SCENES_SQL = ""
            + "WITH\n"
            + "  level1 AS (\n"
            + "    SELECT\n"
            + "      id AS service_scene_level1_id,\n"
            + "      name AS service_scene_level1_name,\n"
            + "      '' service_scene_level2_id,\n"
            + "      '' service_scene_level2_name,\n"
            + "      '' service_scene_level3_id,\n"
            + "      '' service_scene_level3_name,\n"
            + "      '' service_scene_level4_id,\n"
            + "      '' service_scene_level4_name\n"
            + "    FROM mysql_source_t217_ticket_scenes\n"
            + "    WHERE parent_id = 1\n"
            + "  ),\n"
            + "  level2 AS (\n"
            + "    SELECT\n"
            + "      b.service_scene_level1_id,\n"
            + "      b.service_scene_level1_name,\n"
            + "      a.id AS service_scene_level2_id,\n"
            + "      a.name AS service_scene_level2_name,\n"
            + "      '' service_scene_level3_id,\n"
            + "      '' service_scene_level3_name,\n"
            + "      '' service_scene_level4_id,\n"
            + "      '' service_scene_level4_name\n"
            + "    FROM level1 AS b\n"
            + "    LEFT JOIN mysql_source_t217_ticket_scenes AS a ON a.parent_id = b.service_scene_level1_id\n"
            + "  ),\n"
            + "  level3 AS (\n"
            + "    SELECT\n"
            + "      b.service_scene_level1_id,\n"
            + "      b.service_scene_level1_name,\n"
            + "      b.service_scene_level2_id,\n"
            + "      b.service_scene_level2_name,\n"
            + "      a.id AS service_scene_level3_id,\n"
            + "      a.name AS service_scene_level3_name,\n"
            + "      '' service_scene_level4_id,\n"
            + "      '' service_scene_level4_name\n"
            + "    FROM level2 AS b\n"
            + "    LEFT JOIN mysql_source_t217_ticket_scenes AS a ON a.parent_id = b.service_scene_level2_id\n"
            + "  ),\n"
            + "  level4 AS (\n"
            + "    SELECT\n"
            + "      COALESCE(a.id, b.service_scene_level3_id, b.service_scene_level2_id, b.service_scene_level1_id) "
            + "      AS flag_id,\n"
            + "      b.service_scene_level1_id,\n"
            + "      b.service_scene_level1_name,\n"
            + "      b.service_scene_level2_id,\n"
            + "      b.service_scene_level2_name,\n"
            + "      b.service_scene_level3_id,\n"
            + "      b.service_scene_level3_name,\n"
            + "      a.id AS service_scene_level4_id,\n"
            + "      a.name AS service_scene_level4_name\n"
            + "    FROM level3 AS b\n"
            + "    LEFT JOIN mysql_source_t217_ticket_scenes AS a ON a.parent_id = b.service_scene_level3_id\n"
            + "  )\n"
            + "SELECT\n"
            + "  CASE\n"
            + "    WHEN CAST(flag_id AS BIGINT)=service_scene_level4_id THEN CAST(service_scene_level4_id AS BIGINT)\n"
            + "    WHEN CAST(flag_id AS BIGINT)=service_scene_level3_id THEN CAST(service_scene_level3_id AS BIGINT)\n"
            + "    WHEN CAST(flag_id AS BIGINT)=service_scene_level2_id THEN CAST(service_scene_level2_id AS BIGINT)\n"
            + "    WHEN CAST(flag_id AS BIGINT)=service_scene_level1_id THEN CAST(service_scene_level1_id AS BIGINT)\n"
            + "  END AS dim_id,\n"
            + "  CAST(service_scene_level1_id AS BIGINT) AS service_scene_level1_id,\n"
            + "  CAST(service_scene_level2_id AS BIGINT) AS service_scene_level2_id,\n"
            + "  CAST(service_scene_level3_id AS BIGINT) AS service_scene_level3_id,\n"
            + "  CAST(service_scene_level4_id AS BIGINT) AS service_scene_level4_id,\n"
            + "  service_scene_level1_name,\n"
            + "  service_scene_level2_name,\n"
            + "  service_scene_level3_name,\n"
            + "  service_scene_level4_name\n"
            + "FROM level4\n"
            + "\n"
            + "UNION\n"
            + "\n"
            + "SELECT\n"
            + "  CAST(service_scene_level1_id AS BIGINT) AS dim_id,\n"
            + "  CAST(service_scene_level1_id AS BIGINT) AS service_scene_level1_id,\n"
            + "  CAST(service_scene_level2_id AS BIGINT) AS service_scene_level2_id,\n"
            + "  CAST(service_scene_level3_id AS BIGINT) AS service_scene_level3_id,\n"
            + "  CAST(service_scene_level4_id AS BIGINT) AS service_scene_level4_id,\n"
            + "  service_scene_level1_name,\n"
            + "  service_scene_level2_name,\n"
            + "  service_scene_level3_name,\n"
            + "  service_scene_level4_name\n"
            + "FROM level1\n"
            + "WHERE CAST(service_scene_level1_id AS BIGINT) IS NOT NULL\n"
            + "\n"
            + "UNION\n"
            + "\n"
            + "SELECT\n"
            + "  CAST(service_scene_level2_id AS BIGINT) AS dim_id,\n"
            + "  CAST(service_scene_level1_id AS BIGINT) AS service_scene_level1_id,\n"
            + "  CAST(service_scene_level2_id AS BIGINT) AS service_scene_level2_id,\n"
            + "  CAST(service_scene_level3_id AS BIGINT) AS service_scene_level3_id,\n"
            + "  CAST(service_scene_level4_id AS BIGINT) AS service_scene_level4_id,\n"
            + "  service_scene_level1_name,\n"
            + "  service_scene_level2_name,\n"
            + "  service_scene_level3_name,\n"
            + "  service_scene_level4_name\n"
            + "FROM level2\n"
            + "WHERE CAST(service_scene_level2_id AS BIGINT) IS NOT NULL\n"
            + "\n"
            + "UNION\n"
            + "\n"
            + "SELECT\n"
            + "  CAST(service_scene_level3_id AS BIGINT) AS dim_id,\n"
            + "  CAST(service_scene_level1_id AS BIGINT) AS service_scene_level1_id,\n"
            + "  CAST(service_scene_level2_id AS BIGINT) AS service_scene_level2_id,\n"
            + "  CAST(service_scene_level3_id AS BIGINT) AS service_scene_level3_id,\n"
            + "  CAST(service_scene_level4_id AS BIGINT) AS service_scene_level4_id,\n"
            + "  service_scene_level1_name,\n"
            + "  service_scene_level2_name,\n"
            + "  service_scene_level3_name,\n"
            + "  service_scene_level4_name\n"
            + "FROM level3\n"
            + "WHERE CAST(service_scene_level3_id AS BIGINT) IS NOT NULL\n";
}