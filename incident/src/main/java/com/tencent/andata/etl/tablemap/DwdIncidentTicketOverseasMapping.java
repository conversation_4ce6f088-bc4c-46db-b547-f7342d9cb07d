package com.tencent.andata.etl.tablemap;

public class DwdIncidentTicketOverseasMapping {

    public static String mysqlTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"t102_duty\",\n"
            + "        \"fTable\": \"mysql_source_t102_duty\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t201_ticket\",\n"
            + "        \"fTable\":\"mysql_source_t201_ticket\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t201_ticket_extra\",\n"
            + "        \"fTable\":\"mysql_source_t201_ticket_extra\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t204_ticket_operation_extra\",\n"
            + "        \"fTable\": \"mysql_source_t204_ticket_operation_extra\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t202_ticket_operation\",\n"
            + "        \"fTable\": \"mysql_source_t202_ticket_operation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t210_ticket_alarm_record\",\n"
            + "        \"fTable\": \"mysql_source_t210_ticket_alarm_record\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t006_staff\",\n"
            + "        \"fTable\":\"mysql_source_t006_staff\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t903_company_user\",\n"
            + "        \"fTable\":\"mysql_source_t903_company_user\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t009_staff_post\",\n"
            + "        \"fTable\":\"mysql_source_t009_staff_post\"\n"
            + "    }\n"
            + "]";

    public static String mysqlAccountTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_users\",\n"
            + "        \"fTable\":\"mysql_source_t_users\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_duty\",\n"
            + "        \"fTable\": \"iceberg_sink_dim_incident_duty\",\n"
            + "        \"primaryKey\": \"duty_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_operation\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_operation\",\n"
            + "        \"primaryKey\": \"operation_id,operate_time\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_extra\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket_extra\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_operation_extra\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_operation_extra\",\n"
            + "        \"primaryKey\": \"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_alarm_record\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_alarm_record\",\n"
            + "        \"primaryKey\": \"id,create_time\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"ods_t_users\",\n"
            + "        \"fTable\":\"iceberg_sink_ods_t_users\",\n"
            + "        \"primaryKey\":\"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dim_incident_staff_post\",\n"
            + "        \"fTable\":\"iceberg_sink_dim_incident_staff_post\",\n"
            + "        \"primaryKey\":\"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"ods_t006_staff\",\n"
            + "        \"fTable\":\"iceberg_sink_ods_t006_staff\",\n"
            + "        \"primaryKey\":\"user_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"ods_t903_company_user\",\n"
            + "        \"fTable\":\"iceberg_sink_ods_t903_company_user\",\n"
            + "        \"primaryKey\":\"user_id\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket_operation\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_operation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket_extra\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_extra\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket_operation_extra\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_operation_extra\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket_alarm_record\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_alarm_record\"\n"
            + "    }\n"
            + "]";
}