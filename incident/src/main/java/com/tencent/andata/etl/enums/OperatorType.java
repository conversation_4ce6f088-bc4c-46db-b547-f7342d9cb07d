package com.tencent.andata.etl.enums;

import io.vavr.collection.List;

public enum OperatorType {
    CUSTOMER(1), CUSTOMER_SERVICE(2), SYSTEM(3), XINGYUN_SYSTEM(4),
    FAULT_NOTIFY(5), INCIDENT_MANAGER(6), TOOL(7);

    private final int type;

    OperatorType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static OperatorType fromType(int type) {
        return List.of(values())
                .filter(t -> t.type == type)
                .getOrElseThrow(() -> new IllegalArgumentException("Unknown operator type: " + type));
    }
}
