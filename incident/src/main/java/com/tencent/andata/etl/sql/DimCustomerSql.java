package com.tencent.andata.etl.sql;

public class DimCustomerSql {

    public static final String QUERY_CUSTOMER_SQL = ""
            + "SELECT\n"
            + "    CONCAT(CAST(uin AS STRING), '-', CAST(owner_uin AS STRING)) AS pk,\n"
            + "    uin,\n"
            + "    owner_uin,\n"
            + "    owner_name,\n"
            + "    region_code,\n"
            + "    phone_number,\n"
            + "    customer_level,\n"
            + "    sales_supportor,\n"
            + "    is_valet,\n"
            + "    is_agent,\n"
            + "    is_cooperator_user,\n"
            + "    appid,\n"
            + "    create_time,\n"
            + "    modify_time,\n"
            + "    agent_uin,\n"
            + "    customer_type,\n"
            + "    service_group,\n"
            + "    nickname,\n"
            + "    area,\n"
            + "    trade\n"
            + "FROM mysql_source_t008_customer";
}
