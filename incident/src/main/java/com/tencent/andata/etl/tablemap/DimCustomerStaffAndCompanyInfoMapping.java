package com.tencent.andata.etl.tablemap;

public class DimCustomerStaffAndCompanyInfoMapping {

    public static String mysqlAccountTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_users\",\n"
            + "        \"fTable\":\"mysql_source_t_users\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_companies\",\n"
            + "        \"fTable\":\"mysql_source_t_companies\"\n"
            + "    }\n"
            + "]";

    public static String mysqlWorkTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"t006_staff\",\n"
            + "        \"fTable\":\"mysql_source_t006_staff\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t903_company_user\",\n"
            + "        \"fTable\":\"mysql_source_t903_company_user\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_customer_staff_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_customer_staff_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_company_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_company_info\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dim_customer_staff_info\",\n"
            + "        \"fTable\":\"iceberg_sink_dim_customer_staff_info\",\n"
            + "        \"primaryKey\":\"uid\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dim_company_info\",\n"
            + "        \"fTable\":\"iceberg_sink_dim_company_info\",\n"
            + "        \"primaryKey\":\"company_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"ods_t_users\",\n"
            + "        \"fTable\":\"iceberg_sink_ods_t_users\",\n"
            + "        \"primaryKey\":\"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"ods_t006_staff\",\n"
            + "        \"fTable\":\"iceberg_sink_ods_t006_staff\",\n"
            + "        \"primaryKey\":\"user_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"ods_t903_company_user\",\n"
            + "        \"fTable\":\"iceberg_sink_ods_t903_company_user\",\n"
            + "        \"primaryKey\":\"user_id\"\n"
            + "    }\n"
            + "]";

    public static String starRocksTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dim_customer_staff_info\",\n"
            + "        \"fTable\":\"flink_starrocks_dim_customer_staff_info\",\n"
            + "        \"primaryKey\": \"uid\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_company_info\",\n"
            + "        \"fTable\": \"flink_starrocks_dim_company_info\",\n"
            + "        \"primaryKey\": \"company_id\"\n"
            + "    }\n"
            + "]";
}
