package com.tencent.andata.etl.scene.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


@Data
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DimServiceScenes implements Serializable {

    // 主键
    public long dimId;
    // 一级归档id
    public long serviceSceneLevel1Id;
    // 一级归档名称
    public String serviceSceneLevel1Name;
    // 二级归档id
    public long serviceSceneLevel2Id;
    // 二级归档名称
    public String serviceSceneLevel2Name;
    // 三级归档id
    public long serviceSceneLevel3Id;
    // 三级归档名称
    public String serviceSceneLevel3Name;
    // 四级归档id
    public long serviceSceneLevel4Id;
    // 四级归档名称
    public String serviceSceneLevel4Name;
    // 场景状态, 0-挂起, 1-启用
    public int status;
    // 删除状态, 1-删除, 0-正常
    public int disabled;
    // obs成本id
    public String obsId;
    //标签形态
    public String property;
    // 更新时间
    public Timestamp updateTime;
    // 更改时间
    public Timestamp modifyTime;


    public static DimServiceScenes buildDimServiceScenes(List<Scene> scenePath) {
        DimServiceScenesBuilder builder = DimServiceScenes.builder();
        int len = scenePath.size();
        List<String> obsIds = scenePath.stream().map(Scene::getObsId).collect(Collectors.toList());
        List<Integer> status = scenePath.stream().map(Scene::getStatus).collect(Collectors.toList());
        List<Integer> disabled = scenePath.stream().map(Scene::getDisabled).collect(Collectors.toList());
        List<String> properties = scenePath.stream().map(Scene::getProperty).collect(Collectors.toList());
        List<Timestamp> updateTimeLst = scenePath.stream().map(Scene::getUpdateTime).collect(Collectors.toList());

        // 设置二到四级归档信息
        try {
            // 展开归档ID，dimId，归档名称
            builder.dimId(scenePath.get(len - 1).id);
            builder.modifyTime(scenePath.get(len - 1).modifyTime);
            builder.serviceSceneLevel1Id(scenePath.get(1).id);
            builder.serviceSceneLevel1Name(scenePath.get(1).name);
            builder.serviceSceneLevel2Id(scenePath.get(2).id);
            builder.serviceSceneLevel2Name(scenePath.get(2).name);
            builder.serviceSceneLevel3Id(scenePath.get(3).id);
            builder.serviceSceneLevel3Name(scenePath.get(3).name);
            builder.serviceSceneLevel4Id(scenePath.get(4).id);
            builder.serviceSceneLevel4Name(scenePath.get(4).name);
        } catch (Exception ex) {
            log.warn("当前归档id: {}，只展开到: {}级", scenePath.get(len - 1).id, len);
        }

        builder.status(status.stream().reduce((x, y) -> x * y).orElse(-1));
        builder.disabled(disabled.stream().reduce((x, y) -> x * y).orElse(-1));
        builder.updateTime(updateTimeLst.stream().max(Timestamp::compareTo).orElse(null));
        builder.obsId(obsIds.stream().filter(StringUtils::isNotEmpty).findAny().orElse(""));
        builder.property(properties.stream().filter(StringUtils::isNotEmpty).findAny().orElse(""));

        return builder.build();
    }
}
