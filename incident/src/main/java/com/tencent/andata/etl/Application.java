package com.tencent.andata.etl;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

import com.tencent.andata.etl.dim.DimCustomer;
import com.tencent.andata.etl.dwd.BigCustomer;
import com.tencent.andata.etl.dwd.DwdIncidentTicketOperationDetail;
import com.tencent.andata.etl.dwd.DwdIncidentTicketOverseas;
import com.tencent.andata.etl.dwm.DwmIncidentTicketStatistic;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.udf.GetJsonObject;
import com.tencent.andata.utils.udf.SimpleStringTransform;
import com.tencent.andata.utils.udf.ToJson;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;

public class Application {

    /**
     * 在一个main方法中运行多个flink etl 任务
     *
     * @param args args[0] = iceberg db name, args[1] = pg db name
     */
    public static void main(String[] args) throws Exception {
        FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);

        flinkEnv.env().setRestartStrategy(RestartStrategies.failureRateRestart(
                50, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS) // delay
        ));

        // flinkEnv.env().getConfig().enableObjectReuse();
        final IcebergCatalogReader catalog = new IcebergCatalogReader();

        // String parallelism = parameterTool.get("parallelism", "1");
        // table env config
        Configuration configuration = flinkEnv.streamTEnv().getConfig().getConfiguration();

        // 限制 taskName 的长度
        configuration.setString("pipeline.task-name-length", "25");
        // 禁用算子链
        // configuration.setString("pipeline.operator-chaining", "true");
        //开启微批模式
        configuration.setString("table.exec.mini-batch.size", "5000");
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "3 s");

        // 设置时区
        // configuration.setString("table.local-time-zone", "UTC");

        // 状态保留3天
        // configuration.setString("table.exec.state.ttl", "259200000");
        configuration.setString("execution.runtime-mode", "streaming");
        configuration.setString("execution.checkpointing.interval", "30s");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        configuration.setString("table.exec.sink.upsert-materialize", "NONE");
        configuration.setString("table.dynamic-table-options.enabled", "true");
        configuration.setString("table.exec.legacy-cast-behaviour", "enabled");

        // 关闭iceberg source的自动推断并行度
        configuration.setString("table.exec.iceberg.infer-source-parallelism", "false");
        // configuration.setString("table.exec.resource.default-parallelism", parallelism);

        // 优化join性能
        configuration.setString("table.optimizer.reuse-source-enabled", "true");
        configuration.setString("table.optimizer.join-reorder-enabled", "true");
        configuration.setString("table.optimizer.reuse-sub-plan-enabled", "true");
        configuration.setString("table.optimizer.multiple-input-enabled", "true");
        configuration.setString("table.exec.disabled-operators", "NestedLoopJoin");
        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");
        configuration.setString("table.exec.simplify-operator-name-enabled", "true");
        configuration.setString("table.optimizer.join.broadcast-threshold", "268435456");
        configuration.setString("table.optimizer.source.aggregate-pushdown-enabled", "true");
        configuration.setString("table.optimizer.source.predicate-pushdown-enabled", "true");

        // get iceberg db name and pg db name from args
        ParameterTool parameterTool = ParameterTool.fromArgs(args);

        String pgDbName = parameterTool.get("pgDbName");
        String icebergDbName = parameterTool.get("icebergDbName");
        String startSnapshot = parameterTool.get("startSnapshot");
        String streamingStr = parameterTool.get("streaming");
        String parallelism = parameterTool.get("parallelism", "1");
        boolean streaming = Boolean.parseBoolean(streamingStr);

        // 注册hive udf
        flinkEnv.hiveModuleV2().registryHiveUDF("to_json", ToJson.class.getName());
        // 注册自定义udf
        flinkEnv.streamTEnv().createTemporaryFunction("simple_string_trans", SimpleStringTransform.class);
        flinkEnv.streamTEnv().createFunction("get_json_object", GetJsonObject.class);

        // instantiate the DWD ETL
        List<Object> appList = new ArrayList<>();
        String subApplicationStr = parameterTool.get("subApplication");

        // 设置flink应用程序名称
        configuration.setString("pipeline.name", "Incident Ticket Application");
        switch (subApplicationStr) {
            case "INCIDENT_DWD_OVERSEAS":
                appList.add(DwdIncidentTicketOverseas
                        .builder()
                        .icebergDbName(icebergDbName)
                        .pgDbName(pgDbName)
                        .build());
                break;
            case "INCIDENT_OPRATION_DETAIL":
                appList.add(DwdIncidentTicketOperationDetail
                                .builder()
                                .icebergDbName(icebergDbName)
                                .pgDbName(pgDbName)
                                .startSnapshot(Long.parseLong(startSnapshot))
                                .streaming(streaming)
                                .parallelism(parallelism)
                                .build()
                );
                break;
            case "INCIDENT_TICKET_STATISTIC":
                appList.add(DwmIncidentTicketStatistic
                        .builder()
                        .icebergDbName(icebergDbName)
                        .pgDbName(pgDbName)
                        .build());
                break;
            case "BIG_CUS":
                appList.add(BigCustomer
                        .builder()
                        .icebergDbName(icebergDbName)
                        .build());
                appList.add(DimCustomer
                        .builder()
                        .icebergDbName(icebergDbName)
                        .pgDbName(pgDbName)
                        .build());
                break;
        }

        // 遍历appList，调用run方法
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", flinkEnv, catalog)));

        // execute the sql statements
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("Incident Application");
    }
}