package com.tencent.andata.etl.sql;

public class BigCustomerSql {

    public static String BIG_CUSTOMER_AND_CHANNEL =
            "SELECT  \n"
                    + "  COALESCE(a.uin, b.uin) AS uin\n"
                    + "  ,IF(b.id IS NULL, 0, b.id) AS b_id\n"
                    + "  ,CAST(IF(COALESCE(a.create_time, TIMESTAMP '1970-01-01 00:00:00') > COALESCE(b.created_at, TIMESTAMP '1970-01-01 00:00:00'), a.create_time, b.created_at) AS TIMESTAMP_LTZ) AS create_time\n"
                    + "  ,CAST(IF(COALESCE(a.modify_time, TIMESTAMP '1970-01-01 00:00:00') > COALESCE(b.updated_at, TIMESTAMP '1970-01-01 00:00:00'), a.modify_time, b.updated_at) AS TIMESTAMP_LTZ) AS update_time\n"
                    + "  ,b.`channel`\n"
                    + "  ,b.channel_id\n"
                    + "  ,IF(b.name IS NULL, a.name, b.name) AS name\n"
                    + "  ,b.is_default\n"
                    + "  ,b.creator\n"
                    + "  ,b.lang\n"
                    + "  ,b.last_operator\n"
                    + "  ,b.sales_supportor\n"
                    + "  ,b.sales_backup\n"
                    + "  ,b.responser1\n"
                    + "  ,b.responser2\n"
                    + "  ,IF(b.service_providers IS NULL, a.service_providers, b.service_providers) AS service_providers\n"
                    + "  ,b.service_assistant\n"
                    + "  ,b.service_assistant_backup\n"
                    + "  ,b.aftersales_district\n"
                    + "  ,b.group_channel\n"
                    + "  ,b.group_belong\n"
                    + "  ,b.source_type\n"
                    + "  ,b.owner_group\n"
                    + "  ,b.add_method\n"
                    + "  ,b.aftersales_type\n"
                    + "  ,a.uin_list\n"
                    + "  ,a.company_name\n"
                    + "  ,a.qqgroup\n"
                    + "  ,a.qqgroup_name\n"
                    + "  ,a.client_manager\n"
                    + "  ,a.business_manager\n"
                    + "  ,a.grade\n"
                    + "  ,a.trade\n"
                    + "  ,a.area_list\n"
                    + "  ,a.`source`\n"
                    + "  ,a.key_person\n"
                    + "  ,a.client_contacts\n"
                    + "  ,a.background\n"
                    + "  ,a.architecture\n"
                    + "  ,a.belong_area\n"
                    + "  ,a.main_products\n"
                    + "  ,a.main_problems\n"
                    + "  ,a.main_bussiness\n"
                    + "  ,a.main_cost\n"
                    + "  ,a.main_members\n"
                    + "  ,a.main_config\n"
                    + "  ,a.main_arch\n"
                    + "  ,a.nickname\n"
                    + "  ,a.is_head\n"
                    + "  ,a.is_upgraded\n"
                    + "  ,a.is_sensitive\n"
                    + "  ,a.phase\n"
                    + "  ,a.customer_inf\n"
                    + "  ,a.special_conf\n"
                    + "  ,a.slack_group\n"
                    + "  ,a.wework_group\n"
                    + "  ,a.second_group_id\n"
                    + "  ,a.receivable_grade\n"
                    + "  ,a.consume_grade\n"
                    + "  ,a.sales_grade\n"
                    + "  ,a.register_name\n"
                    + "  ,a.`privileges`\n"
                    + "  ,a.is_developer\n"
                    + "  ,a.is_promote\n"
                    + "  ,a.operation_manager\n"
                    + "  ,a.channelmark\n"
                    + "  ,a.business_sales\n"
                    + "  ,a.mark_name\n"
                    + "  ,a.is_transfer_outsourcing\n"
                    + "  ,a.is_ext_group\n"
                    + "  ,a.ext_group\n"
                    + "  ,a.appId AS appid\n"
                    + "  ,a.tradetwo\n"
                    + "  ,a.is_green_channel\n"
                    + "  ,a.priority\n"
                    + "  ,a.is_update\n"
                    + "  ,a.district_belong\n"
                    + "  ,a.`language`\n"
                    + "  ,a.is_international\n"
                    + "  ,a.is_postPay AS is_postpay\n"
                    + "  ,a.is_walnut\n"
                    + "  ,a.organization_id\n"
                    + "  ,a.organization_name\n"
                    + "  ,a.group_name\n"
                    + "  ,a.sales_channel\n"
                    + "  ,a.is_public_industry\n"
                    + "  ,CAST(IF(COALESCE(a.deleted_at, TIMESTAMP '1970-01-01 00:00:00') > COALESCE(b.deleted_at, TIMESTAMP '1970-01-01 00:00:00'), a.deleted_at, b.deleted_at) AS TIMESTAMP_LTZ) AS deleted_at\n"
                    + "  ,a.site_properties\n"
                    + "FROM mysql_source_t029_big_customer_v2 a\n"
                    + "FULL JOIN mysql_source_t_customer_channel b ON a.uin = b.uin";
}