package com.tencent.andata.etl.sql;

import java.util.stream.Collectors;
import org.apache.flink.table.api.Schema;

public class DwdProgressReportSql {

    public static String deDupSQL = ""
            + "SELECT *\n"
            + "FROM (\n"
            + "  SELECT\n"
            + "    CURRENT_ROW_TIMESTAMP() AS dwd_create_time,\n"
            + "    value_of_primary_key,\n"
            + "    CAST(record_update_time AS TIMESTAMP) msg_time,\n"
            + "    CAST(record_update_time AS TIMESTAMP) record_update_time,\n"
            + "    CAST(ticket_id AS BIGINT) AS ticket_id,\n"
            + "    ticket_title,\n"
            + "    CAST(service_channel AS BIGINT) AS service_channel,\n"
            + "    CAST(ticket_create_time AS TIMESTAMP) AS ticket_create_time,\n"
            + "    trade AS industry_attribution,\n"
            + "    affected_business AS products_attribution,\n"
            + "    region_name,\n"
            + "    industry_department AS attribution_department,\n"
            + "    CAST(burst_status AS BIGINT) AS burst_status,\n"
            + "    CAST(burst_time_start AS TIMESTAMP) AS burst_time_start,\n"
            + "    CAST(burst_time_end AS TIMESTAMP) AS burst_time_end,\n"
            + "    `description`,\n"
            + "    customer_feedback,\n"
            + "    guest_progress_manager,\n"
            + "    guest_sentiment_owner,\n"
            + "    guest_modifier,\n"
            + "    CAST(guest_modify_time AS TIMESTAMP) AS guest_modify_time,\n"
            + "    current_action,\n"
            + "    next_action,\n"
            + "    help_item,\n"
            + "    reason,\n"
            + "    tech_manager,\n"
            + "    tech_owner,\n"
            + "    tech_modifier,\n"
            + "    CAST(tech_modify_time AS TIMESTAMP) AS tech_modify_time,\n"
            + "    CAST(region_id AS BIGINT) AS region_id,\n"
            + "    incident_influence,\n"
            + "    CAST(estimated_repair_time AS TIMESTAMP) AS estimated_repair_time,\n"
            + "    maintenance_status_value,\n"
            + "    ROW_NUMBER() OVER (PARTITION BY value_of_primary_key ORDER BY record_update_time DESC) AS rn\n"
            + "  FROM iceberg_source_ods_progress_report /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + ") t\n"
            + "WHERE rn = 1";

    public static String sinkToPg(String srcName, String dstName, Schema schema) {
        return String.format("INSERT INTO %s /*+ OPTIONS('sink.parallelism'='1') */ SELECT %s FROM %s",
                dstName,
                schema.getColumns()
                        .stream()
                        .map(s -> String.format("`%s`", s.getName()))
                        .collect(Collectors.joining(",")),
                srcName);
    }

    public static String sinkToIceberg(String srcName, String dstName, Schema schema) {
        return String.format("INSERT INTO %s "
                        + "/*+ OPTIONS('sink.parallelism'='1', "
                        + "'equality-field-columns'='value_of_primary_key', "
                        + "'upsert-enabled'='true') */ "
                        + "SELECT %s FROM %s",
                dstName,
                schema.getColumns()
                        .stream()
                        .map(s -> String.format("`%s`", s.getName()))
                        .collect(Collectors.joining(",")),
                srcName);
    }

}
