package com.tencent.andata.etl.sql;

import java.util.stream.Collectors;
import org.apache.flink.table.api.Schema;

public class DwdFaultReportSql {

    public static String deDupSQL = ""
            + "SELECT *\n"
            + "FROM (\n"
            + "  SELECT\n"
            + "    CURRENT_ROW_TIMESTAMP() AS dwd_create_time,\n"
            + "    value_of_primary_key,\n"
            + "    CAST(record_update_time AS TIMESTAMP) msg_time,\n"
            + "    CAST(ticket_id AS BIGINT) AS ticket_id,\n"
            + "    ticket_title,\n"
            + "    final_customer,\n"
            + "    project_name,\n"
            + "    trade AS industry_attribution,\n"
            + "    affected_business AS products_attribution,\n"
            + "    CAST(burst_status AS BIGINT) AS burst_status,\n"
            + "    `description`,\n"
            + "    customer_feedback,\n"
            + "    reason,\n"
            + "    creator AS reported_by,\n"
            + "    report_wechat_groups,\n"
            + "    CAST(create_time AS TIMESTAMP) create_time,\n"
            + "    CAST(burst_time_start AS TIMESTAMP) burst_time_start,\n"
            + "    CAST(burst_time_end AS TIMESTAMP) burst_time_end,\n"
            + "    manager,\n"
            + "    progress,\n"
            + "    CAST(repair_time AS TIMESTAMP) repair_time,\n"
            + "    CAST(record_update_time AS TIMESTAMP) record_update_time,\n"
            + "    report_groups_info,\n"
            + "    report_title,\n"
            + "    customer,\n"
            + "    industry_department,\n"
            + "    feedback_time,\n"
            + "    ticket_url,\n"
            + "    sixty_url,\n"
            + "    report_type,\n"
            + "    customer_report_statistics,\n"
            + "    ROW_NUMBER() OVER (PARTITION BY value_of_primary_key ORDER BY record_update_time DESC) rn\n"
            + "  FROM iceberg_source_ods_fault_report /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + ") t\n"
            + "WHERE rn = 1";


    public static String insertIntoPg(String srcName, String dstName, Schema schema) {
        return String.format("INSERT INTO %s /*+ OPTIONS('sink.parallelism'='1') */ SELECT %s FROM %s",
                dstName,
                schema.getColumns()
                        .stream()
                        .map(s -> String.format("`%s`", s.getName()))
                        .collect(Collectors.joining(",")),
                srcName);
    }

    public static String insertIntoIceberg(String srcName, String dstName, Schema schema) {
        return String.format("INSERT INTO %s "
                        + "/*+ OPTIONS('sink.parallelism'='1', "
                        + "'equality-field-columns'='value_of_primary_key', "
                        + "'upsert-enabled'='true') */ "
                        + "SELECT %s FROM %s",
                dstName,
                schema.getColumns()
                        .stream()
                        .map(s -> String.format("`%s`", s.getName()))
                        .collect(Collectors.joining(",")),
                srcName);
    }
}
