package com.tencent.andata.etl.dwm;

import static com.tencent.andata.etl.sql.DwmIncidentTicketEsSql.GET_ES_DATA_SQL;
import static com.tencent.andata.etl.sql.DwmIncidentTicketEsSql.GET_EXTRA_DATA_SQL;
import static com.tencent.andata.etl.tablemap.DwmIncidentTicketEsMapping.icebergTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwmIncidentTicketEsMapping.pgsqlTable2FlinkTable;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import com.tencent.andata.etl.transform.TicketContentUtil.CleanTicketOperationMapFun;
import com.tencent.andata.etl.transform.TicketContentUtil.CompleteTicketItemProcessFunction;
import com.tencent.andata.etl.transform.TicketContentUtil.ExtraDataTransMapFun;
import com.tencent.andata.etl.transform.TicketContentUtil.TicketContentInfoMapFunction;
import com.tencent.andata.etl.transform.TicketContentUtil.TicketGetOperationMapFunction;
import com.tencent.andata.etl.transform.TicketContentUtil.TicketItem;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.io.IOException;
import java.util.HashMap;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.elasticsearch.sink.Elasticsearch7SinkBuilder;
import org.apache.flink.connector.elasticsearch.sink.ElasticsearchEmitter;
import org.apache.flink.connector.elasticsearch.sink.FlushBackoffType;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;
import org.apache.http.HttpHost;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.common.xcontent.XContentType;

public class DwmIncidentTicketToEs {

    static ObjectMapper mapper = new ObjectMapper();

    /**
     * 工单数据写入es
     */
    public static void main(String[] args) throws Exception {
        FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);

        final StreamTableEnvironment tEnv = flinkEnv.streamTEnv();

        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        flinkEnv.env().setRestartStrategy(RestartStrategies.failureRateRestart(
                50, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS) // delay
        ));

        final String esIndex = parameterTool.get("esIndex");
        final String pgDbName = parameterTool.get("pgDbName");
        final String icebergDbName = parameterTool.get("icebergDbName");

        final IcebergCatalogReader catalog = new IcebergCatalogReader();

        // table env config
        Configuration configuration = flinkEnv.streamTEnv().getConfig().getConfiguration();

        // 状态保留3天
        configuration.setString("table.exec.state.ttl", "2592000000");
        configuration.setString("execution.runtime-mode", "streaming");
        configuration.setString("execution.checkpointing.interval", "30s");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        configuration.setString("table.dynamic-table-options.enabled", "true");

        // 关闭iceberg source的自动推断并行度
        configuration.setString("table.exec.iceberg.infer-source-parallelism", "false");

        //开启微批模式
        configuration.setString("table.exec.mini-batch.size", "5000");
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "3 s");

        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", pgDbName))
                .build();

        // pgsql table mapping to flink table
        ArrayNode pgsqlTable2FlinkTableMap = mapper.readValue(pgsqlTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(pgsqlDBConf, pgsqlTable2FlinkTableMap, PGSQL, tEnv);

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog);

        DataStream<Row> dataStream = tEnv.toChangelogStream(tEnv.sqlQuery(GET_ES_DATA_SQL),
                Schema.newBuilder().primaryKey("_ticket_id").build(),
                ChangelogMode.upsert());

        //获取204表数据
        DataStream<Row> operationExtraStream = tEnv.toChangelogStream(tEnv.sqlQuery(GET_EXTRA_DATA_SQL),
                Schema.newBuilder().primaryKey("_ticket_id").build(),
                ChangelogMode.upsert());

        SingleOutputStreamOperator<Tuple3<Row, String, String>> inputStream = dataStream
                .map(r -> new Tuple3<>(r, "_ticket_id", "operation_data"))
                .returns(new TypeHint<Tuple3<Row, String, String>>() {
                });

        SingleOutputStreamOperator<Tuple2<String, TicketItem>> extraStream = operationExtraStream
                .map(new ExtraDataTransMapFun());

        SingleOutputStreamOperator<Tuple2<String, TicketItem>> mainStream = inputStream
                .map(new TicketGetOperationMapFunction())
                .map(new TicketContentInfoMapFunction())
                .map(new CleanTicketOperationMapFun());

        SingleOutputStreamOperator<TicketItem> result = mainStream.connect(extraStream)
                .keyBy(r1 -> r1.f0, r2 -> r2.f0)
                .process(new CompleteTicketItemProcessFunction());

        //es链接配置
        String esHost = rainbowUtils.getStringValue("cdc.database.elasticsearch", "host");
        String esPort = rainbowUtils.getStringValue("cdc.database.elasticsearch", "port");
        String esUserName = rainbowUtils.getStringValue("cdc.database.elasticsearch", "username");
        String esPassword = rainbowUtils.getStringValue("cdc.database.elasticsearch", "password");
        Elasticsearch7SinkBuilder<TicketItem> esSinkBuilder = new Elasticsearch7SinkBuilder<>();
        esSinkBuilder.setEmitter(
                (ElasticsearchEmitter<TicketItem>) (row, context, indexer) -> {
                    try {
                        indexer.add(createUpdateIndexRequest(row, esIndex));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });

        esSinkBuilder.setConnectionUsername(esUserName)
                .setConnectionPassword(esPassword)
                .setHosts(new HttpHost(esHost, Integer.parseInt(esPort)))
                // 这里启用了一个指数退避重试策略，初始延迟为 1000 毫秒且最大重试次数为 10
                .setBulkFlushBackoffStrategy(FlushBackoffType.EXPONENTIAL, 10, 1000)
                .setBulkFlushInterval(6000) //批量写入的时间间隔
                .setBulkFlushMaxSizeMb(100) //批量写入时的最大数据量
                .setBulkFlushMaxActions(10000) //批量写入时的最大写入条数
                .setConnectionTimeout(600000)
                .setSocketTimeout(1000 * 60 * 10)
                .setConnectionRequestTimeout(1000 * 60 * 10);

        result.sinkTo(esSinkBuilder.build()).name("sink-es");

        flinkEnv.env().execute("incident-ticket-sink-es");
    }

    private static UpdateRequest createUpdateIndexRequest(TicketItem ticketItem, String esIndex) throws IOException {

        HashMap<String, TicketItem> map = new HashMap<String, TicketItem>() {{
            put("item", ticketItem);
        }};

        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        String json = mapper.writeValueAsString(map);

        System.out.println("json: " + json);
        IndexRequest indexRequest = new IndexRequest(esIndex);
        UpdateRequest updateRequest = new UpdateRequest(esIndex, ticketItem.getId());

        indexRequest.source(json, XContentType.JSON);

        updateRequest
                .doc(indexRequest)
                .docAsUpsert(true)
                .retryOnConflict(5);

        return updateRequest;
    }
}