package com.tencent.andata.etl.dwd;

import static com.tencent.andata.etl.sql.BigCustomerSql.BIG_CUSTOMER_AND_CHANNEL;

import static com.tencent.andata.etl.tablemap.BigCustomerMapping.icebergTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.BigCustomerMapping.mysqlTopCustomerTable2FlinkTable;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;


import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;

import java.util.Properties;
import lombok.Builder;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class BigCustomer {

    private static final Logger logger = LoggerFactory.getLogger(BigCustomer.class);
    private final String icebergDbName;

    public void run(FlinkEnv fEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = fEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        final KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "d_tss_top_customer_tools"))
                .build();

        final ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(mysqlTopCustomerTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlDBConf, mysqlTable2FlinkTableMap, DatabaseEnum.MYSQL, tEnv);

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );

        // 大客户信息与渠道信息
        tEnv.createTemporaryView("big_customer_info_view", tEnv.sqlQuery(BIG_CUSTOMER_AND_CHANNEL));

        StatementSet stmtSet = fEnv.stmtSet();

        stmtSet
                .addInsertSql(insertIntoSql(
                        "big_customer_info_view",
                        "iceberg_sink_dwd_rt_key_customer_information",
                        tEnv.from("iceberg_sink_dwd_rt_key_customer_information"),
                        ICEBERG
                ));
    }
}