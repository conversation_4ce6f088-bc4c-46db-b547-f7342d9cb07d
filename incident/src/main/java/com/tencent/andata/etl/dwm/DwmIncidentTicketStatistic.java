package com.tencent.andata.etl.dwm;

import com.tencent.andata.etl.transform.DwmIncidentTicketStatisticTransform.TicketCloseAndInteractionMapFunction;
import com.tencent.andata.etl.transform.DwmIncidentTicketStatisticTransform.TicketClosedSolvedTimeMapFunction;
import com.tencent.andata.etl.transform.DwmIncidentTicketStatisticTransform.TicketDealDurationMapFunction;
import com.tencent.andata.etl.transform.DwmIncidentTicketStatisticTransform.TicketDenyInfoMapFunction;
import com.tencent.andata.etl.transform.DwmIncidentTicketStatisticTransform.TicketNotClosedSolvedTimeMapFunction;
import com.tencent.andata.etl.transform.DwmIncidentTicketStatisticTransform.TicketReplyInfoMapFunction;
import com.tencent.andata.etl.transform.DwmIncidentTicketStatisticTransform.TicketStaffInfoProcessFunction;
import com.tencent.andata.etl.transform.DwmIncidentTicketStatisticTransform.TicketStaffPostProcessFunction;
import com.tencent.andata.etl.transform.DwmIncidentTicketStatisticTransform.TicketTransferInfoMapFunction;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

import static com.tencent.andata.etl.sql.DwmIncidentTicketStatisticSql.QUERY_DWD_INCIDENT_TICKET_SQL;
import static com.tencent.andata.etl.sql.DwmIncidentTicketStatisticSql.QUERY_MYSQL_STAFF_POST_SQL;
import static com.tencent.andata.etl.sql.DwmIncidentTicketStatisticSql.QUERY_MYSQL_STAFF_INFO_SQL;
import static com.tencent.andata.etl.tablemap.DwmIncidentTicketStatisticMapping.mysqlAccountTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwmIncidentTicketStatisticMapping.mysqlWorkTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwmIncidentTicketStatisticMapping.icebergTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwmIncidentTicketStatisticMapping.pgsqlTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;


@Builder
public class DwmIncidentTicketStatistic {

    private static final Logger LOG = LoggerFactory.getLogger(DwmIncidentTicketStatistic.class);
    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf mysqlWorkDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();

        final DatabaseConf mysqlAccountDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "account"))
                .build();

        final DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlAccountTable2FlinkTableMap = mapper.readValue(mysqlAccountTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlAccountDBConf, mysqlAccountTable2FlinkTableMap, MYSQL, tEnv);

        // mysql table mapping to flink table
        ArrayNode mysqlWorkTable2FlinkTableMap = mapper.readValue(mysqlWorkTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlWorkDBConf, mysqlWorkTable2FlinkTableMap, MYSQL, tEnv);

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog);

        TableUtils.rdbTable2FlinkTable(
                pgDBConf,
                mapper.readValue(pgsqlTable2FlinkTable, ArrayNode.class),
                PGSQL,
                tEnv
        );

        Table tbl = tEnv.sqlQuery(QUERY_DWD_INCIDENT_TICKET_SQL);

        Table staffPost = tEnv.sqlQuery(QUERY_MYSQL_STAFF_POST_SQL);

        Table staffInfo = tEnv.sqlQuery(QUERY_MYSQL_STAFF_INFO_SQL);

        // a map descriptor to store the uid of the staff-info (string) and the staff-info itself.
        MapStateDescriptor<String, Tuple2<String, Integer>> staffInfoStateDesc =
                new MapStateDescriptor<>("staffInfoBroadcastState",
                        TypeInformation.of(String.class),
                        TypeInformation.of(new TypeHint<Tuple2<String, Integer>>() {
                        })
                );

        // a map descriptor to store the user_id of the staff-post (string) and the staff-post itself.
        MapStateDescriptor<String, Integer> staffPostStateDes = new MapStateDescriptor<>(
                "staffPostBroadcastState", BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.INT_TYPE_INFO);

        // staffInfo转为广播流
        BroadcastStream<Row> staffInfoBroadcastStream =
                tEnv.toChangelogStream(staffInfo,
                                Schema.newBuilder().primaryKey("uid").build(),
                                ChangelogMode.upsert())
                        .broadcast(staffInfoStateDesc);

        // staffPost转为广播流
        BroadcastStream<Row> staffPostBroadcastStream =
                tEnv.toChangelogStream(
                                staffPost,
                                Schema.newBuilder().primaryKey("pk").build(),
                                ChangelogMode.upsert())
                        .broadcast(staffPostStateDes);

        DataStream<Row> dataStream = tEnv.toChangelogStream(tbl);

        DataStream<Row> resultStream = dataStream
                .connect(staffPostBroadcastStream)
                .process(new TicketStaffPostProcessFunction()) // 通过广播流，获取操作人岗位
                .connect(staffInfoBroadcastStream)
                .process(new TicketStaffInfoProcessFunction()) // 通过广播流，获取操作人信息(user_name)
                .map(new TicketCloseAndInteractionMapFunction()) // 工单交互相关统计
                .map(new TicketClosedSolvedTimeMapFunction()) // 已结单，计算工单解决时间
                .map(new TicketNotClosedSolvedTimeMapFunction()) // 未结单，计算工单解决时间
                .map(new TicketDenyInfoMapFunction()) // 工单被拒绝相关指标
                .map(new TicketDealDurationMapFunction()) // 工单处理时长指标
                .map(new TicketTransferInfoMapFunction()) // 工单转单类指标
                .map(new TicketReplyInfoMapFunction()) // 工单回复类指标
                .returns(dataStream.getType())
                .name("transfer-0");

        Table sinkTable = tEnv.fromChangelogStream(resultStream);

        // 根据dwd_incident_ticket_base_info表及dwd_incident_ticket_operation计算的指标创建view
        tEnv.createTemporaryView("dwm_incident_ticket_statistic_v1_view", sinkTable);

        // 注册pg表
        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                "dwm_incident_ticket_statistic_v1_view",
                "iceberg_sink_dwm_incident_ticket_statistic_v1",
                        tEnv.from("iceberg_sink_dwm_incident_ticket_statistic_v1"),
                        ICEBERG))
                .addInsertSql(insertIntoSql(
                        "dwm_incident_ticket_statistic_v1_view",
                        "pg_dwm_incident_ticket_statistic_v1",
                        tEnv.from("pg_dwm_incident_ticket_statistic_v1"),
                        PGSQL
                ));

    }
}