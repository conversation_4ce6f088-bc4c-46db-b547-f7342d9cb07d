package com.tencent.andata.etl.dwd;


import static com.tencent.andata.etl.tablemap.DwdIncidentTicketOverseasMapping.icebergTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketOverseasMapping.mysqlAccountTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketOverseasMapping.mysqlTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketOverseasMapping.pgsqlTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class DwdIncidentTicketOverseas {

    private static final Logger logger = LoggerFactory.getLogger(DwdIncidentTicketOverseas.class);
    private final String icebergDbName;
    private final String pgDbName;

    @NotNull
    private static Map<String, String> getStringMap() {
        Map<String, String> tblMap = new HashMap<>();
        tblMap.put("mysql_source_t102_duty", "iceberg_sink_dim_incident_duty");
        tblMap.put("mysql_source_t201_ticket", "iceberg_sink_dwd_incident_ticket");
        tblMap.put("mysql_source_t202_ticket_operation", "iceberg_sink_dwd_incident_ticket_operation");
        tblMap.put("mysql_source_t201_ticket_extra", "iceberg_sink_dwd_incident_ticket_extra");
        tblMap.put("mysql_source_t204_ticket_operation_extra", "iceberg_sink_dwd_incident_ticket_operation_extra");
        tblMap.put("mysql_source_t210_ticket_alarm_record", "iceberg_sink_dwd_incident_ticket_alarm_record");
        tblMap.put("mysql_source_t_users", "iceberg_sink_ods_t_users");
        tblMap.put("mysql_source_t006_staff", "iceberg_sink_ods_t006_staff");
        tblMap.put("mysql_source_t903_company_user", "iceberg_sink_ods_t903_company_user");
        tblMap.put("mysql_source_t009_staff_post", "iceberg_sink_dim_incident_staff_post");
        return tblMap;
    }

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        final KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();

        final DatabaseConf mysqlAccountDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "account"))
                .build();

        final DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        final ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(mysqlTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlDBConf, mysqlTable2FlinkTableMap, MYSQL, tEnv);

        ArrayNode mysqlAccountTable2FlinkTableMap = mapper.readValue(mysqlAccountTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlAccountDBConf, mysqlAccountTable2FlinkTableMap, MYSQL, tEnv);

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(this.icebergDbName, mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog);

        // pgsql table mapping to flink table
        ArrayNode pgsqlTable2FlinkTableMap = mapper.readValue(pgsqlTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(pgsqlDBConf, pgsqlTable2FlinkTableMap, PGSQL, tEnv);

        Map<String, String> tblMap = getStringMap();

        StatementSet stmtSet = flinkEnv.stmtSet();
        for (Entry<String, String> entry : tblMap.entrySet()) {
            stmtSet.addInsertSql(insertIntoSql(
                    entry.getKey(),
                    entry.getValue(),
                    tEnv.from(entry.getValue()),
                    ICEBERG
            ));
        }

        Table ticket_operation_extra_tbl = tEnv.sqlQuery("SELECT * FROM mysql_source_t204_ticket_operation_extra");


        DataStream<Row> ticketOperationExtraStream = tEnv.toChangelogStream(ticket_operation_extra_tbl);
        DataStream<Row> cleanedTicketOperationExtraStreamStream = ticketOperationExtraStream
                .map(DwdIncidentTicketOverseas::cleanRow)
                .returns(TableUtils.getTableRowTypeInformation(tEnv.from("pgsql_sink_dwd_incident_ticket_operation_extra")));

        Table cleaned_ticket_operation_extra_tbl = tEnv.fromChangelogStream(cleanedTicketOperationExtraStreamStream);
        tEnv.createTemporaryView("cleaned_ticket_operation_extra_view", cleaned_ticket_operation_extra_tbl);

        stmtSet.addInsertSql(insertIntoSql(
                        "mysql_source_t201_ticket",
                        "pgsql_sink_dwd_incident_ticket", // 工单主表数据写入pgsql
                        tEnv.from("pgsql_sink_dwd_incident_ticket"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t202_ticket_operation",
                        "pgsql_sink_dwd_incident_ticket_operation", // 工单流水表数据写入pgsql
                        tEnv.from("pgsql_sink_dwd_incident_ticket_operation"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t201_ticket_extra",
                        "pgsql_sink_dwd_incident_ticket_extra", // 工单主表扩展数据数据写入pgsql
                        tEnv.from("pgsql_sink_dwd_incident_ticket_extra"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "cleaned_ticket_operation_extra_view",
                        "pgsql_sink_dwd_incident_ticket_operation_extra", // 工单流水表扩展数据数据写入pgsql
                        tEnv.from("pgsql_sink_dwd_incident_ticket_operation_extra"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t210_ticket_alarm_record",
                        "pgsql_sink_dwd_incident_ticket_alarm_record", // 工单告警记录表数据写入pgsql
                        tEnv.from("pgsql_sink_dwd_incident_ticket_alarm_record"),
                        PGSQL
                ));
    }

    private static Row cleanRow(Row row) {
        for (int i = 0; i < row.getArity(); i++) {
            Object field = row.getField(i);
            if (field instanceof String) {
                String cleaned = ((String) field).replaceAll("\u0000", "");
                row.setField(i, cleaned);
            }
        }
        return row;
    }
}