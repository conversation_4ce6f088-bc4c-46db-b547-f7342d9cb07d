package com.tencent.andata.etl.sql;

public class DwdIncidentTicketOperationDetailSql {

    public static final String QUERY_DWD_INCIDENT_TICKET_SQL = ""
            + "SELECT\n"
            + "  CAST(t.`ticket_id` AS BIGINT) AS `ticket_id`,\n"
            + "  CAST(t.`uin` AS BIGINT) AS `uin`,\n"
            + "  t.`related_region_code`,\n"
            + "  t.`related_phone_number`,\n"
            + "  CAST(t.`service_channel` AS BIGINT) AS `service_channel`,\n"
            + "  CAST(t.`service_scene` AS BIGINT) AS `service_scene`,\n"
            + "  CAST(t.`service_scene_checked` AS BIGINT) AS `service_scene_checked`,\n"
            + "  CAST(t.`archive_type` AS BIGINT) AS `archive_type`,\n"
            + "  CAST(t.`priority` AS BIGINT) AS `priority`,\n"
            + "  t.`creator`,\n"
            + "  t.`create_time`,\n"
            + "  t.`current_operator`,\n"
            + "  t.`last_operator`,\n"
            + "  t.`last_staff`,\n"
            + "  t.`last_operate_time`,\n"
            + "  REGEXP_REPLACE(t.question, '[\\n\\t]', '') as question,\n"
            + "  REGEXP_REPLACE(t.`last_inner_reply`, '[\\n\\t]', '') as last_inner_reply,\n"
            + "  CAST(t.`status` AS BIGINT) AS `status`,\n"
            + "  t.`operation_ticket_id`,\n"
            + "  t.`responsible`,\n"
            + "  t.`first_line_responsible`,\n"
            + "  t.`second_line_responsible`,\n"
            + "  CAST(t.`solve_status` AS BIGINT) AS `solve_status`,\n"
            + "  CAST(t.`service_rate` AS BIGINT) AS `service_rate`,\n"
            + "  CAST(t.`satisfaction` AS BIGINT) AS `satisfaction`,\n"
            + "  CAST(t.`unsatisfy_reason` AS BIGINT) AS `unsatisfy_reason`,\n"
            + "  CAST(t.`first_should_assign` AS BIGINT) AS `first_should_assign`,\n"
            + "  CAST(t.`first_fact_assign` AS BIGINT) AS `first_fact_assign`,\n"
            + "  t.`first_fact_assign_time`,\n"
            + "  CAST(t.`should_assign` AS BIGINT) AS `should_assign`,\n"
            + "  CAST(t.`fact_assign` AS BIGINT) AS `fact_assign`,\n"
            + "  t.`fact_assign_time`,\n"
            + "  CAST(t.`unsatisfy_ticket_id` AS BIGINT) AS `unsatisfy_ticket_id`,\n"
            + "  CAST(t.`related_unsatisfied_ticket_id` AS BIGINT) AS `related_unsatisfied_ticket_id`,\n"
            + "  t.`related_qcloud_ticket_id`,\n"
            + "  t.`qcloud_ticket_id`,\n"
            + "  CAST(t.`qcloud_category_id` AS BIGINT) AS `qcloud_category_id`,\n"
            + "  CAST(t.`qcloud_receive_notice_flag` AS BIGINT) AS `qcloud_receive_notice_flag`,\n"
            + "  t.`instance_id`,\n"
            + "  t.`woodpecker_jobid`,\n"
            + "  t.`appraise`,\n"
            + "  t.`appraise_time`,\n"
            + "  t.`next_follow_time`,\n"
            + "  CAST(t.`qcloud_complaint_id` AS BIGINT) AS `qcloud_complaint_id`,\n"
            + "  CAST(t.`complainted_ticket_id` AS BIGINT) AS `complainted_ticket_id`,\n"
            + "  CAST(t.`language` AS BIGINT) AS `language`,\n"
            + "  t.`callcenter_answer_time`,\n"
            + "  t.`callcenter_hand_up_time`,\n"
            + "  CAST(t.`callcenter_talk_duration` AS BIGINT) AS `callcenter_talk_duration`,\n"
            + "  CAST(t.`callcenter_queue_duration` AS BIGINT) AS `callcenter_queue_duration`,\n"
            + "  t.`callcenter_session_id`,\n"
            + "  t.`callcenter_agent_id`,\n"
            + "  t.`callcenter_agent_name`,\n"
            + "  t.`callcenter_session_filename`,\n"
            + "  t.`qqgroup_start_time`,\n"
            + "  t.`qqgroup_end_time`,\n"
            + "  t.`qqgroup_response_time`,\n"
            + "   CAST(t.`qqgroup_response_duration` AS BIGINT) AS `qqgroup_response_duration`,\n"
            + "   CAST(t.`qqgroup_number` AS BIGINT) AS `qqgroup_number`,\n"
            + "  t.`qqgroup_responsable`,\n"
            + "  t.`qqgroup_big_customer_operator`,\n"
            + "  t.`secret_content`,\n"
            + "  t.`update_time`,\n"
            + "  t.`qcloud_last_customer_reply_time`,\n"
            + "  CAST(t.`vip_ask_callback_id` AS BIGINT) AS `vip_ask_callback_id`,\n"
            + "  t.`callback_time_start`,\n"
            + "  t.`callback_time_end`,\n"
            + "  CAST(t.`owner_uin` AS BIGINT) AS `owner_uin`,\n"
            + "  CAST(t.`is_deleted` AS BIGINT) AS `is_deleted`,\n"
            + "  CAST(t.`is_ever_to_wan` AS BIGINT) AS `is_ever_to_wan`,\n"
            + "  CAST(t.`company_id` AS BIGINT) AS `company_id`,\n"
            + "  CAST(t.`is_claim` AS BIGINT) AS `is_claim`,\n"
            + "  CAST(t.`is_fault_report` AS BIGINT) AS `is_fault_report`,\n"
            + "  CAST(t.`is_internal_go_up` AS BIGINT) AS `is_internal_go_up`,\n"
            + "  CAST(t.`post` AS BIGINT) AS `post`,\n"
            + "  t.`next_up_responsor`,\n"
            + "  t.`next_up_time`,\n"
            + "  t.`cc_person`,\n"
            + "  t.`operation_service_scene`,\n"
            + "  t.`responsible_should_assign`,\n"
            + "  t.`chat_group_id`,\n"
            + "  CAST(t.`first_assign_company_id` AS BIGINT) AS `first_assign_company_id`,\n"
            + "  CAST(t.`feedback_channel` AS BIGINT) AS `feedback_channel`,\n"
            + "  t.`group_id`,\n"
            + "  CAST(t.`source_channel` AS BIGINT) AS `source_channel`,\n"
            + "  t.`customer_contact_info`,\n"
            + "  CAST(t.`question_category` AS BIGINT) AS `question_category`,\n"
            + "  CAST(t.`reason_category` AS BIGINT) AS `reason_category`,\n"
            + "  t.`question_start_time`,\n"
            + "  t.`question_end_time`,\n"
            + "  t.`title`,\n"
            + "  t.`reason`,\n"
            + "  t.`upgrade_time`,\n"
            + "  t.`sales_supportor`,\n"
            + "  t.`trade`,\n"
            + "  t.`name`,\n"
            + "  CAST(t.`event_tss_id` AS BIGINT) AS `event_tss_id`,\n"
            + "  CAST(t.`add_info_application_post` AS BIGINT) AS `add_info_application_post`,\n"
            + "  CAST(t.`add_info_application_should_assign` AS BIGINT) AS `add_info_application_should_assign`,\n"
            + "  t.`add_info_application_operator`,\n"
            + "  t.`tapd_story_id`,\n"
            + "  t.`callcenter_skill_group`,\n"
            + "  CAST(t.`keyissues_id` AS BIGINT) AS `keyissues_id`,\n"
            + "  CAST(t.`complaint` AS BIGINT) AS `complaint`,\n"
            + "  REGEXP_REPLACE(t.`complaint_content`, '[\\n\\t]', '') as complaint_content,\n"
            + "  t.`ltc_name`,\n"
            + "  t.`product_version`,\n"
            + "  t.`severity`,\n"
            + "  t.`short`,\n"
            + "  CAST(t.`extern_status` AS BIGINT) AS `extern_status`,\n"
            + "  t.`caller`,\n"
            + "  t.`affected_customers`,\n"
            + "  t.`customer_uid`,\n"
            + "  t.`incident_manager`,\n"
            + "  CAST(t.`incident_manager_should_assign` AS BIGINT) AS incident_manager_should_assign,\n"
            + "  t.`risk_control_first_time`,\n"
            + "  CAST(t.`risk_control_num` AS BIGINT) AS `risk_control_num`,\n"
            + "  CAST(t.`is_incident_manager_into` AS BIGINT) AS `is_incident_manager_into`,\n"
            + "  'https://intl.andon.woa.com/ticket/detail/?id=' "
            + "   || CAST(t.`ticket_id` AS STRING) "
            + "   || '&sign=' "
            + "   || MD5(MD5(CAST(t.`ticket_id` AS STRING) || 'andontcs')) AS url,\n"
            + "   'https://intl.andon.woa.com/ticket/nologin/redirect?id=' "
            + "   || CAST(t.`ticket_id` AS STRING) "
            + "   || '&sign=' "
            + "   || MD5(MD5(CAST(t.`ticket_id` AS STRING) || 'andontcs')) AS short_url,\n"
            + "  t1.operation_data AS operation_data\n"
            + "FROM (\n"
            + "  SELECT\n"
            + "    *, \n"
            + "    ROW_NUMBER() OVER (PARTITION BY ticket_id ORDER BY update_time DESC) AS rn\n"
            + "  FROM iceberg_source_dwd_incident_ticket\n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='3s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + ") as t\n"
            + "LEFT JOIN (\n"
            + "    SELECT ticket_id, \n"
            + "    concat('[',LISTAGG(concat('{ \"ticket_id\": \"', CAST(ticket_id as string), \n"
            + "    '\", \"operation_id\": \"', CAST(operation_id as string), \n"
            + "    '\", \"operate_time\": \"', CAST(operate_time as string),\n"
            + "    '\", \"operation_type\": \"', CAST(operation_type as string), \n"
            + "    '\", \"operator_type\": \"', CAST(operator_type as string), \n"
            + "    '\", \"operator\": \"', operator, \n"
            + "    '\", \"target_status\": \"', CAST(target_status as string), \n"
            + "    '\", \"next_operator\": \"', next_operator,\n"
            + "    '\", \"duration\": \"', CAST(duration as string), \n"
            + "    '\", \"next_assign\": \"', CAST(next_assign as string), \n"
            + "    '\", \"company_id\": \"', CAST(company_id as string), \n"
            + "    '\", \"target_post\": \"', CAST(target_post as string), \n"
            + "    '\", \"status\": \"', CAST(status as string), \n"
            + "    '\", \"current_operator\": \"', current_operator, \n"
            + "    '\", \"fact_assign\": \"', CAST(fact_assign as string), \n"
            + "    '\", \"post\": \"', CAST(post as string), \n"
            + "    '\", \"customer_fields\": \"', case when customer_fields like '%operator_post%' and customer_fields like '%assign_company_id%' then concat('{\\\"operator_post\\\":',get_json_object(customer_fields, '$.operator_post'),',\\\"assign_company_id\\\":',get_json_object(customer_fields,'$.assign_company_id'),'}') when  customer_fields like '%operator_post%' then concat('{\\\"operator_post\\\":',get_json_object(customer_fields, '$.operator_post'),'}') when  customer_fields like '%assign_company_id%' then concat('{\\\"assign_company_id\\\":',get_json_object(customer_fields, '$.assign_company_id'),'}') else '' end, \n"
            + "    '\", \"responsible\": \"', responsible, \n"
            + "    '\", \"next_responsible\": \"', next_responsible, \n"
            + "    '\", \"request_source\": \"', request_source, \n"
            + "    '\", \"remark\": \"', COALESCE(REGEXP_EXTRACT(remark, '拒绝原因： (.*?)<br/>'), ''), \n"
            + "    '\", \"inner_reply\": \"', case when inner_reply like '%认领原因:工单回访%' then '认领原因:工单回访' "
            + "    when inner_reply<>'' and inner_reply is not null then '1' else '' end,  \n"
            + "    '\", \"extern_reply\": \"', "
            + "    case when extern_reply<>'' and extern_reply is not null then '1' else '' end, \n"
            + "    '\"  }') ,','),']')  as operation_data \n"
            + "   from iceberg_source_dwd_incident_ticket_operation "
            + "  /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s',"
            + "   'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + "    GROUP BY ticket_id\n"
            + "  ) as t1 ON t.ticket_id = t1.ticket_id\n"
            + "WHERE t.rn = 1";
}