package com.tencent.andata.etl.sql;

public class DwdTIncidentTicketAlarmRecordSql {

    public static String TICKET_ALARM_RECORD_PROCESS_SQL = ""
            + "SELECT\n"
            + "  a.id AS `id`,\n"
            + "  a.ticket_id AS `ticket_id`,\n"
            + "  a.urge_times AS `cnt`,\n"
            + "  a.warning_receiver AS `user_list`,\n"
            + "  a.warning_type AS `type`,\n"
            + "  a.create_time AS `create_time`,\n"
            + "  a.has_notice AS `has_notice`,\n"
            + "  COALESCE(\n"
            + "    simple_string_trans(SPLIT_INDEX(SPLIT_INDEX(a.has_notice, '|', 1), ':', 1), NULL),\n"
            + "    simple_string_trans(get_json_object(a.ticket, '$.post'), NULL)\n"
            + "  ) AS `post`,\n"
            + "  COALESCE(\n"
            + "    simple_string_trans(SPLIT_INDEX(SPLIT_INDEX(a.has_notice, '|', 3), ':', 1), NULL),\n"
            + "    simple_string_trans(get_json_object(a.ticket, '$.priority'), NULL)\n"
            + "  ) AS `priority`,\n"
            + "  a.ticket AS `ticket`,\n"
            + "  COALESCE(\n"
            + "    simple_string_trans(SPLIT_INDEX(SPLIT_INDEX(a.has_notice, '|', 2), ':', 1), NULL),\n"
            + "    simple_string_trans(get_json_object(a.ticket, '$.service_channel'), NULL)\n"
            + "  ) AS `service_channel`,\n"
            + "  simple_string_trans( \n"
            + "     get_json_object(a.ticket, '$.service_scene_checked'), \n"
            + "     NULL\n"
            + "  ) AS `service_scene_checked`,\n"
            + "  simple_string_trans(\n"
            + "    get_json_object(a.ticket, '$.operation_service_scene'), \n"
            + "    NULL\n"
            + "  ) AS `operation_service_scene`,\n"
            + "  simple_string_trans(\n"
            + "    get_json_object(a.ticket, '$.gradient.alarm_level'), \n"
            + "    NULL\n"
            + "  ) AS `level`,\n"
            + "  simple_string_trans(\n"
            + "    get_json_object(a.ticket, '$.current_operator'), \n"
            + "    NULL\n"
            + "  ) AS `current_operator`,\n"
            + "  simple_string_trans(get_json_object(a.ticket, '$.company_id'), NULL) AS `company`,\n"
            + "  simple_string_trans(get_json_object(a.ticket, '$.fact_assign'), NULL) AS `fact_assign`,\n"
            + "  simple_string_trans(get_json_object(a.ticket, '$.responsible'), NULL) AS `responsible`,\n"
            + "  get_json_object(a.ticket, '$.uin') AS `uin`,\n"
            + "  a.level_user_list AS `level_user_list`,\n"
            + "  a.next_level_user_list AS `next_level_user_list`,\n"
            + "  a.alarm_title AS `alarm_title`,\n"
            + "  a.ticket_operation AS `ticket_operation`,\n"
            + "  a.assign_error AS `assign_error`\n"
            + "FROM mysql_source_t210_ticket_alarm_record AS a\n"
            + "WHERE create_time >= '2023-08-20 00:00:00'";
}
