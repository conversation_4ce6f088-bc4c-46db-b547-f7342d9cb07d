package com.tencent.andata.etl.tablemap;

public class AppRiskControlTicketInfoMapping {

    public static String pgsqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_customer_staff_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_customer_staff_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_company_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_company_info\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_operation\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_incident_ticket_operation\",\n"
            + "        \"primaryKey\": \"operation_id\"\n"
            + "    }\n"
            + "]";
}