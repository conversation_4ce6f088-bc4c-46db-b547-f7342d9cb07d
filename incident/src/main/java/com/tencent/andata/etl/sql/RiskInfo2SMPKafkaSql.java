package com.tencent.andata.etl.sql;

public class RiskInfo2SMPKafkaSql {

    public static final String QUERY_RISK_INFO_SQL = ""
            + "WITH tmp1 AS (\n"
            + "    SELECT\n"
            + "      t1.id,\n"
            + "      t1.conversation_id,\n"
            + "      t1.risk_type,\n"
            + "      t2.risk_level,\n"
            + "      t2.is_valid,\n"
            + "      t1.`statement`,\n"
            + "      t2.is_created_chat,\n"
            + "      t2.is_annotated,\n"
            + "      t1.process_time\n"
            + "    FROM pg_source_opinion_webim t1\n"
            + "    LEFT JOIN pg_source_annotate_data t2\n"
            + "    ON (t1.id = t2.source_id \n"
            + "          AND t1.update_time BETWEEN t2.update_time - INTERVAL '12' HOUR \n"
            + "          AND t2.update_time + INTERVAL '12' HOUR)\n"
            + "    WHERE t1.update_time > '2023-11-29'\n"
            + "), tmp2 AS (\n"
            + "  SELECT\n"
            + "    tmp1.*,\n"
            + "    t2.conversation_ticket_ids as ticket_id\n"
            + "  FROM tmp1\n"
            + "  LEFT JOIN dwm_webim_base_view FOR SYSTEM_TIME AS OF tmp1.process_time t2\n"
            + "  ON tmp1.conversation_id = t2.conversation_id\n"
            + ")\n"
            + "SELECT\n"
            + "  tmp2.id,\n"
            + "  tmp2.conversation_id,\n"
            + "  CAST(tmp2.ticket_id AS BIGINT) AS ticket_id,\n"
            + "  tmp2.`statement`,\n"
            + "  tmp2.risk_type,\n"
            + "  tmp2.risk_level,\n"
            + "  tmp2.is_valid,\n"
            + "  tmp2.is_annotated,\n"
            + "  tmp2.is_created_chat\n"
            + "FROM tmp2 WHERE ticket_id IS NOT NULL\n"
            + "\n"
            + "UNION ALL \n"
            + "\n"
            + "SELECT\n"
            + "  t1.id,\n"
            + "  '' AS conversation_id,\n"
            + "  t1.ticket_id,\n"
            + "  t1.`statement`,\n"
            + "  t1.risk_type,\n"
            + "  t2.risk_level,\n"
            + "  t2.is_valid,\n"
            + "  t2.is_annotated,\n"
            + "  t2.is_created_chat\n"
            + "FROM pg_source_opinion_keywords t1\n"
            + "LEFT JOIN pg_source_annotate_data t2\n"
            + "ON (t1.id = t2.source_id \n"
            + "     AND t1.update_time BETWEEN t2.update_time - INTERVAL '12' HOUR \n"
            + "     AND t2.update_time + INTERVAL '12' HOUR)\n"
            + "WHERE t1.update_time > '2023-11-29' AND t1.ticket_id > 0\n"
            + "\n"
            + "UNION ALL\n"
            + "\n"
            + "SELECT\n"
            + "  t1.id,\n"
            + "  '' AS conversation_id,\n"
            + "  t1.ticket_id,\n"
            + "  t1.`statement`,\n"
            + "  t1.risk_type_list,\n"
            + "  t2.risk_level,\n"
            + "  t2.is_valid,\n"
            + "  t2.is_annotated,\n"
            + "  t2.is_created_chat\n"
            + "FROM pg_source_opinion_im_group t1\n"
            + "LEFT JOIN pg_source_annotate_data t2\n"
            + "ON (t1.id = t2.source_id \n"
            + "     AND t1.update_time BETWEEN t2.update_time - INTERVAL '12' HOUR \n"
            + "     AND t2.update_time + INTERVAL '12' HOUR)\n"
            + "WHERE t1.update_time > '2023-11-29' AND t1.ticket_id > 0";
}
