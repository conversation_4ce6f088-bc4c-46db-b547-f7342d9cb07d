package com.tencent.andata.etl.sql;

public class DwdScheduleDutySql {

    public static final String GET_CUSTOMER_SERVICE_SCHEDULE = ""
            + "SELECT\n"
            + "  MD5(MD5(CONCAT(t1.`user_id`, CAST(t1.`date` AS STRING)))) AS value_of_primary_key,\n"
            + "  t1.`user_id`,\n"
            + "  t3.`duty_name`,\n"
            + "  t1.`date`,\n"
            + "  t1.`work_shift_id`,\n"
            + "  t4.`time_start1`,\n"
            + "  t4.`time_end1`,\n"
            + "  t4.`time_start2`,\n"
            + "  t4.`time_end2`\n"
            + "FROM mysql_source_t103_schedule t1\n"
            + "LEFT JOIN mysql_source_t104_schedule_duty_map t2 ON (t1.`schedule_id` = t2.`schedule_id`)\n"
            + "LEFT JOIN mysql_source_t102_duty t3 ON (t2.`duty_id` = t3.`duty_id` AND t3.`is_valid` = 1)\n"
            + "LEFT JOIN mysql_source_t101_work_shift t4 ON (t1.`work_shift_id` = t4.`work_shift_id`)\n"
            + "WHERE t1.`date` > '2016-01-01'\n";




    public static final String GET_COMPANY_SCHEDULE = ""
            + "SELECT\n"
            + "  MD5(MD5(CONCAT(CAST(t1.schedule_id AS string), CAST(t2.company_id AS string), t2.work_id, "
            + "  CAST(t3.user_id AS string), CAST(t4.schedule_id AS string), CAST(t4.duty_id AS string), "
            + "  CAST(t5.duty_id AS string)))) AS value_of_primary_key,\n"
            + "  t5.duty_name,\n"
            + "  t3.user_name,\n"
            + "  t3.company_id,\n"
            + "  t1.work_id,\n"
            + "  t1.`date`,\n"
            + "  t2.time_start1,\n"
            + "  t2.time_end1,\n"
            + "  t2.time_start2,\n"
            + "  t2.time_end2\n"
            + "FROM mysql_source_t907_company_schedule t1\n"
            + "LEFT JOIN mysql_source_t906_company_work t2 ON (t1.company_id=t2.company_id and t1.work_id=t2.work_id)\n"
            + "LEFT JOIN mysql_source_t903_company_user t3 ON (t1.user_id = t3.user_id)\n"
            + "LEFT JOIN mysql_source_t908_company_schedule_duties t4 ON (t1.`schedule_id` = t4.`schedule_id`)\n"
            + "LEFT JOIN mysql_source_t102_duty t5 ON (t5.`duty_id` = t4.`duty_id` AND t5.`is_valid` = 1)\n"
            + "WHERE t1.`date` > '2016-01-01'\n";
}
