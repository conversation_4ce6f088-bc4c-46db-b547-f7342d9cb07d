package com.tencent.andata.etl.dwd;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableUtils;
import lombok.Builder;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.data.RowData;
import org.apache.iceberg.flink.TableLoader;
import org.apache.iceberg.flink.source.FlinkSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.tencent.andata.etl.sql.DwdIncidentTicketOperationDetailSql.QUERY_DWD_INCIDENT_TICKET_SQL;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketOperationDetailMapping.icebergTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;


@Builder
public class DwdIncidentTicketOperationDetail {

    private static final Logger LOG = LoggerFactory.getLogger(DwdIncidentTicketOperationDetail.class);
    private final String icebergDbName;
    private final String pgDbName;
    private final Long startSnapshot;
    private final String parallelism;
    private final boolean streaming;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        ObjectMapper mapper = new ObjectMapper();

        // 获取ODS Iceberg表
        org.apache.iceberg.Table dwdTicketTable = catalog.getTableInstance(
                icebergDbName, "dwd_incident_ticket"
        );
        TableLoader dwdTicketLoader = catalog.getTableLoaderInstance(
                icebergDbName, "dwd_incident_ticket"
        );
        DataStream<RowData> dwdTicketDataStream;
        if (streaming) {
            // 获取Streaming数据
            dwdTicketDataStream = FlinkSource
                    .forRowData()
                    .env(flinkEnv.env())
                    .startSnapshotId(startSnapshot)
                    .table(dwdTicketTable)
                    .tableLoader(dwdTicketLoader)
                    .streaming(true)
                    .build();
        } else {
            Configuration configuration = new Configuration();
            configuration.setString("table.exec.resource.default-parallelism", parallelism);
            configuration.setString("table.exec.iceberg.infer-source-parallelism.max", parallelism);
            // 批处理
            dwdTicketDataStream = FlinkSource
                    .forRowData()
                    .env(flinkEnv.env())
                    .table(dwdTicketTable)
                    .tableLoader(dwdTicketLoader)
                    .flinkConf(configuration)
                    .build();
        }

        tEnv.createTemporaryView("iceberg_source_dwd_incident_ticket", tEnv.fromDataStream(dwdTicketDataStream));

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog);

        Table tbl = tEnv.sqlQuery(QUERY_DWD_INCIDENT_TICKET_SQL);

        tEnv.createTemporaryView("dwd_incident_ticket_operation_detail_view", tbl);

        // 注册pg表
        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                "dwd_incident_ticket_operation_detail_view",
                "iceberg_sink_dwd_incident_ticket_operation_detail",
                tEnv.from("iceberg_sink_dwd_incident_ticket_operation_detail"),
                ICEBERG));
    }
}