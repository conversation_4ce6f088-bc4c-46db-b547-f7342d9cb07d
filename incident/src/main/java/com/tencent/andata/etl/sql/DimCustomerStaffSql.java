package com.tencent.andata.etl.sql;

public class DimCustomerStaffSql {

    public static final String CLEAN_STAFF_SQL = ""
            + "WITH t1 AS (\n"
            + "  SELECT\n"
            + "    t1.user_id AS uid,\n"
            + "    t1.user_id AS user_id,\n"
            + "    t1.user_name AS user_name,\n"
            + "    t1.company_id AS company_id,\n"
            + "    t2.group_id AS group_id,\n"
            + "    t2.company_id AS assign_company_id\n"
            + "  FROM mysql_source_t_users t1\n"
            + "  LEFT JOIN mysql_source_t903_company_user t2 ON t1.id = t2.user_id\n"
            + "  WHERE t1.company_id > 0\n"
            + "\n"
            + "  UNION ALL\n"
            + "\n"
            + "  SELECT\n"
            + "    t1.user_id AS uid,\n"
            + "    t1.user_id AS user_id,\n"
            + "    t1.user_name AS user_name,\n"
            + "    t1.company_id AS company_id,\n"
            + "    t2.group_id AS group_id,\n"
            + "    t2.assign_company_id AS assign_company_id\n"
            + "  FROM mysql_source_t_users t1\n"
            + "  LEFT JOIN mysql_source_t006_staff t2 ON t1.user_id = t2.user_id\n"
            + "  WHERE t1.company_id = 0\n"
            + "  \n"
            + "  UNION ALL\n"
            + "\n"
            + "  SELECT\n"
            + "    CAST(t1.id AS STRING) AS uid,\n"
            + "    t1.user_id AS user_id,\n"
            + "    t1.user_name AS user_name,\n"
            + "    t1.company_id AS company_id,\n"
            + "    t2.group_id AS group_id,\n"
            + "    t2.company_id AS assign_company_id\n"
            + "  FROM mysql_source_t_users t1\n"
            + "  LEFT JOIN mysql_source_t903_company_user t2 ON t1.id = t2.user_id\n"
            + "  WHERE t1.company_id > 0\n"
            + "\n"
            + "  UNION ALL\n"
            + "\n"
            + "  SELECT\n"
            + "    CAST(t1.id AS STRING) AS uid,\n"
            + "    t1.user_id AS user_id,\n"
            + "    t1.user_name AS user_name,\n"
            + "    t1.company_id AS company_id,\n"
            + "    t2.group_id AS group_id,\n"
            + "    t2.assign_company_id AS assign_company_id\n"
            + "  FROM mysql_source_t_users t1\n"
            + "  LEFT JOIN mysql_source_t006_staff t2 ON t1.user_id = t2.user_id\n"
            + "  WHERE t1.company_id = 0\n"
            + ")\n"
            + "SELECT\n"
            + "  *\n"
            + "FROM t1";

    public static final String QUERY_COMPANY_SQL = ""
            + "SELECT \n"
            + "  `id` AS `company_id`,\n"
            + "  `short_name` AS `company_short_name`,\n"
            + "  `name` AS `company_name`\n"
            + "FROM mysql_source_t_companies";
}
