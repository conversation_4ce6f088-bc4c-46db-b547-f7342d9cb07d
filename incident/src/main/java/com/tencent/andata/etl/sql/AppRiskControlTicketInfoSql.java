package com.tencent.andata.etl.sql;

public class AppRiskControlTicketInfoSql {

    public static final String QUERY_TICKET_OPERATION_SQL = ""
            + "SELECT\n"
            + "  t1.*,\n"
            + "  (UNIX_TIMESTAMP(CAST(operate_time AS STRING)) - 28800) AS ts,\n"
            + "  get_json_object(data, '$.create_time') AS create_time,\n"
            + "  get_json_object(data, '$.service_channel') AS service_channel\n"
            + "FROM iceberg_source_dwd_incident_ticket_operation "
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ AS t1\n"
            + "LEFT JOIN hbase_ticket_base_info FOR SYSTEM_TIME AS OF process_time AS t2 "
            + "ON CAST(t1.ticket_id AS STRING) = t2.rowkey";
}
