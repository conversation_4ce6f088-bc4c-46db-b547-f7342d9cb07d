package com.tencent.andata.etl.tablemap;

public class RiskInfoTableMapping {

    public static String smartyPgTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"public_opinion_keywords_source\",\n"
            + "        \"fTable\": \"pg_source_opinion_keywords\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"public_opinion_im_group_source\",\n"
            + "        \"fTable\": \"pg_source_opinion_im_group\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"public_opinion_webim_source\",\n"
            + "        \"fTable\": \"pg_source_opinion_webim\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"annotate_data\",\n"
            + "        \"fTable\": \"pg_source_annotate_data\"\n"
            + "    }\n"
            + "]";

    public static String dwPgTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwm_im_online_customer_service_backend_data\",\n"
            + "        \"fTable\": \"pg_source_im_online\"\n"
            + "    }\n"
            + "]";
}
