package com.tencent.andata.etl.sql;

public class DwdTIncidentTicketSql {

    public static final String FULL_JOIN_TICKET_SQL = ""
            + "SELECT\n"
            + "  CAST(t1.`ticket_id` AS BIGINT) AS `ticket_id`,\n"
            + "  CAST(t1.`uin` AS BIGINT) AS `uin`,\n"
            + "  t1.`related_region_code`,\n"
            + "  t1.`related_phone_number`,\n"
            + "  CAST(t1.`service_channel` AS BIGINT) AS `service_channel`,\n"
            + "  CAST(t1.`service_scene` AS BIGINT) AS `service_scene`,\n"
            + "  CAST(t1.`service_scene_checked` AS BIGINT) AS `service_scene_checked`,\n"
            + "  CAST(t1.`archive_type` AS BIGINT) AS `archive_type`,\n"
            + "  CAST(t1.`priority` AS BIGINT) AS `priority`,\n"
            + "  t1.`creator`,\n"
            + "  t1.`create_time`,\n"
            + "  t1.`current_operator`,\n"
            + "  t1.`last_operator`,\n"
            + "  t1.`last_staff`,\n"
            + "  t1.`last_operate_time`,\n"
            + "  t1.`question`,\n"
            + "  t1.`last_inner_reply`,\n"
            + "  CAST(t1.`status` AS BIGINT) AS `status`,\n"
            + "  t1.`operation_ticket_id`,\n"
            + "  t1.`responsible`,\n"
            + "  t1.`first_line_responsible`,\n"
            + "  t1.`second_line_responsible`,\n"
            + "  CAST(t1.`solve_status` AS BIGINT) AS `solve_status`,\n"
            + "  CAST(t1.`service_rate` AS BIGINT) AS `service_rate`,\n"
            + "  CAST(t1.`satisfaction` AS BIGINT) AS `satisfaction`,\n"
            + "  CAST(t1.`unsatisfy_reason` AS BIGINT) AS `unsatisfy_reason`,\n"
            + "  CAST(t1.`first_should_assign` AS BIGINT) AS `first_should_assign`,\n"
            + "  CAST(t1.`first_fact_assign` AS BIGINT) AS `first_fact_assign`,\n"
            + "  t1.`first_fact_assign_time`,\n"
            + "  CAST(t1.`should_assign` AS BIGINT) AS `should_assign`,\n"
            + "  CAST(t1.`fact_assign` AS BIGINT) AS `fact_assign`,\n"
            + "  t1.`fact_assign_time`,\n"
            + "  CAST(t1.`unsatisfy_ticket_id` AS BIGINT) AS `unsatisfy_ticket_id`,\n"
            + "  CAST(t1.`related_unsatisfied_ticket_id` AS BIGINT) AS `related_unsatisfied_ticket_id`,\n"
            + "  t1.`related_qcloud_ticket_id`,\n"
            + "  t1.`qcloud_ticket_id`,\n"
            + "  CAST(t1.`qcloud_category_id` AS BIGINT) AS `qcloud_category_id`,\n"
            + "  CAST(t1.`qcloud_receive_notice_flag` AS BIGINT) AS `qcloud_receive_notice_flag`,\n"
            + "  t1.`instance_id`,\n"
            + "  t1.`woodpecker_jobid`,\n"
            + "  t1.`appraise`,\n"
            + "  t1.`appraise_time`,\n"
            + "  t1.`next_follow_time`,\n"
            + "  CAST(t1.`qcloud_complaint_id` AS BIGINT) AS `qcloud_complaint_id`,\n"
            + "  CAST(t1.`complainted_ticket_id` AS BIGINT) AS `complainted_ticket_id`,\n"
            + "  CAST(t1.`language` AS BIGINT) AS `language`,\n"
            + "  t1.`callcenter_answer_time`,\n"
            + "  t1.`callcenter_hand_up_time`,\n"
            + "  CAST(t1.`callcenter_talk_duration` AS BIGINT) AS `callcenter_talk_duration`,\n"
            + "  CAST(t1.`callcenter_queue_duration` AS BIGINT) AS `callcenter_queue_duration`,\n"
            + "  t1.`callcenter_session_id`,\n"
            + "  t1.`callcenter_agent_id`,\n"
            + "  t1.`callcenter_agent_name`,\n"
            + "  t1.`callcenter_session_filename`,\n"
            + "  t1.`qqgroup_start_time`,\n"
            + "  t1.`qqgroup_end_time`,\n"
            + "  t1.`qqgroup_response_time`,\n"
            + "   CAST(t1.`qqgroup_response_duration` AS BIGINT) AS `qqgroup_response_duration`,\n"
            + "   CAST(t1.`qqgroup_number` AS BIGINT) AS `qqgroup_number`,\n"
            + "  t1.`qqgroup_responsable`,\n"
            + "  t1.`qqgroup_big_customer_operator`,\n"
            + "  t1.`secret_content`,\n"
            + "  t1.`update_time`,\n"
            + "  t1.`qcloud_last_customer_reply_time`,\n"
            + "  CAST(t1.`vip_ask_callback_id` AS BIGINT) AS `vip_ask_callback_id`,\n"
            + "  t1.`callback_time_start`,\n"
            + "  t1.`callback_time_end`,\n"
            + "  CAST(t1.`owner_uin` AS BIGINT) AS `owner_uin`,\n"
            + "  CAST(t1.`is_deleted` AS BIGINT) AS `is_deleted`,\n"
            + "  CAST(t1.`is_ever_to_wan` AS BIGINT) AS `is_ever_to_wan`,\n"
            + "  CAST(t1.`company_id` AS BIGINT) AS `company_id`,\n"
            + "  CAST(t1.`is_claim` AS BIGINT) AS `is_claim`,\n"
            + "  CAST(t1.`is_fault_report` AS BIGINT) AS `is_fault_report`,\n"
            + "  CAST(t1.`is_internal_go_up` AS BIGINT) AS `is_internal_go_up`,\n"
            + "  CAST(t1.`post` AS BIGINT) AS `post`,\n"
            + "  t1.`next_up_responsor`,\n"
            + "  t1.`next_up_time`,\n"
            + "  t1.`cc_person`,\n"
            + "  t1.`operation_service_scene`,\n"
            + "  t1.`responsible_should_assign`,\n"
            + "  t1.`chat_group_id`,\n"
            + "  CAST(t1.`first_assign_company_id` AS BIGINT) AS `first_assign_company_id`,\n"
            + "  CAST(t1.`feedback_channel` AS BIGINT) AS `feedback_channel`,\n"
            + "  t1.`group_id`,\n"
            + "  CAST(t1.`source_channel` AS BIGINT) AS `source_channel`,\n"
            + "  t1.`customer_contact_info`,\n"
            + "  CAST(t1.`question_category` AS BIGINT) AS `question_category`,\n"
            + "  CAST(t1.`reason_category` AS BIGINT) AS `reason_category`,\n"
            + "  t1.`question_start_time`,\n"
            + "  t1.`question_end_time`,\n"
            + "  t1.`title`,\n"
            + "  t1.`reason`,\n"
            + "  t1.`upgrade_time`,\n"
            + "  t1.`sales_supportor`,\n"
            + "  t1.`trade`,\n"
            + "  t1.`name`,\n"
            + "  CAST(t1.`event_tss_id` AS BIGINT) AS `event_tss_id`,\n"
            + "  CAST(t1.`add_info_application_post` AS BIGINT) AS `add_info_application_post`,\n"
            + "  CAST(t1.`add_info_application_should_assign` AS BIGINT) AS `add_info_application_should_assign`,\n"
            + "  t1.`add_info_application_operator`,\n"
            + "  t1.`tapd_story_id`,\n"
            + "  t1.`callcenter_skill_group`,\n"
            + "  CAST(t1.`keyissues_id` AS BIGINT) AS `keyissues_id`,\n"
            + "  CAST(t1.`complaint` AS BIGINT) AS `complaint`,\n"
            + "  t1.`complaint_content`,\n"
            + "  t1.`ltc_name`,\n"
            + "  t1.`product_version`,\n"
            + "  t1.`severity`,\n"
            + "  t1.`short`,\n"
            + "  CAST(t1.`extern_status` AS BIGINT) AS `extern_status`,\n"
            + "  t1.`caller`,\n"
            + "  t1.`affected_customers`,\n"
            + "  t1.`customer_uid`,\n"
            + "  t1.`incident_manager`,\n"
            + "  CAST(t1.`incident_manager_should_assign` AS BIGINT) AS incident_manager_should_assign,\n"
            + "  t1.`risk_control_first_time`,\n"
            + "  CAST(t1.`risk_control_num` AS BIGINT) AS `risk_control_num`,\n"
            + "  CAST(t1.`is_incident_manager_into` AS BIGINT) AS `is_incident_manager_into`,\n"
//            + "  t2.`order_id`,\n"
//            + "  t2.`type`,\n"
//            + "  t2.`service_type`,\n"
//            + "  t2.`ltc_id`,\n"
//            + "  t2.`service_stage`,\n"
//            + "  t2.`product_name`,\n"
//            + "  t2.`service_mode`,\n"
//            + "  t2.`region`,\n"
//            + "  t2.`project_stage`,\n"
//            + "  t2.`project_id`,\n"
//            + "  t2.`judian_name`,\n"
//            + "  t2.`jid`,\n"
//            + "  t2.`judian_status`,\n"
//            + "  t2.`trade` AS `industry`,\n"
            + "  'https://andon.woa.com/ticket/detail/?id=' "
            + "   || CAST(t1.`ticket_id` AS STRING) "
            + "   || '&sign=' "
            + "   || MD5(MD5(CAST(t1.`ticket_id` AS STRING) || 'andontcs')) AS url,\n"
            + "   'https://andon.cloud.tencent.com/ticket/nologin/redirect?id=' "
            + "   || CAST(t1.`ticket_id` AS STRING) "
            + "   || '&sign=' "
            + "   || MD5(MD5(CAST(t1.`ticket_id` AS STRING) || 'andontcs')) AS short_url\n"
            + "FROM mysql_source_t201_ticket AS t1\n";
//          + "FULL JOIN mysql_source_t201_ticket_tce_insales AS t2 ON t1.ticket_id = t2.ticket_id\n";
//          + "WHERE t1.update_time >= '2023-08-20'";


    public static final String QUERY_T202_TICKET_OPERATION_SQL = ""
            + "SELECT \n"
            + "  CAST(operation_id AS BIGINT) AS operation_id,\n"
            + "  CAST(ticket_id AS BIGINT) AS ticket_id,\n"
            + "  operate_time,\n"
            + "  operator,\n"
            + "  CAST(operator_type AS BIGINT) AS operator_type,\n"
            + "  inner_reply,\n"
            + "  extern_reply,\n"
            + "  CAST(qcloud_comment_id AS BIGINT) AS qcloud_comment_id,\n"
            + "  CAST(target_status AS BIGINT) AS target_status,\n"
            + "  next_operator,\n"
            + "  CAST(next_assign AS BIGINT) AS next_assign,\n"
            + "  CAST(duration AS BIGINT) AS duration,\n"
            + "  remark,\n"
            + "  secret_content,\n"
            + "  CAST(is_undo AS BIGINT) AS is_undo,\n"
            + "  CAST(company_id AS BIGINT) AS company_id,\n"
            + "  CAST(target_post AS BIGINT) AS target_post,\n"
            + "  cc_person,\n"
            + "  CAST(operation_type AS BIGINT) AS operation_type,\n"
            + "  CAST(status AS BIGINT) AS status,\n"
            + "  current_operator,\n"
            + "  CAST(fact_assign AS BIGINT) AS fact_assign,\n"
            + "  CAST(post AS BIGINT) AS post,\n"
            + "  responsible,\n"
            + "  next_responsible,\n"
            + "  customer_fields,\n"
            + "  request_source,\n"
            + "  CAST(target_extern_status AS BIGINT) AS target_extern_status\n"
            + "FROM mysql_source_t202_ticket_operation\n";
    //+ "WHERE operate_time >= '2023-08-20'";


    public static final String FLATTEN_TICKET_QUALITY_CATEGORY_SQL = ""
            + "WITH\n"
            + "  level1 AS (\n"
            + "    SELECT\n"
            + "      id AS quality_category_level1_id,\n"
            + "      name AS quality_category_level1_name,\n"
            + "      '' quality_category_level2_id,\n"
            + "      '' quality_category_level2_name,\n"
            + "      '' quality_category_level3_id,\n"
            + "      '' quality_category_level3_name\n"
            + "    FROM mysql_source_t251_ticket_quality_category\n"
            + "    WHERE parent_id = 0\n"
            + "  ),\n"
            + "  level2 AS (\n"
            + "    SELECT\n"
            + "      b.quality_category_level1_id,\n"
            + "      b.quality_category_level1_name,\n"
            + "      a.id AS quality_category_level2_id,\n"
            + "      a.name AS quality_category_level2_name,\n"
            + "      '' quality_category_level3_id,\n"
            + "      '' quality_category_level3_name\n"
            + "    FROM level1 AS b\n"
            + "    LEFT JOIN mysql_source_t251_ticket_quality_category AS a "
            + "    ON a.parent_id = b.quality_category_level1_id\n"
            + "  ),\n"
            + "  level3 AS (\n"
            + "    SELECT\n"
            + "      COALESCE(a.id, b.quality_category_level2_id, b.quality_category_level1_id) "
            + "      AS flag_id,\n"
            + "      b.quality_category_level1_id,\n"
            + "      b.quality_category_level1_name,\n"
            + "      b.quality_category_level2_id,\n"
            + "      b.quality_category_level2_name,\n"
            + "      a.id AS quality_category_level3_id,\n"
            + "      a.name AS quality_category_level3_name\n"
            + "    FROM level2 AS b\n"
            + "    LEFT JOIN mysql_source_t251_ticket_quality_category AS a "
            + "    ON a.parent_id = b.quality_category_level2_id\n"
            + "  )\n"
            + "SELECT\n"
            + "  CASE\n"
            + "    WHEN CAST(flag_id AS BIGINT)=quality_category_level3_id "
            + "    THEN CAST(quality_category_level3_id AS BIGINT)\n"
            + "    WHEN CAST(flag_id AS BIGINT)=quality_category_level2_id "
            + "    THEN CAST(quality_category_level2_id AS BIGINT)\n"
            + "    WHEN CAST(flag_id AS BIGINT)=quality_category_level1_id "
            + "    THEN CAST(quality_category_level1_id AS BIGINT)\n"
            + "  END AS dim_id,\n"
            + "  CAST(quality_category_level1_id AS BIGINT) AS quality_category_level1_id,\n"
            + "  CAST(quality_category_level2_id AS BIGINT) AS quality_category_level2_id,\n"
            + "  CAST(quality_category_level3_id AS BIGINT) AS quality_category_level3_id,\n"
            + "  quality_category_level1_name,\n"
            + "  quality_category_level2_name,\n"
            + "  quality_category_level3_name\n"
            + "FROM level3\n"
            + "\n"
            + "UNION\n"
            + "\n"
            + "SELECT\n"
            + "  CAST(quality_category_level2_id AS BIGINT) AS dim_id,\n"
            + "  CAST(quality_category_level1_id AS BIGINT) AS quality_category_level1_id,\n"
            + "  CAST(quality_category_level2_id AS BIGINT) AS quality_category_level2_id,\n"
            + "  CAST(quality_category_level3_id AS BIGINT) AS quality_category_level3_id,\n"
            + "  quality_category_level1_name,\n"
            + "  quality_category_level2_name,\n"
            + "  quality_category_level3_name\n"
            + "FROM level2\n"
            + "WHERE CAST(quality_category_level2_id AS BIGINT) IS NOT NULL\n"
            + "\n"
            + "UNION\n"
            + "\n"
            + "SELECT\n"
            + "  CAST(quality_category_level1_id AS BIGINT) AS dim_id,\n"
            + "  CAST(quality_category_level1_id AS BIGINT) AS quality_category_level1_id,\n"
            + "  CAST(quality_category_level2_id AS BIGINT) AS quality_category_level2_id,\n"
            + "  CAST(quality_category_level3_id AS BIGINT) AS quality_category_level3_id,\n"
            + "  quality_category_level1_name,\n"
            + "  quality_category_level2_name,\n"
            + "  quality_category_level3_name\n"
            + "FROM level1\n"
            + "WHERE CAST(quality_category_level1_id AS BIGINT) IS NOT NULL\n";
    public static final String QUERY_T226_TICKET_ACCESS_RECORD_SQL = ""
            + "SELECT \n"
            + "  CAST(id AS BIGINT) AS id,\n"
            + "  CAST(ticket_id AS BIGINT) AS ticket_id,\n"
            + "  user_id,\n"
            + "  CAST(access_source AS BIGINT) AS access_source,\n"
            + "  url,\n"
            + "  params,\n"
            + "  create_time,\n"
            + "  CAST(is_report AS BIGINT) AS is_report,\n"
            + "  get_json_object(params,'$.sign') AS sign,\n"
            + "  get_json_object(params,'$.olauuid') AS olauuid\n"
            + "FROM mysql_source_t226_ticket_access_record\n";
    // + "WHERE create_time >= '2023-08-20 00:00:00'";
    // 这段逻辑放在后续twd平台处理
    public static String QUERY_TICKET_CUSTOM_FIELD_ORIGIN_SOLVE_SQL = ""
            + "SELECT\n"
            + "  ticket_id,\n"
            + "  MAX(CASE "
            + "        WHEN custom_field_key='origin_appeal' THEN custom_field_value "
            + "        ELSE NULL END"
            + "  ) AS origin_appeal,\n"
            + "  MAX(CASE "
            + "        WHEN custom_field_key='solve_proposal' THEN custom_field_value "
            + "      ELSE NULL END"
            + "  ) AS solve_proposal\n"
            + "FROM t208_ticket_custom_field_view\n"
            + "WHERE custom_field_key IN ('origin_appeal','solve_proposal')\n"
            + "GROUP BY ticket_id";
}
