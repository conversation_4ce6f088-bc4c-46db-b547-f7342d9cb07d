package com.tencent.andata.etl.sql;

public class DwmIncidentTicketEsSql {

    public static final String GET_ES_DATA_SQL = ""
            + "SELECT \n"
            + "    'smart_assist-incident_ticket-' || CAST(t1.`ticket_id` AS STRING) AS id,\n"
            + "    '' AS `content`,\n"
            + "    CAST(t1.`ticket_id` AS STRING) AS _ticket_id,\n"
            + "    IF(t1.`title` = '' OR t1.`title` IS NULL, t1.`question`, t1.`title`) AS title,\n"
            + "    CAST(t1.`create_time` AS TIMESTAMP(0)) AS _create_time,\n"
            + "    CAST(t1.`update_time` AS TIMESTAMP(0)) AS _update_time,\n"
            + "    false AS deleted,\n"
            + "  'https://intl.andon.woa.com/ticket/detail/?id=' "
            + "   || CAST(t1.`ticket_id` AS STRING) "
            + "   || '&sign=' "
            + "   || MD5(MD5(CAST(t1.`ticket_id` AS STRING) || 'andontcs')) AS short_url,\n"
            + "    'incident_ticket' AS source_type,\n"
            + "    'smart_assist' AS source_name,\n"
            + "    COALESCE(t1.`service_channel`, 0) AS service_channel,\n"
            + "    COALESCE(t1.`service_scene_checked`, t1.`service_scene`, 0) AS service_scene_checked,\n"
            + "    t1.`status`,\n"
            + "    t1.`current_operator`,\n"
            + "    t1.`post`,\n"
            + "    t1.`should_assign`,\n"
            + "    t1.`fact_assign`,\n"
            + "    t1.`company_id`,\n"
            + "    t1.`responsible`,\n"
            + "    t1.`priority`,\n"
            + "    t1.`language`,\n"
            + "    '' AS operators,\n"
            + "    t1.`uin`,\n"
            + "    t1.`owner_uin`,\n"
            + "    '' AS operation_data,\n"
            + "    t2.`appid` AS app_id,\n"
            + "    t1.`customer_contact_info` AS customer_contact_info,\n"
            + "    t1.`sales_supportor` AS sales_supportor,\n"
            + "    t1.`ltc_name` AS ltc_name,\n"
            + "    t1.`service_rate` AS service_rate,\n"
            + "    t1.`first_assign_company_id`,\n"
            + "    t2.`owner_name` AS owner_name,\n"
            + "    t1.`name` AS name\n"
            + "FROM iceberg_source_dwd_incident_ticket_base_info\n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='3s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ AS t1\n"
            + "LEFT JOIN pg_source_dim_customer FOR SYSTEM_TIME AS OF t1.`process_time` AS t2\n"
            + "ON CONCAT(CAST(t1.`uin` AS STRING), '-', CAST(t1.`owner_uin` AS STRING)) = t2.`pk`";

    public static final String GET_EXTRA_DATA_SQL = ""
            + "SELECT \n"
            + "    'smart_assist-incident_ticket-' || CAST(t1.`ticket_id` AS STRING) AS id,\n"
            + "    CAST(t1.`ticket_id` AS STRING) AS _ticket_id,\n"
            + "    CAST(t1.`close_time` AS TIMESTAMP(0)) AS close_time,\n"
            + "    CAST(t1.`handled_companies` AS STRING) AS handled_companies,\n"
            + "    CAST(t1.`apply_close_tester` AS STRING) AS apply_close_tester,\n"
            + "    COALESCE(t1.`is_transfer_production_research`, 0) AS is_transfer_production_research\n"
            + "FROM iceberg_source_dwd_incident_ticket_operation_extra\n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='3s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ AS t1\n";
}