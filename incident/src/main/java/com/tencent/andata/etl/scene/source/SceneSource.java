package com.tencent.andata.etl.scene.source;


import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

import com.tencent.andata.etl.scene.operator.SceneRichFlatMapFun;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.cdc.deserializer.RowKindJsonDeserializationSchemaBase.TableIRowKindJson;
import com.tencent.andata.utils.cdc.source.CDCSourceViewFactory;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.connector.source.Source;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;

@Slf4j
public class SceneSource {

    public static void main(String[] args) throws Exception {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        FlinkEnv fEnv = FlinkEnvUtils.getStreamTableEnv(args);
        StreamExecutionEnvironment env = fEnv.env();
        StreamTableEnvironment tEnv = fEnv.streamTEnv();
        Configuration configuration = tEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.legacy-cast-behaviour", "enabled");

        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        final KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf dbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();

        final DatabaseConf rsConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        final DatabaseConf pgConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", "dataware"))
                .build();

        final DatabaseConf s360Conf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", "s360"))
                .build();

        final ObjectMapper mapper = new ObjectMapper();

        // pgsql table mapping to flink table
        ArrayNode pgsqlTable2FlinkTableMap = mapper.readValue(
                "[\n"
                        + " {\n"
                        + "   \"rdbTable\":\"dim_service_scenes\",\n"
                        + "   \"fTable\":\"flink_dataware_dim_service_scenes\",\n"
                        + "   \"primaryKey\": \"dim_id\"\n"
                        + "  }\n"
                        + "]", ArrayNode.class);

        TableUtils.rdbTable2FlinkTable(pgConf, pgsqlTable2FlinkTableMap, PGSQL, tEnv);

        ArrayNode pgTable2FlinkTableMap = mapper.readValue(
                "[\n"
                        + " {\n"
                        + "   \"rdbTable\":\"dim_service_scenes\",\n"
                        + "   \"fTable\":\"flink_s360_dim_service_scenes\",\n"
                        + "   \"primaryKey\": \"dim_id\"\n"
                        + "  }\n"
                        + "]", ArrayNode.class);

        TableUtils.rdbTable2FlinkTable(pgConf, pgTable2FlinkTableMap, PGSQL, tEnv);

        // starrocks table mapping to flink table
        ArrayNode starRocksTable2FlinkTableMap = mapper.readValue(
                "[\n"
                        + " {\n"
                        + "   \"rdbTable\":\"dim_service_scenes\",\n"
                        + "   \"fTable\":\"flink_starrocks_dim_service_scenes\",\n"
                        + "   \"primaryKey\": \"dim_id\"\n"
                        + "  }\n"
                        + "]", ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(rsConf, starRocksTable2FlinkTableMap, ROCKS, tEnv);

        SingleOutputStreamOperator<TableIRowKindJson> ds = getSceneSource(fEnv, dbConf);

        assert ds != null;
        SingleOutputStreamOperator<Row> dataStream = ds.flatMap(new SceneRichFlatMapFun(dbConf))
                .uid("flatten-scene")
                .returns(Types.ROW_NAMED(new String[]{"dim_id", "service_scene_level1_id", "service_scene_level1_name",
                                "service_scene_level2_id", "service_scene_level2_name", "service_scene_level3_id",
                                "service_scene_level3_name", "service_scene_level4_id", "service_scene_level4_name",
                                "status", "disabled", "obs_id", "property", "update_time", "modify_time"},
                        Types.LONG, Types.LONG, Types.STRING, Types.LONG, Types.STRING, Types.LONG, Types.STRING,
                        Types.LONG, Types.STRING, Types.INT, Types.INT, Types.STRING, Types.STRING,
                        Types.SQL_TIMESTAMP, Types.SQL_TIMESTAMP));

        Table table = tEnv.fromChangelogStream(
                dataStream,
                Schema.newBuilder().primaryKey("dim_id").build(),
                ChangelogMode.upsert()
        );

        tEnv.createTemporaryView("src_view", table);

        StatementSet stmtSet = fEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                        "src_view",
                        "flink_dataware_dim_service_scenes",
                        tEnv.from("flink_dataware_dim_service_scenes"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "src_view",
                        "flink_s360_dim_service_scenes",
                        tEnv.from("flink_s360_dim_service_scenes"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "src_view",
                        "flink_starrocks_dim_service_scenes",
                        tEnv.from("flink_starrocks_dim_service_scenes"),
                        ROCKS));
        stmtSet.execute();
        env.execute("MySQL CDC + Changelog");
    }

    public static SingleOutputStreamOperator<TableIRowKindJson> getSceneSource(FlinkEnv fEnv, DatabaseConf dbConf) {
        StreamExecutionEnvironment env = fEnv.env();
        List<String> tableList = Collections.singletonList("t217_ticket_scenes");
        try {
            // 通过反射获取私有方法的执行
            CDCSourceViewFactory sourceFactory = new CDCSourceViewFactory();
            // 获取 MyClass 类的 Class 对象
            Class<?> cls = sourceFactory.getClass();
            // 获取私有方法 myPrivateMethod
            Method getMySQLSource = cls.getDeclaredMethod("getMySQLSource", DatabaseConf.class, List.class);
            // 设置为可访问
            getMySQLSource.setAccessible(true);
            Source<TableIRowKindJson, ?, ?> source =
                    (Source<TableIRowKindJson, ?, ?>) getMySQLSource.invoke(sourceFactory, dbConf, tableList);
            return env.fromSource(source, WatermarkStrategy.noWatermarks(), "MySQL Source");
        } catch (Exception e) {
            log.error("An error occurred while fetching data from MySQL：" + e.getMessage());
        }
        return null;
    }
}
