package com.tencent.andata.etl.dim;

import com.tencent.andata.utils.*;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Properties;

import static com.tencent.andata.etl.sql.DimCustomerSql.QUERY_CUSTOMER_SQL;
import static com.tencent.andata.etl.tablemap.DimCustomerMapping.*;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.*;
@Builder
public class DimCustomer {
    private final String icebergDbName;
    private final String pgDbName;
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {

        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();

        // get config from property file
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf mysqlWorkDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();

        DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlWorkTable2FlinkTableMap = mapper.readValue(mysqlWorkTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlWorkDBConf, mysqlWorkTable2FlinkTableMap, MYSQL, tEnv);


        // pgsql table mapping to flink table
        ArrayNode pgsqlTable2FlinkTableMap = mapper.readValue(pgsqlTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(pgsqlDBConf, pgsqlTable2FlinkTableMap, PGSQL, tEnv);

        // icebergTable mapping to flinkTable
        ArrayNode icebergTable2FlinkTableMap = mapper.readValue(icebergTable2FlinkTable, ArrayNode.class);
        TableUtils.icebergTable2FlinkTable(this.icebergDbName, icebergTable2FlinkTableMap, tEnv, catalog);

        tEnv.createTemporaryView("dim_customer_view", tEnv.sqlQuery(QUERY_CUSTOMER_SQL));

        StatementSet stmtSet = flinkEnv.stmtSet();

        stmtSet.addInsertSql(insertIntoSql(
                        "dim_customer_view",
                        "pgsql_sink_dim_customer", // 客户维表写入pgsql
                        tEnv.from("pgsql_sink_dim_customer"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "dim_customer_view",
                        "iceberg_sink_dim_customer", // 供应商维表写入iceberg
                        tEnv.from("iceberg_sink_dim_customer"),
                        ICEBERG
                ));
    }
}
