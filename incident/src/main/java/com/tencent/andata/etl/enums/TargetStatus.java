package com.tencent.andata.etl.enums;

import io.vavr.collection.List;

public enum TargetStatus {
    UNKNOWN(0, "未知"),
    CLOSED(3, "已结单"),

    DELETED(11, "已删除"),

    PENDING(21, "待处理"),

    IN_HAND(22, "处理中"),

    RESTORED(26, "已恢复分析根因"),

    CLOSE_APPLICATION(23, "申请结单"),

    NOT_RESTORED_ANALYSIS(28, "待查根因"),

    TO_BE_ADDED_BY_CUSTOMER(4, "待补充"),

    TO_BE_VALIDATION(29, "待客户实施或验证"),

    PENDING_CHANGE(27, "待变更（需出包修复）"),

    TO_CONFIRM_RESTORE(25, "待确认业务恢复"),

    ADD_INFO_APPLICATION(24, "申请补充或待复现"),

    TO_BE_PACKAGED(30, "待出包"),

    CLOSE_CONFIRMATION_PENDING(5, "待确认结单");


    private final int code;
    private final String description;

    TargetStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static TargetStatus fromCode(int code) {
        return List.of(values())
                .filter(t -> t.code == code)
                .getOrElseThrow(() -> new IllegalArgumentException("Unknown target status: " + code));
    }
}