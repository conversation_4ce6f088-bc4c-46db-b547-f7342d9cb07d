package com.tencent.andata.etl.tablemap;

public class DwmIncidentTicketEsMapping {
    public static String pgsqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_customer\",\n"
            + "        \"fTable\": \"pg_source_dim_customer\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_incident_ticket_base_info\",\n"
            + "        \"primaryKey\": \"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_operation_extra\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_incident_ticket_operation_extra\",\n"
            + "        \"primaryKey\": \"ticket_id\"\n"
            + "    }\n"
            + "]";
}