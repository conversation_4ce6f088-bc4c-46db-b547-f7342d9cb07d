package com.tencent.andata.etl.sql;

public class DimStaffInfoSql {

    public static String deDupSQL = ""
            + "SELECT *\n"
            + "FROM (\n"
            + "  SELECT\n"
            + "    CURRENT_ROW_TIMESTAMP() AS dim_create_time,\n"
            + "    value_of_primary_key,\n"
            + "    CAST(record_update_time AS TIMESTAMP) msg_time,\n"
            + "    user_id,\n"
            + "    user_name,\n"
            + "    CAST(group_id AS BIGINT) AS group_id,\n"
            + "    group_name,\n"
            + "    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY record_update_time DESC) rn\n"
            + "  FROM iceberg_source_ods_staff_info /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + ") t\n"
            + "WHERE rn = 1";

    public static String insertIntoPg(String srcName, String dstName) {
        return String.format("INSERT INTO %s /*+ OPTIONS('sink.parallelism'='1') */ \n"
                + "SELECT\n"
                + "  dim_create_time,\n"
                + "  value_of_primary_key,\n"
                + "  msg_time,\n"
                + "  user_id,\n"
                + "  user_name,\n"
                + "  group_id,\n"
                + "  group_name,\n"
                + "  CURRENT_ROW_TIMESTAMP() AS record_update_time\n"
                + "FROM %s", dstName, srcName);
    }

    public static String insertIntoIceberg(String srcName, String dstName) {
        return String.format("INSERT INTO %s "
                + "/*+ OPTIONS('sink.parallelism'='1', "
                + "'equality-field-columns'='user_id', "
                + "'upsert-enabled'='true') */ \n"
                + "SELECT\n"
                + "  dim_create_time,\n"
                + "  value_of_primary_key,\n"
                + "  msg_time,\n"
                + "  user_id,\n"
                + "  user_name,\n"
                + "  group_id,\n"
                + "  group_name\n"
                + "FROM %s", dstName, srcName);
    }
}
