package com.tencent.andata.etl.scene.operator;

import static com.tencent.andata.etl.scene.helper.BuildTree.buildTree;
import static com.tencent.andata.etl.scene.helper.BuildTree.convertJsonStrToScene;
import static com.tencent.andata.etl.scene.helper.BuildTree.flattenScene;
import static com.tencent.andata.etl.scene.model.DimServiceScenes.buildDimServiceScenes;
import static com.tencent.andata.utils.JdbcUtil.queryList;

import com.tencent.andata.etl.scene.model.DimServiceScenes;
import com.tencent.andata.etl.scene.model.Scene;
import com.tencent.andata.utils.cdc.deserializer.RowKindJsonDeserializationSchemaBase.TableIRowKindJson;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.jetbrains.annotations.NotNull;

@Slf4j

public class SceneRichFlatMapFun extends RichFlatMapFunction<TableIRowKindJson, Row> {

    private static final ObjectMapper mapper = new ObjectMapper();
    // 存储归档树（key: 归档Id, value: 归档树节点）
    public static ConcurrentHashMap<Long, Scene> sceneNodeMap = new ConcurrentHashMap<>();
    private final DatabaseConf dbCon;
    private Connection conn;

    public SceneRichFlatMapFun(DatabaseConf dbCon) {
        this.dbCon = dbCon;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        String url = "*************************************************************************************";
        conn = DriverManager.getConnection(String.format(url, dbCon.dbHost, dbCon.dbPort, dbCon.dbName),
                dbCon.userName,
                dbCon.password);

        String querySql = "SELECT "
                + "id, name, status, disabled, parent_id, obs_id, property, update_time, modify_time "
                + "FROM t217_ticket_scenes";
        List<Scene> sceneList = queryList(conn, querySql, Scene.class, true);
        Scene root = buildTree(sceneList);
        List<Scene> flattenedSceneNodes = flattenScene(root);

        // 缓存全局的树节点，键是节点的 ID，值是节点本身
        flattenedSceneNodes.forEach(node -> sceneNodeMap.put(node.id, node));
    }

    @Override
    public void flatMap(TableIRowKindJson input, Collector<Row> collector) throws Exception {
        JsonNode json = mapper.readValue(input.getJson(), JsonNode.class);
        long id = json.get("id").asLong();
        Scene node = sceneNodeMap.get(id);

        if (node == null) {
            // 如果节点不存在，创建一个新的节点
            node = convertJsonStrToScene(input.getJson());
            sceneNodeMap.put(id, node);
        } else {
            // 如果节点已经存在，更新其父节点
            Scene oldParentNode = sceneNodeMap.get(node.parentId);
            if (oldParentNode != null) {
                oldParentNode.children.remove(node);
            }
            node.setStatus(json.get("status").asInt());
            node.setDisabled(json.get("disabled").asInt());
            node.setParentId(json.get("parent_id").asLong());
            node.setName(json.get("name").asText(""));
            node.setObsId(json.get("obs_id").asText(""));
            node.setProperty(json.get("property").asText(""));
        }

        // 如果节点已存在，同样需要更新
        // 因为 节点的其他属性可能会变
        sceneNodeMap.put(id, node);

        Scene newParentNode = sceneNodeMap.get(node.parentId);
        if (newParentNode != null) {
            newParentNode.children.add(node);
        }

        List<Scene> flattenedScenes = flattenScene(sceneNodeMap.get(id));
        for (Scene sceneNode : flattenedScenes) {
            List<Scene> scenePath = flattenSceneFromLeaf(sceneNode);
            // 构建Row
            Row rowData = getRow(input.getRowKind(), buildDimServiceScenes(scenePath));
            collector.collect(rowData);
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        conn.close();
    }

    private List<Scene> flattenSceneFromLeaf(Scene scene) {
        List<Scene> result = Collections.synchronizedList(new ArrayList<>());
        if (scene == null) {
            return result;
        }
        result = flattenSceneFromLeaf(sceneNodeMap.get(scene.getParentId()));
        result.add(scene);
        return result;
    }

    @NotNull
    private Row getRow(RowKind rowKind, @NotNull DimServiceScenes dimServiceScenes) {
        Row rowData = Row.withPositions(15);
        rowData.setKind(rowKind);
        rowData.setField(0, dimServiceScenes.dimId);
        rowData.setField(1, dimServiceScenes.serviceSceneLevel1Id);
        rowData.setField(2, dimServiceScenes.serviceSceneLevel1Name);
        rowData.setField(3, dimServiceScenes.serviceSceneLevel2Id);
        rowData.setField(4, dimServiceScenes.serviceSceneLevel2Name);
        rowData.setField(5, dimServiceScenes.serviceSceneLevel3Id);
        rowData.setField(6, dimServiceScenes.serviceSceneLevel3Name);
        rowData.setField(7, dimServiceScenes.serviceSceneLevel4Id);
        rowData.setField(8, dimServiceScenes.serviceSceneLevel4Name);
        rowData.setField(9, dimServiceScenes.status);
        rowData.setField(10, dimServiceScenes.disabled);
        rowData.setField(11, dimServiceScenes.obsId);
        rowData.setField(12, dimServiceScenes.property);
        rowData.setField(13, dimServiceScenes.updateTime);
        rowData.setField(14, dimServiceScenes.modifyTime);
        return rowData;
    }
}
