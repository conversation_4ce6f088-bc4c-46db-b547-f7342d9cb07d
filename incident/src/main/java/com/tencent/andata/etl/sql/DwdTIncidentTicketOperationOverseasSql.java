package com.tencent.andata.etl.sql;

public class DwdTIncidentTicketOperationOverseasSql {

    public static final String FULL_JOIN_TICKET_OPERATION_SQL = ""
            + "SELECT\n"
            + "cast(t1.operation_id as bigint) as dwd_id,\n"
            + "True as dwd_is_valid,\n"
            + "cast(t1.operation_id as bigint)as value_of_primary_key,\n"
            + "now() as dwd_create_time,\n"
            + "now() as record_update_time,\n"
            + "cast(t1.operation_id as bigint) as operation_id,\n"
            + "cast(t1.ticket_id as bigint) as ticket_id,\n"
            + "t1.responsible,\n"
            + "t1.next_responsible,\n"
            + "t1.operate_time,\n"
            + "t1.operator,\n"
            + "COALESCE(t10.duty_name,cast(t1.fact_assign as string)) as fact_assign,\n"
            + "operator_type,\n" // todo 客服 --2
            + "post,\n" // todo 一线 --2
            + "t1.inner_reply,\n"
            + "t1.extern_reply,\n"
            + "cast(t1.qcloud_comment_id as string) as qcloud_comment_id,\n"
            + "target_status,\n" // todo 待处理 --21
            + "status,\n"
            + "t1.next_operator,\n"
            + "COALESCE(t11.duty_name,cast(t1.next_assign as string)) as next_assign,\n"
            + "t1.duration,\n"
            + "t1.remark,\n"
            + "t1.secret_content,\n"
            + "cast(t1.is_undo as string) as is_undo,\n"
            + "t12.name as company_id,\n" // todo 腾讯云 --0
            + "target_post,\n" // todo 1.5线 --3
            + "t1.cc_person,\n"
            + "operation_type,\n" // todo 转单 --5
            + "CAST('' AS VARCHAR(64)) as operator_post,\n" //todo 一线 需要计算
            + "t1.customer_fields,\n"
            + "t1.request_source\n,"
            + "CAST('' AS VARCHAR(64)) as target_extern_status,\n"
            + "CAST('' AS VARCHAR(64)) as operator_arch,\n"
            + "CAST('' AS VARCHAR(64)) as responsible_arch,\n"
            + "CAST('' AS VARCHAR(64)) as next_responsible_arch,\n"
            + "CAST('' AS VARCHAR(64)) as next_operator_arch,\n"
            + "CAST('' AS STRING) as next_operator_company,\n"
            + "CAST('' AS STRING) as next_responsible_company\n"
            + "FROM mysql_source_t202_ticket_operation AS t1\n"
            + "LEFT JOIN mysql_source_t102_duty AS t10 ON t1.fact_assign = t10.duty_id\n"
            + "LEFT JOIN mysql_source_t102_duty AS t11 ON t1.next_assign = t11.duty_id\n"
            + "LEFT JOIN mysql_source_t_companies AS t12  ON t1.company_id = t12.id\n";
}
