package com.tencent.andata.etl.sql;

public class DwmIncidentTicketStatisticSql {

    public static final String QUERY_DWD_INCIDENT_TICKET_SQL = ""
            + "SELECT\n"
            + "  t.`ticket_id`,\n"
            + "  t.`uin`,\n"
            + "  t.`related_region_code`,\n"
            + "  t.`related_phone_number`,\n"
            + "  t.`service_channel`,\n"
            + "  t.`service_scene`,\n"
            + "  t.`service_scene_checked`,\n"
            + "  t.`archive_type`,\n"
            + "  t.`priority`,\n"
            + "  t.`creator`,\n"
            + "  t.`create_time`,\n"
            + "  t.`current_operator`,\n"
            + "  t.`last_operator`,\n"
            + "  t.`last_staff`,\n"
            + "  t.`last_operate_time`,\n"
            + "  t.`question`,\n"
            + "  t.`last_inner_reply`,\n"
            + "  t.`status`,\n"
            + "  t.`operation_ticket_id`,\n"
            + "  t.`responsible`,\n"
            + "  t.`first_line_responsible`,\n"
            + "  t.`second_line_responsible`,\n"
            + "  t.`solve_status`,\n"
            + "  t.`service_rate`,\n"
            + "  t.`satisfaction`,\n"
            + "  t.`unsatisfy_reason`,\n"
            + "  t.`first_should_assign`,\n"
            + "  t.`first_fact_assign`,\n"
            + "  t.`first_fact_assign_time`,\n"
            + "  t.`should_assign`,\n"
            + "  t.`fact_assign`,\n"
            + "  t.`fact_assign_time`,\n"
            + "  t.`unsatisfy_ticket_id`,\n"
            + "  t.`related_unsatisfied_ticket_id`,\n"
            + "  t.`related_qcloud_ticket_id`,\n"
            + "  t.`qcloud_ticket_id`,\n"
            + "  t.`qcloud_category_id`,\n"
            + "  t.`qcloud_receive_notice_flag`,\n"
            + "  t.`instance_id`,\n"
            + "  t.`woodpecker_jobid`,\n"
            + "  t.`appraise`,\n"
            + "  t.`appraise_time`,\n"
            + "  t.`next_follow_time`,\n"
            + "  t.`qcloud_complaint_id`,\n"
            + "  t.`complainted_ticket_id`,\n"
            + "  t.`language`,\n"
            + "  t.`callcenter_answer_time`,\n"
            + "  t.`callcenter_hand_up_time`,\n"
            + "  t.`callcenter_talk_duration`,\n"
            + "  t.`callcenter_queue_duration`,\n"
            + "  t.`callcenter_session_id`,\n"
            + "  t.`callcenter_agent_id`,\n"
            + "  t.`callcenter_agent_name`,\n"
            + "  t.`callcenter_session_filename`,\n"
            + "  t.`qqgroup_start_time`,\n"
            + "  t.`qqgroup_end_time`,\n"
            + "  t.`qqgroup_response_time`,\n"
            + "  t.`qqgroup_response_duration`,\n"
            + "  t.`qqgroup_number`,\n"
            + "  t.`qqgroup_responsable`,\n"
            + "  t.`qqgroup_big_customer_operator`,\n"
            + "  t.`secret_content`,\n"
            + "  t.`update_time`,\n"
            + "  t.`qcloud_last_customer_reply_time`,\n"
            + "  t.`vip_ask_callback_id`,\n"
            + "  t.`callback_time_start`,\n"
            + "  t.`callback_time_end`,\n"
            + "  t.`owner_uin`,\n"
            + "  t.`is_deleted`,\n"
            + "  t.`is_ever_to_wan`,\n"
            + "  t.`company_id`,\n"
            + "  t.`is_claim`,\n"
            + "  t.`is_fault_report`,\n"
            + "  t.`is_internal_go_up`,\n"
            + "  t.`post`,\n"
            + "  t.`next_up_responsor`,\n"
            + "  t.`next_up_time`,\n"
            + "  t.`cc_person`,\n"
            + "  t.`operation_service_scene`,\n"
            + "  t.`responsible_should_assign`,\n"
            + "  t.`chat_group_id`,\n"
            + "  t.`first_assign_company_id`,\n"
            + "  t.`feedback_channel`,\n"
            + "  t.`group_id`,\n"
            + "  t.`source_channel`,\n"
            + "  t.`customer_contact_info`,\n"
            + "  t.`question_category`,\n"
            + "  t.`reason_category`,\n"
            + "  t.`question_start_time`,\n"
            + "  t.`question_end_time`,\n"
            + "  t.`title`,\n"
            + "  t.`reason`,\n"
            + "  t.`upgrade_time`,\n"
            + "  t.`sales_supportor`,\n"
            + "  t.`trade`,\n"
            + "  t.`name`,\n"
            + "  t.`event_tss_id`,\n"
            + "  t.`add_info_application_post`,\n"
            + "  t.`add_info_application_should_assign`,\n"
            + "  t.`add_info_application_operator`,\n"
            + "  t.`tapd_story_id`,\n"
            + "  t.`callcenter_skill_group`,\n"
            + "  t.`keyissues_id`,\n"
            + "  t.`complaint`,\n"
            + "  t.`complaint_content`,\n"
            + "  t.`ltc_name`,\n"
            + "  t.`product_version`,\n"
            + "  t.`severity`,\n"
            + "  t.`short`,\n"
            + "  t.`extern_status`,\n"
            + "  t.`caller`,\n"
            + "  t.`affected_customers`,\n"
            + "  t.`customer_uid`,\n"
            + "  t.`incident_manager`,\n"
            + "  t.`incident_manager_should_assign`,\n"
            + "  t.`risk_control_first_time`,\n"
            + "  t.`risk_control_num`,\n"
            + "  t.`is_incident_manager_into`,\n"
            + "  t.`url`,\n"
            + "  t.`short_url`,\n"
            + "  t.operation_data,\n"
            + "  '' AS operators,\n"
            + "  '' AS close_time,\n"
            + "  0 AS close_type,\n"
            + "  0 AS reply_times,\n"
            + "  0 AS is_reported,\n"
            + "  '' AS last_reporter,\n"
            + "  0 AS transfer_times,\n"
            + "  '' AS close_operator,\n"
            + "  0 AS interaction_times,\n"
            + "  0 AS customer_reply_times,\n"
            + "  0 AS first_line_reply_times,\n"
            + "  '' AS first_response_stuff,\n"
            + "  0 AS second_line_reply_times,\n"
            + "  0 AS agent_reply_times,\n"
            + "  '' AS last_reported_time,\n"
            + "  '' AS ticket_solved_time,\n"
            + "  '' AS inner_creator,\n"
            + "  0 AS is_inner_created,\n"
            + "  0 AS is_complaint,\n"
            + "  '' AS is_satisfy,\n"
            + "  '' AS request_source,\n"
            + "  '' AS operation_deny_reason,\n"
            + "  '' AS second_line_deny_reason,\n"
            + "  '' AS backend_first_deny_operator,\n"
            + "  '' AS product_research_deny_reason,\n"
            + "  '' AS vertical_product_research_deny_reason,\n"
            + "  0 AS first_response_queue,\n"
            + "  0 AS agent_duration,\n"
            + "  0 AS unknown_duration,\n"
            + "  0 AS customer_duration,\n"
            + "  0 AS operation_duration,\n"
            + "  0 AS first_line_duration,\n"
            + "  0 AS second_line_duration,\n"
            + "  0 AS ticket_deal_duration,\n"
            + "  0 AS agent_customer_duration,\n"
            + "  0 AS product_research_duration,\n"
            + "  0 AS unknown_customer_duration,\n"
            + "  0 AS first_line_customer_duration,\n"
            + "  0 AS second_line_customer_duration,\n"
            + "  0 AS vertical_product_research_duration,\n"
            + "  0 AS agent_engineer_duration,\n"
            + "  0 AS ticket_staff_deal_duration,\n"
            + "  0 AS unknown_engineer_duration,\n"
            + "  0 AS first_line_engineer_duration,\n"
            + "  0 AS second_line_engineer_duration,\n"
            + "  0 AS is_transfer_first_line,\n"
            + "  0 AS is_denied_by_operation,\n"
            + "  0 AS is_denied_by_second_line,\n"
            + "  '' AS transfer_first_line_time,\n"
            + "  0 AS transfer_first_line_times,\n"
            + "  0 AS transfer_operation_times,\n"
            + "  -1 AS transfer_first_line_queue,\n"
            + "  '' AS transfer_first_line_staff,\n"
            + "  0 AS transfer_second_line_times,\n"
            + "  '' AS transfer_type,\n"
            + "  0 AS is_denied_by_production_research,\n"
            + "  0 AS transfer_production_research_times,\n"
            + "  -1 AS first_transfer_operation_staff_post,\n"
            + "  '' AS first_transfer_operation_staff,\n"
            + "  CAST(create_time AS STRING) AS ticket_create_time,\n"
            + "  0 AS transfer_vertical_production_research_times,\n"
            + "  -1 AS first_transfer_production_research_staff_post,\n"
            + "  -1 AS first_transfer_vert_production_research_staff_post,\n"
            + "  '' AS first_transfer_second_line_staff,\n"
            + "  '' AS first_second_line_operator,\n"
            + "  '' AS first_transfer_operation_time,\n"
            + "  '' AS first_second_line_service_time,\n"
            + "  -1 AS first_second_line_assign,\n"
            + "  0 AS second_line_solved_duration,\n"
            + "  0 AS first_second_line_response_duration,\n"
            + "  '' AS current_second_line_operator,\n"
            + "  '' AS first_second_line_fact_operator,\n"
            + "  -1 AS first_second_line_fact_assign,\n"
            + "  '' AS `measures`,\n"
            + "  '' AS first_transfer_complaint_queue_time,\n"
            + "  0 AS customer_waiting_impatience_duration,\n"
            + "  -1 AS last_operation_fact_assign,\n"
            + "  -1 AS last_production_research_fact_assign,\n"
            + "  '' AS second_line_operators,\n"
            + "  -1 AS current_second_line_fact_assign,\n"
            + "  '' AS first_transfer_production_research_time,\n"
            + "  -1 AS first_second_line_operator_company_id,\n"
            + "  -1 AS current_second_line_operator_company_id,\n"
            + "  '' AS second_line_operator_company_ids,\n"
            + "  0 AS is_transfer_company_in_second_line,\n"
            + "  0 AS is_transfer_inner_staff_in_second_line,\n"
            + "  '' AS second_line_last_refuser,\n"
            + "  '' AS last_customer_operator,\n"
            + "  '' AS second_line_last_company_operator,\n"
            + "  0 AS second_line_refuse_times,\n"
            + "  0 AS to_be_confirm_close_times,\n"
            + "  0 AS average_reply_duration,\n"
            + "  0 AS longest_reply_duration,\n"
            + "  '' AS longest_reply_staff,\n"
            + "  0 AS stuff_counts,\n"
            + "  0 AS customer_urge_times,\n"
            + "  0 AS connect_customer_times,\n"
            + "  0 AS is_transfer_hotline_first_line,\n"
            + "  -1 AS hotline_first_line_queue,\n"
            + "  '' AS last_to_be_added_time,\n"
            + "  '' AS current_first_line_operator,\n"
            + "  '' AS first_response_time,\n"
            + "  0 AS first_response_duration,\n"
            + "  0 AS hotline_first_transfer_deal_duration,\n"
            + "  '' AS hotline_first_transfer_line_time,\n"
            + "  '' AS hotline_first_transfer_response_time,\n"
            + "  '' AS operation_handler,\n"
            + "  -1 AS operation_handler_company_id,\n"
            + "  0 AS hotline_first_transfer_response_duration,\n"
            + "  -1 AS second_line_last_company_operator_company_id,\n"
            + "  '' AS first_line_last_operator,\n"
            + "  -1 AS first_line_last_operator_company_id,\n"
            + "  -1 AS first_line_last_operator_fact_assign,\n"
            + "  '' AS production_research_handler,\n"
            + "  '' AS first_transfer_second_line_time,\n"
            + "  0 AS is_transfer_tgw,\n"
            + "  0 AS teg_queue_duration,\n"
            + "  0 AS is_phone_callback,\n"
            + "  0 AS out_times,\n"
            + "  '' AS last_ofc_operator,\n"
            + "  -1 AS last_ofc_operator_post,\n"
            + "  COALESCE(t1.over_3h_no_reply_count,0) as over_3h_no_reply_count,\n"
            + "  COALESCE(t1.reply_over_2h_times,0) as reply_over_2h_times\n"
            + "FROM (\n"
            + "  SELECT\n"
            + "    *, \n"
            + "    ROW_NUMBER() OVER (PARTITION BY ticket_id ORDER BY update_time DESC) AS rn\n"
            + "  FROM iceberg_sink_dwd_incident_ticket_operation_detail\n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='3s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + ") as t\n"
            + " Left Join (\n"
            + "  SELECT\n"
            + "    ticket_id,\n"
            + "    count(distinct case when urge_times>10800 then id else null end) AS over_3h_no_reply_count,\n"
            + "    count(distinct case when urge_times>7200 then id else null end) AS reply_over_2h_times\n"
            + "  FROM iceberg_source_dwd_incident_ticket_alarm_record\n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='3s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + " WHERE warning_type=21 and urge_times>7200\n"
            + " GROUP BY ticket_id\n"
            + ") as t1\n"
            + "  ON t.ticket_id = t1.ticket_id\n"
            + "WHERE t.rn = 1";

    public static final String QUERY_MYSQL_STAFF_INFO_SQL = ""
            + "WITH t1 AS (\n"
            + "  SELECT\n"
            + "    t1.user_id AS uid,\n"
            + "    t1.user_id AS user_id,\n"
            + "    t1.user_name AS user_name,\n"
            + "    t1.company_id AS company_id,\n"
            + "    t2.group_id AS group_id,\n"
            + "    t2.company_id AS assign_company_id\n"
            + "  FROM mysql_source_t_users t1\n"
            + "  LEFT JOIN mysql_source_t903_company_user t2 ON t1.id = t2.user_id\n"
            + "  WHERE t1.company_id > 0\n"
            + "\n"
            + "  UNION ALL\n"
            + "\n"
            + "  SELECT\n"
            + "    t1.user_id AS uid,\n"
            + "    t1.user_id AS user_id,\n"
            + "    t1.user_name AS user_name,\n"
            + "    t1.company_id AS company_id,\n"
            + "    t2.group_id AS group_id,\n"
            + "    0 AS assign_company_id\n"
            + "  FROM mysql_source_t_users t1\n"
            + "  LEFT JOIN mysql_source_t006_staff t2 ON t1.user_id = t2.user_id\n"
            + "  WHERE t1.company_id = 0\n"
            + "  \n"
            + "  UNION ALL\n"
            + "\n"
            + "  SELECT\n"
            + "    CAST(t1.id AS STRING) AS uid,\n"
            + "    t1.user_id AS user_id,\n"
            + "    t1.user_name AS user_name,\n"
            + "    t1.company_id AS company_id,\n"
            + "    t2.group_id AS group_id,\n"
            + "    t2.company_id AS assign_company_id\n"
            + "  FROM mysql_source_t_users t1\n"
            + "  LEFT JOIN mysql_source_t903_company_user t2 ON t1.id = t2.user_id\n"
            + "  WHERE t1.company_id > 0\n"
            + "\n"
            + "  UNION ALL\n"
            + "\n"
            + "  SELECT\n"
            + "    CAST(t1.id AS STRING) AS uid,\n"
            + "    t1.user_id AS user_id,\n"
            + "    t1.user_name AS user_name,\n"
            + "    t1.company_id AS company_id,\n"
            + "    t2.group_id AS group_id,\n"
            + "    0 AS assign_company_id\n"
            + "  FROM mysql_source_t_users t1\n"
            + "  LEFT JOIN mysql_source_t006_staff t2 ON t1.user_id = t2.user_id\n"
            + "  WHERE t1.company_id = 0\n"
            + ")\n"
            + "SELECT\n"
            + "  *\n"
            + "FROM t1";


    public static final String QUERY_MYSQL_STAFF_POST_SQL = ""
            + "SELECT *\n"
            + "FROM (\n"
            + "  SELECT \n"
            + "    CONCAT(user_id, '-', CAST(service_channel AS STRING)) AS pk,\n"
            + "    CAST(post AS INT) AS operator_post,\n"
            + "    *,\n"
            + "    ROW_NUMBER() OVER ("
            + "      PARTITION BY CONCAT(user_id, '-', CAST(service_channel AS STRING)) ORDER BY id) AS rn\n"
            + "  FROM mysql_source_t009_staff_post\n"
            + ") t\n"
            + "WHERE rn = 1";
}