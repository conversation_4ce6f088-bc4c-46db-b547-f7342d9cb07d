package com.tencent.andata.etl.sql;

public class DwmIncidentTicketAssistantSql {
    public static final String QUERY_DWD_INCIDENT_TICKET_SQL = "SELECT\n"
            + "  *,\n"
            + "  t2.*,\n"
            + "  t3.*,\n"
            + "  '' AS extra_data,\n"
            + "  '' AS close_time\n"
            + "FROM (SELECT * FROM iceberg_source_dwd_incident_ticket_base_info \n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ AS t1\n"
            + "LEFT JOIN pg_source_dim_company_info FOR SYSTEM_TIME AS OF t1.`process_time` AS t2 "
            + "ON t1.company_id = t2.company_id \n"
            + "LEFT JOIN pg_source_dim_t_incident_ticket_scenes FOR SYSTEM_TIME AS OF t1.`process_time` AS t3 ON \n"
            + "(IF(t1.service_scene_checked > 0, service_scene_checked, t1.service_scene) = t3.dim_id)\n";
}
