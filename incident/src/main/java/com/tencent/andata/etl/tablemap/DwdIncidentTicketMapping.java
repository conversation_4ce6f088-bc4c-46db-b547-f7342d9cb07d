package com.tencent.andata.etl.tablemap;

public class DwdIncidentTicketMapping {

    public static String mysqlTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"t201_ticket\",\n"
            + "        \"fTable\":\"mysql_source_t201_ticket\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t201_ticket_fsm\",\n"
            + "        \"fTable\":\"mysql_source_t201_ticket_fsm\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t201_ticket_extra\",\n"
            + "        \"fTable\":\"mysql_source_t201_ticket_extra\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t201_ticket_preview\",\n"
            + "        \"fTable\":\"mysql_source_t201_ticket_preview\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t201_ticket_tce_insales\",\n"
            + "        \"fTable\":\"mysql_source_t201_ticket_tce_insales\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t208_ticket_custom_field\",\n"
            + "        \"fTable\": \"mysql_source_t208_ticket_custom_field\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t204_ticket_operation_extra\",\n"
            + "        \"fTable\": \"mysql_source_t204_ticket_operation_extra\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t246_ticket_review\",\n"
            + "        \"fTable\":\"mysql_source_t246_ticket_review\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t202_ticket_operation\",\n"
            + "        \"fTable\": \"mysql_source_t202_ticket_operation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t235_ticket_calling\",\n"
            + "        \"fTable\": \"mysql_source_t235_ticket_calling\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t252_ticket_quality_operation\",\n"
            + "        \"fTable\": \"mysql_source_t252_ticket_quality_operation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t251_ticket_quality_category\",\n"
            + "        \"fTable\": \"mysql_source_t251_ticket_quality_category\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t217_ticket_scenes\",\n"
            + "        \"fTable\":\"mysql_source_t217_ticket_scenes\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t226_ticket_access_record\",\n"
            + "        \"fTable\": \"mysql_source_t226_ticket_access_record\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t1003_ticket_favorites\",\n"
            + "        \"fTable\": \"mysql_source_t1003_ticket_favorites\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t1004_ticket_favorites_marks\",\n"
            + "        \"fTable\": \"mysql_source_t1004_ticket_favorites_marks\"\n"
            + "    }\n,"
            + "    {\n"
            + "        \"rdbTable\": \"t210_ticket_alarm_record\", \n"
            + "        \"fTable\": \"mysql_source_t210_ticket_alarm_record\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t102_duty\",\n"
            + "        \"fTable\": \"mysql_source_t102_duty\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t107_company_duty_config\",\n"
            + "        \"fTable\": \"mysql_source_t107_company_duty_config\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t211_ticket_comment\",\n"
            + "        \"fTable\": \"mysql_source_t211_ticket_comment\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t933_ticket_public_opinion\",\n"
            + "        \"fTable\": \"mysql_source_t933_ticket_public_opinion\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket_review\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_review\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_t_incident_ticket_scenes\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_t_incident_ticket_scenes\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_incident_duty\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_incident_duty\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_incident_company_duty_config\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_incident_company_duty_config\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket_comment\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_comment\"\n"
            + "    }\n"
            + "]";

    public static String starRocksTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_incident_ticket_comment\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_incident_ticket_comment\",\n"
            + "        \"primaryKey\": \"id,ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_incident_ticket_public_opinion\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_incident_ticket_public_opinion\",\n"
            + "        \"primaryKey\": \"id,ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_incident_ticket\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_incident_ticket\",\n"
            + "        \"primaryKey\": \"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_incident_ticket_operation\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_incident_ticket_operation\",\n"
            + "        \"primaryKey\": \"operation_id,ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_incident_ticket_operation_extra\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_incident_ticket_operation_extra\",\n"
            + "        \"primaryKey\": \"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_incident_ticket_custom_field\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_incident_ticket_custom_field\",\n"
            + "        \"primaryKey\": \"id,ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dim_incident_duty\",\n"
            + "        \"fTable\":\"flink_starrocks_dim_incident_duty\",\n"
            + "        \"primaryKey\": \"duty_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"ods_s360_customer_dynamic\",\n"
            + "        \"fTable\":\"flink_starrocks_ods_s360_customer_dynamic\",\n"
            + "        \"primaryKey\": \"value_of_primary_key\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwd_incident_ticket_alarm_record\",\n"
            + "        \"fTable\":\"flink_starrocks_dwd_incident_ticket_alarm_record\",\n"
            + "        \"primaryKey\": \"id,ticket_id,create_time\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket\",\n"
            + "        \"primaryKey\":\"ticket_id,create_time\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"ods_s360_customer_dynamic\",\n"
            + "        \"fTable\":\"iceberg_source_ods_s360_customer_dynamic\",\n"
            + "        \"primaryKey\":\"value_of_primary_key\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_fsm\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket_fsm\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_preview\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket_preview\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_extra\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket_extra\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_tce_insales\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket_tce_insales\",\n"
            + "        \"primaryKey\":\"ticket_id,create_time\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_review\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket_review\",\n"
            + "        \"primaryKey\":\"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_base_info\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket_base_info\",\n"
            + "        \"primaryKey\":\"ticket_id,create_time\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_custom_field\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_custom_field\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_operation\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_operation\",\n"
            + "        \"primaryKey\": \"operation_id,operate_time\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_operation_extra\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_operation_extra\",\n"
            + "        \"primaryKey\": \"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_calling\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_calling\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_quality_operation\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_quality_operation\",\n"
            + "        \"primaryKey\": \"quality_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t251_ticket_quality_category\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t251_ticket_quality_category\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_ticket_quality_category\",\n"
            + "        \"fTable\": \"iceberg_sink_dim_incident_ticket_quality_category\",\n"
            + "        \"primaryKey\": \"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dim_incident_ticket_scenes\",\n"
            + "        \"fTable\":\"iceberg_sink_dim_incident_ticket_scenes\",\n"
            + "        \"primaryKey\":\"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_access_record\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_access_record\",\n"
            + "        \"primaryKey\": \"id,create_time\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_favorites\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_favorites\",\n"
            + "        \"primaryKey\": \"mark_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_favorites_marks\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_favorites_marks\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_alarm_record\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_alarm_record\",\n"
            + "        \"primaryKey\": \"id,create_time\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_duty\",\n"
            + "        \"fTable\": \"iceberg_sink_dim_incident_duty\",\n"
            + "        \"primaryKey\": \"duty_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_company_duty_config\",\n"
            + "        \"fTable\": \"iceberg_sink_dim_incident_company_duty_config\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_comment\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_comment\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    }\n"
            + "]";
}
