package com.tencent.andata.etl.transform;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Arrays;

import static com.tencent.andata.etl.transform.DwmIncidentTicketStatisticTransform.getTicketOperationList;
import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

public class DwmIncidentTicketOperatorDurationInfoTransform {

    private static final ObjectMapper mapper = new ObjectMapper();
    private static final Logger LOG = LoggerFactory.getLogger(DwmIncidentTicketOperatorDurationInfoTransform.class);

    private static long getUnixTime(String stTime) {
        String pattern = "yyyy-MM-dd HH:mm:ss";
        return DateTimeFormat
                .forPattern(pattern)
                .parseDateTime(stTime)
                .getMillis() / 1000;
    }

    public static class TicketOperatorDurationInfoMapFunction implements FlatMapFunction<Row, Row> {

        @Override
        public void flatMap(Row row, Collector<Row> out) throws Exception {
            HashMap<String, TicketHandlerInfo> infoMap = new HashMap<>();
            String ticketSolvedTime = row.getFieldAs("ticket_solved_time"); // 工单解决时间
            long ticketId = Long.parseLong(row.getFieldAs("ticket_id").toString()); // 工单Id

            if (StringUtils.isEmpty(ticketSolvedTime) || ticketSolvedTime.equals("1970-01-01 00:00:00")) {
                ticketSolvedTime = "9999-12-31 23:59:59";
            }

            List<JsonNode> ticketOperations = getTicketOperationList(row);
            // 反转操作流水，方便计算
            Collections.reverse(ticketOperations);
            ListIterator<JsonNode> it = ticketOperations.listIterator();

            // 辅助字段，标识流水计算进度
            long progressId = 0L;
            while (it.hasNext()) {
                int idx = it.nextIndex();
                JsonNode operation = it.next();

                int intervalDuration = 0;
                int post = operation.get("post").asInt();
                long operationId = operation.get("operation_id").asLong();
                String operateTime = operation.get("operate_time").asText();
                int duration = operation.get("duration").asInt(0);
                int targetPost = operation.get("target_post").asInt(0);
                final int operatorType = operation.get("operator_type").asInt(0);
                int operationType = operation.get("operation_type").asInt(0);

                if (operateTime.compareTo(ticketSolvedTime) > 0) {
                    continue;
                }

                if (operationType == 11 && post != targetPost) {
                    continue;
                }

                // 因ticketOperations在上方被反转过，
                // 如果外层循环的operationId > 内层循环的operationId，说明这部分数据已被计算过，需要跳过。
                if (progressId > 0 && (operationId > progressId)) {
                    continue;
                }

                if (operatorType < 3) {
                    intervalDuration = duration;
                    List<JsonNode> subOperations = ticketOperations.subList(idx + 1, ticketOperations.size());
                    for (JsonNode subOperation : subOperations) {
                        progressId = subOperation.get("operation_id").asLong();
                        final int subPost = subOperation.get("post").asInt(0);
                        String subOperateTime = subOperation.get("operate_time").asText();
                        int subDuration = subOperation.get("duration").asInt(0);
                        String currOperator = subOperation.get("next_operator_name").asText();
                        final int subTargetPost = subOperation.get("target_post").asInt(0);
                        final int subOperationType = subOperation.get("operation_type").asInt(0);
                        String valueOfPrimaryKey = ticketId + "|" + currOperator.replaceAll("（" + ".*?）", "").trim();
                        if (StringUtils.isNotEmpty(currOperator) && !currOperator.equalsIgnoreCase("SYSTEM")) {
                            if (infoMap.get(currOperator) == null) {
                                // 设置默认值
                                infoMap.put(currOperator,
                                        new TicketHandlerInfo.Builder()
                                                .duration(0L)
                                                .post(post)
                                                .ticketId(ticketId)
                                                .operator(currOperator)
                                                .inOutTimeList(new ArrayList<>())
                                                .intervalDurationList(new ArrayList<>())
                                                .valueOfPrimaryKey(valueOfPrimaryKey)
                                                .build());
                            }

                            long outTime = getUnixTime(operateTime);
                            long inTime = getUnixTime(subOperateTime);
                            infoMap.get(currOperator).duration += duration;
                            infoMap.get(currOperator).intervalDurationList.add(intervalDuration);
                            infoMap.get(currOperator).inOutTimeList.add(Arrays.asList(inTime, outTime));
                            break;
                        }
                        duration += subDuration;
                        intervalDuration += subDuration;
                        if (subTargetPost != subPost && subOperationType == 11) {
                            duration -= subDuration;
                            intervalDuration -= subDuration;
                        }
                    }
                }
                infoMap.values().forEach(consumer((value) -> {
                    Row rstRow = new Row(row.getKind(), 14);
                    rstRow.setField(0, value.valueOfPrimaryKey);
                    rstRow.setField(1, ticketId);
                    rstRow.setField(2, value.operator);
                    rstRow.setField(3, value.post);
                    rstRow.setField(4, value.duration);
                    rstRow.setField(5, row.getField("service_channel"));
                    rstRow.setField(6, row.getField("service_scene"));
                    rstRow.setField(7, row.getField("service_scene_checked"));
                    rstRow.setField(8, row.getField("priority"));
                    rstRow.setField(9, row.getField("ticket_create_time"));
                    rstRow.setField(10, row.getField("close_time"));
                    rstRow.setField(11, row.getField("ticket_solved_time"));
                    rstRow.setField(12, mapper.writeValueAsString(value.inOutTimeList));
                    rstRow.setField(13, mapper.writeValueAsString(value.intervalDurationList));
                    out.collect(rstRow);
                }));
            }
            ticketOperations = null;
        }
    }

    private static class TicketHandlerInfo {

        int post;
        long duration;
        long ticketId;
        String operator;
        int operationType;
        String valueOfPrimaryKey;
        List<List<Long>> inOutTimeList;
        List<Integer> intervalDurationList;

        private TicketHandlerInfo() {
        }

        private TicketHandlerInfo(TicketHandlerInfo origin) {
            this.post = origin.post;
            this.duration = origin.duration;
            this.ticketId = origin.ticketId;
            this.operator = origin.operator;
            this.inOutTimeList = origin.inOutTimeList;
            this.operationType = origin.operationType;
            this.valueOfPrimaryKey = origin.valueOfPrimaryKey;
            this.intervalDurationList = origin.intervalDurationList;

        }

        @Override
        public String toString() {
            return "{"
                    // + "post='" + post + '\''
                    + "duration='" + duration + '\''
                    // + ", ticketId=" + ticketId
                    // + ", operator='" + operator + '\''
                    // + ", inOutTimeList='" + inOutTimeList.toString() + '\''
                    // + ", operationType='" + operationType + '\''
                    // + ", valueOfPrimaryKey='" + valueOfPrimaryKey + '\''
                    // + ", intervalDurationList='" + intervalDurationList.toString() + '\''
                    + '}';
        }

        private static class Builder {

            private final TicketHandlerInfo target;

            public Builder() {
                this.target = new TicketHandlerInfo();
            }

            private Builder post(Integer post) {
                target.post = post;
                return this;
            }

            private Builder duration(Long duration) {
                target.duration = duration;
                return this;
            }

            private Builder ticketId(Long ticketId) {
                target.ticketId = ticketId;
                return this;
            }

            private Builder operator(String operator) {
                target.operator = operator;
                return this;
            }

            private Builder valueOfPrimaryKey(String valueOfPrimaryKey) {
                target.valueOfPrimaryKey = valueOfPrimaryKey;
                return this;
            }

            private Builder inOutTimeList(List<List<Long>> inOutTimeList) {
                target.inOutTimeList = inOutTimeList;
                return this;
            }

            private Builder intervalDurationList(List<Integer> intervalDurationList) {
                target.intervalDurationList = intervalDurationList;
                return this;
            }

            private TicketHandlerInfo build() {
                return new TicketHandlerInfo(target);
            }
        }
    }
}
