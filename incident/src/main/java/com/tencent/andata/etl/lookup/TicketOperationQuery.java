package com.tencent.andata.etl.lookup;
import com.tencent.andata.utils.lookup.jdbc.AbstractJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.andata.utils.DbResultUtils;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import org.apache.calcite.tools.ValidationException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
public class TicketOperationQuery extends AbstractJDBCLookupQuery<Long, JsonNode> {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final DbResultUtils dbResultUtils = DbResultUtils.getInstance(); // 获取单例实例
    public TicketOperationQuery(DatabaseEnum databaseEnum, DatabaseConf databaseConf) throws ValidationException {
        super(databaseEnum, databaseConf);
    }

    @Override
    protected JsonNode executeQuery(Connection connection, Long ticketId) throws Exception {
        String sql = "SELECT\n"
                + "    *,\n"
                + "    'ticket_operation' AS data_type\n"
                + "FROM t202_ticket_operation\n"
                + "WHERE ticket_id = ? ORDER BY operation_id";

        final PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setLong(1, ticketId);
        final ResultSet resultSet = preparedStatement.executeQuery();

        // 收集所有结果行
        ArrayNode resultArray = objectMapper.createArrayNode();
        while (resultSet.next()) {
            JsonNode rowNode = dbResultUtils.convertResultSetRowToJsonNode(resultSet);
            resultArray.add(rowNode);
        }

        // 返回结果数组，如果没有记录则返回空数组
        return resultArray;
    }
}