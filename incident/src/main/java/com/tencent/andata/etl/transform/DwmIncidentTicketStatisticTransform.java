package com.tencent.andata.etl.transform;

import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;
import java.util.HashSet;
import java.util.Comparator;
import java.util.ListIterator;
import java.util.Collections;
import java.util.Optional;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.IdentityHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.tencent.andata.utils.DateFormatUtils.timeDelta;

public class DwmIncidentTicketStatisticTransform {

    private static final Logger LOG = LoggerFactory.getLogger(DwmIncidentTicketStatisticTransform.class);

    // 需要排除的操作类型
    // 需要排除的操作类型
    private static final List<Integer> operationTypeExcludeList = new ArrayList<>(
            Arrays.asList(7, 8, 10, 19, 21, 23, 27, 29, 30, 31, 32, 39, 44, 45, 46, 48, 50,
                    52, 53, 56, 57, 58, 59, 60));

    private static final List<Integer> companyIds = new ArrayList<>(
            Arrays.asList(1, 9, 23, 5, 2, 38, 169, 322, 4, 163, 321, 335, 336));

    // 后端岗位
    private static final List<Integer> backendPostList = new ArrayList<>(Arrays.asList(4, 5, 11));

    // 需要排除的服务通道
    private static final List<String> serviceChannelExcludeList = new ArrayList<>(Arrays.asList("30", "45"));

    // 定义一线客服操作类型的集合
    private static final Set<Integer> firstLineOperationTypes = new HashSet<>(Arrays.asList(
            2, 5, 8, 10, 11, 12, 15, 28, 35, 37, 48 // 根据需求单中提供的操作类型ID
    ));

    // 定义1.5线客服操作类型的集合
    private static final Set<Integer> secondLineOperationTypes = new HashSet<>(Arrays.asList(
            2, 3, 5, 8, 11, 12, 13, 14, 15, 16, 28, 30, 33, 35, 40, 41, 43, 48, 50
    ));

    private static final ObjectMapper mapper = new ObjectMapper();

    // jsonPath conf
    private static final Configuration conf = Configuration.defaultConfiguration()
            .addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL);

    public static <T> String replaceNullValues(T value, T defaultValue) {
        if (value != null) {
            return value.toString();
        }

        if (defaultValue != null) {
            return defaultValue.toString();
        }

        return "";
    }

    public static boolean isValidJson(String json) {
        try {
            mapper.readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取工单流水
     * @param row 工单信息
     * @return 工单流水
     * @throws Exception 异常
     */
    public static List<JsonNode> getTicketOperationList(Row row) throws Exception {
        String operationData = row.getFieldAs("operation_data");

        if (operationData == null) {
            LOG.error(String.format("operation_data is null, row: %s", row));
        }
        List<JsonNode> jsonNodes = new ArrayList<>();
        try {
            jsonNodes = mapper.readValue(
                    operationData,
                    mapper.getTypeFactory().constructCollectionType(List.class, JsonNode.class)
            );
            jsonNodes.sort(Comparator.comparingLong(node -> node.get("operation_id").asLong()));
        } catch (Exception e) {
            LOG.error(String.format("operation_data is not valid json, row: %s", row));
        }

        return jsonNodes;
    }

    public static boolean contentIsNotEmpty(String content) {
        if (content.equalsIgnoreCase("NULL")) {
            return false;
        }

        return StringUtils.isNotEmpty(content);
    }

    /**
     * 工单交互相关统计
     */

    public static class TicketCloseAndInteractionMapFunction implements MapFunction<Row, Row> {

        @Override
        public Row map(Row row) throws Exception {
            List<JsonNode> ticketOperations = getTicketOperationList(row);

            String isSatisfy = "未评价"; // 是否满意
            // 工单评价
            final long serviceRate = Long.parseLong(replaceNullValues(row.getFieldAs("service_rate"), 0L));
            // 不满意原因
            final long unsatisfyReason = Long.parseLong(replaceNullValues(row.getFieldAs("unsatisfy_reason"), 0L));

            // 是否满意
            if (serviceRate == 5 || unsatisfyReason == 7) {
                isSatisfy = "满意";
            } else if (serviceRate == 0 || unsatisfyReason == 0) {
                isSatisfy = "未评价";
            } else if (serviceRate == 1 || serviceRate == 2 || serviceRate == 3 || serviceRate == 4) {
                isSatisfy = "不满意";
            } else if (unsatisfyReason == 1 || unsatisfyReason == 2 || unsatisfyReason == 3 || unsatisfyReason == 4
                    || unsatisfyReason == 5 || unsatisfyReason == 6) {
                isSatisfy = "不满意";
            }

            int closeType = 0; // 结单类型
            int isReported = 0; // 是否上报
            int replyTimes = 0; // 回复次数
            int transferTimes = 0; // 转单次数
            int isComplaint = 0; // 是否是投诉单
            int stuffCounts = 0; // 工单经手人数
            int interactionTimes = 0; // 互动次数
            String closeOperator = ""; // 结单人
            String requestSource = ""; // 建单来源
            String lastReporter = ""; // 最后上报人
            String innerCreator = ""; // 创建工单的人
            int isInnerCreated = -1; // 是否是内部间单
            int customerReplyTimes = 0; // 客户回复次数
            int agentReplyTimes = 0; // 供应商回复次数
            int isPhoneCallback = 0; // 是否是电话回访
            String firstResponseStuff = ""; // 首次响应人
            int firstResponseDuration = 0; // 首次响应时长
            int firstLineReplyTimes = 0; // 一线对外回复次数
            int secondLineReplyTimes = 0; // 1.5线对外回复次数
            boolean createTicketFlag = true; // 是否是建单操作
            Set<String> operators = new HashSet<>(); // 工单经手人
            String closeTime = "1970-01-01 00:00:00"; // 结单时间
            boolean firstResponseStuffFlag = true; // 是否是第一次回复
            String lastReportedTime = "1970-01-01 00:00:00"; // 最后上报时间
            String firstResponseTime = "1970-01-01 00:00:00"; // 客服首次响应时间
            String time1 = "1970-01-01 00:00:00"; // 辅助变量，用于计算客户与客服的互动次数
            int outTimes = 0; //总外呼数：取流水表中 operation_type 为 '有效外呼' 和 '无效外呼'的次数

            ListIterator<JsonNode> it = ticketOperations.listIterator();
            String createTime = row.getFieldAs("ticket_create_time");

            // 辅助字段
            int closeTicketIndex = -1;
            int effectiveCallIndex = -1;
            int waitCustomerCloseIndex = -1;

            while (it.hasNext()) {
                JsonNode operation = it.next();
                final int post = operation.get("post").asInt();
                final String operator = operation.get("operator").asText();
                final String source = operation.get("request_source").asText();
                final int operatorType = operation.get("operator_type").asInt();
                final int targetStatus = operation.get("target_status").asInt();
                final int operationType = operation.get("operation_type").asInt();
                final String operateTime = operation.get("operate_time").asText();
                final String operatorPost = operation.get("operator_post").asText();
                final String operatorName = operation.get("operator_name").asText(); // 操作人姓名
                final String responsibleName = operation.get("responsible_name").asText(); // 当前客户代表姓名;
                final String nextOperatorName = operation.get("next_operator_name").asText(); // 下一操作人姓名
                final String currentOperatorName = operation.get("current_operator_name").asText(); // 当前操作人姓名
                final String nextResponsibleName = operation.get("next_responsible_name").asText(); // 下一客户代表姓名

                // 工单经手人计算逻辑
                operators.add(responsibleName);
                operators.add(nextOperatorName);
                operators.add(currentOperatorName);
                operators.add(nextResponsibleName);

                //总外呼数：取流水表中 operation_type 为 '有效外呼' 和 '无效外呼'的次数
                if (operationType == 44 || operationType == 45) {
                    outTimes += 1;
                }
                // 回复次数计算逻辑
                if (contentIsNotEmpty(operation.get("extern_reply").asText())) {
                    if (operatorType == 2 && operationType != 1) {
                        // 总对外回复次数
                        // 取流水表中“operator_type”为“客服”，“extern_reply”不为空，且operation_type不为建单的流水单数量之和
                        replyTimes += 1;

                        // 一线对外回复次数
                        // operator_type为“客服”， extern_reply”不为空，且operation_type不为“建单”
                        // ①“operator_post”字段为空，"post"字段为一线/运维/产研的记录数
                        if (StringUtils.isEmpty(operatorPost)) {
                            if (post == 2 || post == 4 || post == 5) {
                                firstLineReplyTimes += 1;
                            } else if (post == 1) {
                                agentReplyTimes += 1;
                            } else if (post == 3) {
                                secondLineReplyTimes += 1;
                            }
                        }

                        if (Integer.parseInt(operatorPost) == 1) {
                            agentReplyTimes += 1;
                        }

                        // 一线对外回复次数
                        // operator_type为“客服”， extern_reply”不为空，且operation_type不为“建单”
                        // ②operator_post为一线的记录数
                        if (Integer.parseInt(operatorPost) == 2) {
                            firstLineReplyTimes += 1;
                        }

                        if (Integer.parseInt(operatorPost) == 3) {
                            secondLineReplyTimes += 1;
                        }

                        // 首次响应人
                        // 流水表中“operator_type”为“客服”，①“extern_reply”不为空的记录流水按操作时间排序
                        // 取第一条operation_type不为“建单”的流水记录并返回“operator”字段
                        if (firstResponseStuffFlag && !operatorName.equalsIgnoreCase("SYSTEM")) {
                            firstResponseTime = operateTime;
                            firstResponseStuff = operatorName;
                            firstResponseStuffFlag = false;
                        }
                    }

                    // 用户回复次数
                    // operation_type为customer_reply，且“extern_reply”内容不为空。
                    if (operationType == 9) {
                        customerReplyTimes += 1;
                    }

                } else if (operationType == 44 && operatorType == 2 && firstResponseStuffFlag
                        && !operatorName.equalsIgnoreCase("SYSTEM")) {
                    // 首次响应人
                    // 流水表中“operator_type”为“客服”，
                    // ②或者首次操作类型为“有效外呼”的流水按操作时间排序返回“operator”字段
                    firstResponseTime = operateTime;
                    firstResponseStuff = operatorName;
                    firstResponseStuffFlag = false;
                }

                if (operationType == 15) {
                    waitCustomerCloseIndex = it.nextIndex();
                } else if (operationType == 16) {
                    closeTicketIndex = it.nextIndex();
                } else if (operationType == 44) {
                    effectiveCallIndex = it.nextIndex();
                }

                // close_time&close_type计算逻辑
                if (operationType == 16 || operationType == 1 && targetStatus == 3) {
                    closeTime = operateTime;
                    closeType = operatorType;
                    closeOperator = operator;
                }

                // transfer_times计算逻辑
                if (operationType == 5) {
                    transferTimes += 1;
                }

                // 故障上报相关计算逻辑
                if (operationType == 23) {
                    isReported = 1;
                    lastReporter = operator;
                    lastReportedTime = operateTime;
                }

                // 是否是投诉单
                if (operationType == 31) {
                    isComplaint = 1;
                }

                // 内部建单人
                if (operationType == 1 && createTicketFlag && !operatorName.equalsIgnoreCase("SYSTEM")) {
                    requestSource = source;
                    if (operatorType == 2) {
                        isInnerCreated = 1;
                        innerCreator = operatorName;
                    } else if (operatorType == 1) {
                        isInnerCreated = 0;
                    }
                    createTicketFlag = false;
                }
                // interaction_times计算逻辑
                int idx = it.nextIndex();
                boolean found = false;
                if (operatorType == 1 && idx < ticketOperations.size() - 1 && time1.compareTo(operateTime) < 0) {
                    List<JsonNode> subList = ticketOperations.subList(idx + 1, ticketOperations.size());
                    for (JsonNode node : subList) {
                        if (!found && node.get("operator_type").asInt() == 2
                                && StringUtils.isNotEmpty(node.get("extern_reply").asText())) {
                            // 更新time1的值
                            time1 = node.get("operate_time").asText();
                            interactionTimes += 1;
                            found = true;
                        }
                    }
                }
            }

            // 去除空字符串
            operators = operators
                    .stream()
                    .distinct()
                    .filter(StringUtils::isNotBlank)
                    .filter(x -> !x.equalsIgnoreCase("SYSTEM"))
                    .map(String::trim)
                    .collect(Collectors.toSet());

            stuffCounts = operators.size();

            // 计算首次响应时长（秒）
            if (StringUtils.isNotEmpty(firstResponseTime)
                    && StringUtils.isNotEmpty(createTime)
                    && !firstResponseTime.equals("1970-01-01 00:00:00")) {
                firstResponseDuration = (int) timeDelta(createTime, firstResponseTime);
            }

            if (effectiveCallIndex > 0) {
                if ((effectiveCallIndex > waitCustomerCloseIndex && waitCustomerCloseIndex > 0)
                        || (effectiveCallIndex > closeTicketIndex && closeTicketIndex > 0)) {
                    isPhoneCallback = 1;
                }
            }

            ticketOperations = null;
            row.setField("close_time", closeTime);
            row.setField("close_type", closeType);
            row.setField("is_satisfy", isSatisfy);
            row.setField("is_reported", isReported);
            row.setField("reply_times", replyTimes);
            row.setField("is_complaint", isComplaint);
            row.setField("stuff_counts", stuffCounts);
            row.setField("inner_creator", innerCreator);
            row.setField("last_reporter", lastReporter);
            row.setField("request_source", requestSource);
            row.setField("transfer_times", transferTimes);
            row.setField("close_operator", closeOperator);
            row.setField("is_inner_created", isInnerCreated);
            row.setField("agent_reply_times", agentReplyTimes);
            row.setField("is_phone_callback", isPhoneCallback);
            row.setField("interaction_times", interactionTimes);
            row.setField("last_reported_time", lastReportedTime);
            row.setField("first_response_time", firstResponseTime);
            row.setField("customer_reply_times", customerReplyTimes);
            row.setField("first_response_stuff", firstResponseStuff);
            row.setField("first_line_reply_times", firstLineReplyTimes);
            row.setField("second_line_reply_times", secondLineReplyTimes);
            row.setField("operators", mapper.writeValueAsString(operators));
            row.setField("first_response_duration", firstResponseDuration);
            row.setField("out_times", outTimes);
            return row;
        }
    }

    /**
     * 工单已结单，解决时间相关统计
     */
    public static class TicketClosedSolvedTimeMapFunction implements MapFunction<Row, Row> {

        @Override
        public Row map(Row row) throws Exception {
            List<JsonNode> ticketOperations = getTicketOperationList(row);

            // 反转ticketOperations
            Collections.reverse(ticketOperations);

            int toBeConfirmCloseTimes = 0; // 待客户确认结单次数
            String ticketSolvedTime = "1970-01-01 00:00:00"; // 工单解决时间

            // 辅助字段
            boolean claimFlag = false;
            boolean recoveredFlag = false;
            boolean notRecoveredFlag = false;
            boolean systemCloseTicket = false;
            boolean customerCloseTicket = false;
            boolean waitCustomConfirmFlag = false;

            int ticketCloseType = -1;
            String applyCloseTime = "";
            String customerAddTime = "";
            String acceptCloseTime = "";
            String tobeRecoveredTime = "";
            String customerCloseTime = "";
            String mulReplyTroubleTicketTime = "";
            String customerServiceCloseTimeV1 = "";
            String customerServiceCloseTimeV2 = "";

            // 服务通道
            String serviceChannel = Optional
                    .ofNullable(row.getFieldAs("service_channel"))
                    .orElse("")
                    .toString();

            String firstCloseTime = row.getFieldAs("close_time");

            for (JsonNode operation : ticketOperations) {
                int targetStatus = operation.get("target_status").asInt();
                int operatorType = operation.get("operator_type").asInt();
                String innerReply = operation.get("inner_reply").asText();
                int operationType = operation.get("operation_type").asInt();
                String operateTime = operation.get("operate_time").asText();
                String operatorName = operation.get("operator_name").asText(); // 操作人姓名

                if (waitCustomConfirmFlag && operationType == 3 && innerReply.contains("认领原因:工单回访")) {
                    claimFlag = true;
                    waitCustomConfirmFlag = false;
                }

                // 待客户补充
                if (operationType == 11 && StringUtils.isEmpty(customerAddTime)) {
                    customerAddTime = operateTime;
                }

                if (operationType == 38 && !recoveredFlag) {
                    notRecoveredFlag = true;
                } else if (operationType == 36 && !notRecoveredFlag) {
                    recoveredFlag = true;
                } else if (operationType == 35 && StringUtils.isEmpty(tobeRecoveredTime)) { //待确认业务恢复
                    tobeRecoveredTime = operateTime;
                } else if (operationType == 12 && StringUtils.isEmpty(applyCloseTime)) {
                    applyCloseTime = operateTime;
                } else if (operationType == 12 && StringUtils.isEmpty(applyCloseTime)) {
                    applyCloseTime = operateTime;
                } else if (operationType == 13 && targetStatus == 4 && StringUtils.isEmpty(acceptCloseTime)) {
                    acceptCloseTime = operateTime; // “同意申请结单”且“target_status”为"待补充"
                } else if (operationType == 24 && targetStatus == 5 && StringUtils.isEmpty(mulReplyTroubleTicketTime)) {
                    mulReplyTroubleTicketTime = operateTime;
                }

                if (systemCloseTicket && !operationTypeExcludeList.contains(operationType)) {
                    if (operationType == 15) {
                        waitCustomConfirmFlag = true;
                        if (operatorType == 3) {
                            ticketCloseType = 0;
                            systemCloseTicket = false;
                        } else if (operatorType == 2) {
                            ticketCloseType = 5;
                            systemCloseTicket = false;
                            customerServiceCloseTimeV2 = operateTime;
                            toBeConfirmCloseTimes += 1;
                        }
                    } else if (operationType == 13 && targetStatus == 5) {
                        ticketCloseType = 1;
                        systemCloseTicket = false;
                        if (operatorType == 2) {
                            toBeConfirmCloseTimes += 1;
                        }
                    } else if (operationType == 24 && targetStatus == 5) {
                        ticketCloseType = 6;
                        systemCloseTicket = false;
                    } else if (operationType == 11) {
                        ticketCloseType = 7;
                        systemCloseTicket = false;
                    }
                }

                if (customerCloseTicket && !operationTypeExcludeList.contains(operationType)) {
                    customerCloseTicket = false;  // 此逻辑只需判定客户结单前一条非operation_exclude_list操作类型的流水

                    if (operationType == 15) { // 待客户确认结单
                        waitCustomConfirmFlag = true;
                        if (operatorType == 3) {
                            ticketCloseType = 0; // "客户结单"，最后一次待客户确认结单是“系统”操作的
                        } else if (operatorType == 2) {
                            ticketCloseType = 5; // "客户结单"，最后一次待客户确认结单是“客服”操作的
                            customerServiceCloseTimeV2 = operateTime;
                            toBeConfirmCloseTimes += 1;
                        }
                    } else if (operationType == 13 && targetStatus == 5) {
                        ticketCloseType = 1;
                        if (operatorType == 2) {
                            toBeConfirmCloseTimes += 1;
                        }
                    } else if (operationType == 11) {
                        ticketCloseType = 2;
                    } else if (operationType == 35 || operationType == 36) {
                        ticketCloseType = 3;
                    } else if (operationType == 24 && targetStatus == 5) {
                        ticketCloseType = 6;
                    } else {
                        ticketCloseType = 4; // 如果是"客户"结单,结单前一条流水不符合以上四种类型,就用最后一次"结单"时间作为结单时间点
                    }
                }

                if (operatorType == 3 && operationType == 16 && !serviceChannelExcludeList.contains(serviceChannel)) {
                    systemCloseTicket = true;
                } else if (operationType == 16
                        && (operatorType == 1 || serviceChannelExcludeList.contains(serviceChannel))) {
                    customerCloseTicket = true;
                    customerCloseTime = operateTime;
                } else if (operatorType == 2 && operationType == 16
                        && !operatorName.equalsIgnoreCase("SYSTEM")
                        && !serviceChannelExcludeList.contains(serviceChannel)) {
                    customerServiceCloseTimeV1 = operateTime; // "客服"结单
                }
            }

            //tobeRecoveredTime、customerAddTime、acceptCloseTime 三个字段取最大值
            Optional<String> maxCloseTime = Stream.of(tobeRecoveredTime, customerAddTime, acceptCloseTime)
                    .filter(StringUtils::isNotEmpty)
                    .max(Comparator.comparing(String::valueOf));

            if (StringUtils.isNotEmpty(customerServiceCloseTimeV1)) {
                ticketSolvedTime = customerServiceCloseTimeV1;
            } else if ((ticketCloseType == 0 || ticketCloseType == 7) && maxCloseTime.isPresent()) {
                ticketSolvedTime = maxCloseTime.get();
            } else if (ticketCloseType == 1 && StringUtils.isNotEmpty(applyCloseTime)) {
                ticketSolvedTime = applyCloseTime;
            } else if (ticketCloseType == 2 && StringUtils.isNotEmpty(customerAddTime)) {
                ticketSolvedTime = customerAddTime;
            } else if (ticketCloseType == 3 && StringUtils.isNotEmpty(tobeRecoveredTime)) {
                ticketSolvedTime = tobeRecoveredTime;
            } else if (ticketCloseType == 4 && StringUtils.isNotEmpty(customerCloseTime)) {
                ticketSolvedTime = customerCloseTime;
            } else if (ticketCloseType == 6 && StringUtils.isNotEmpty(mulReplyTroubleTicketTime)) {
                ticketSolvedTime = mulReplyTroubleTicketTime;
            } else if (ticketCloseType == 5) {
                if (maxCloseTime.isPresent() && maxCloseTime.get().equals(tobeRecoveredTime)
                        && (recoveredFlag || !notRecoveredFlag)) {
                    ticketSolvedTime = tobeRecoveredTime;
                } else if (maxCloseTime.isPresent() && maxCloseTime.get().equals(customerAddTime)) {
                    ticketSolvedTime = claimFlag ? customerAddTime : customerServiceCloseTimeV2;
                } else {
                    ticketSolvedTime = customerServiceCloseTimeV2;
                }
            } else if (ticketCloseType == 6 && StringUtils.isNotEmpty(mulReplyTroubleTicketTime)) {
                ticketSolvedTime = mulReplyTroubleTicketTime;
            } else {
                ticketSolvedTime = firstCloseTime;
            }

            row.setField("ticket_solved_time", ticketSolvedTime);
            row.setField("to_be_confirm_close_times", toBeConfirmCloseTimes);

            ticketOperations = null;
            return row;
        }
    }

    /**
     * 工单未结单，解决时间相关统计
     */
    public static class TicketNotClosedSolvedTimeMapFunction implements MapFunction<Row, Row> {


        @Override
        public Row map(Row row) throws Exception {
            List<JsonNode> ticketOperations = getTicketOperationList(row);

            int toBeConfirmCloseTimes = 0; // 待客户确认结单次数
            String ticketSolvedTime = row.getFieldAs("ticket_solved_time"); // 工单解决时间

            // 辅助字段
            boolean claimFlag = false;
            boolean recoveredFlag = false;
            boolean notRecoveredFlag = false;
            boolean waitCustomConfirmFlag = false;

            String cancelTime = "";
            int ticketCloseType = -1;
            String applyCloseTime = "";
            String customerAddTime = "";
            String acceptCloseTime = "";
            String serviceCloseTime = "";
            String tobeRecoveredTime = "";
            String mulReplyTroubleTicketTime = "";

            // 工单解决时间为空
            if (StringUtils.isEmpty(ticketSolvedTime) || ticketSolvedTime.equals("1970-01-01 00:00:00")) {
                // 反转ticketOperations
                Collections.reverse(ticketOperations);
                for (JsonNode operation : ticketOperations) {
                    int targetStatus = operation.get("target_status").asInt();
                    int operatorType = operation.get("operator_type").asInt();
                    String innerReply = operation.get("inner_reply").asText();
                    int operationType = operation.get("operation_type").asInt();
                    String operateTime = operation.get("operate_time").asText();

                    if (waitCustomConfirmFlag) {
                        waitCustomConfirmFlag = false;
                        if (operationType == 3 && innerReply.contains("认领原因:工单回访")) {
                            claimFlag = true;
                        }
                    }

                    // 记录相关时间点
                    if (operationType == 38 && !recoveredFlag) {
                        notRecoveredFlag = true;
                    } else if (operationType == 36 && !notRecoveredFlag) {
                        recoveredFlag = true;
                    } else if (operationType == 17 && StringUtils.isEmpty(cancelTime)) {
                        cancelTime = operateTime;
                    } else if (operationType == 11 && StringUtils.isEmpty(customerAddTime)) {
                        customerAddTime = operateTime;
                    } else if (operationType == 35 && StringUtils.isEmpty(tobeRecoveredTime)) {
                        tobeRecoveredTime = operateTime;
                    } else if (operationType == 12 && StringUtils.isEmpty(applyCloseTime)) {
                        applyCloseTime = operateTime;
                    } else if (operationType == 24 && StringUtils.isEmpty(mulReplyTroubleTicketTime)) {
                        mulReplyTroubleTicketTime = operateTime;
                    } else if (operationType == 13 && targetStatus == 4 && StringUtils.isEmpty(acceptCloseTime)) {
                        acceptCloseTime = operateTime;
                    } else if (operationType == 15 && operatorType == 2 && StringUtils.isEmpty(serviceCloseTime)) {
                        serviceCloseTime = operateTime;
                    }

                    // 判断距离“当前时间节点”最近的操作类型
                    if (ticketCloseType < 0 && !operationTypeExcludeList.contains(operationType)) {
                        if (operationType == 15) {
                            waitCustomConfirmFlag = true;
                            if (operatorType == 3) {
                                ticketCloseType = 0; // 系统结单
                            } else if (operatorType == 2) {
                                ticketCloseType = 1; // 客服结单
                                toBeConfirmCloseTimes += 1;
                            }
                        } else if (operationType == 35 || operationType == 36) {
                            ticketCloseType = 2;
                        } else if (operationType == 13 && targetStatus == 5) {
                            ticketCloseType = 3;
                            if (operatorType == 2) {
                                toBeConfirmCloseTimes += 1;
                            }
                        } else if (operationType == 24 && targetStatus == 5) {
                            ticketCloseType = 4;
                        } else if (operationType == 17) {
                            ticketCloseType = 5;
                        }
                    }
                }

                // 取相关操作记录最近的时间('待客户补充', '待确认业务恢复', '同意申请结单且target_status为"待补充"')
                Optional<String> maxCloseTime = Stream.of(tobeRecoveredTime, customerAddTime, acceptCloseTime)
                        .filter(StringUtils::isNotEmpty)
                        .max(Comparator.comparing(String::valueOf));

                // 根据标记条件去不同的时间点作为结单时间
                if (ticketCloseType == 1) {
                    if (maxCloseTime.isPresent() && maxCloseTime.get().equals(customerAddTime) && claimFlag) {
                        ticketSolvedTime = customerAddTime;
                    } else if (maxCloseTime.isPresent()
                            && maxCloseTime.get().equals(tobeRecoveredTime)
                            && (recoveredFlag || !notRecoveredFlag)) {
                        ticketSolvedTime = tobeRecoveredTime;
                    } else {
                        ticketSolvedTime = serviceCloseTime;
                    }
                } else if (ticketCloseType == 0 && maxCloseTime.isPresent()) {
                    ticketSolvedTime = maxCloseTime.get();
                } else if (ticketCloseType == 2) {
                    ticketSolvedTime = tobeRecoveredTime;
                } else if (ticketCloseType == 3) {
                    ticketSolvedTime = applyCloseTime;
                } else if (ticketCloseType == 4) {
                    ticketSolvedTime = mulReplyTroubleTicketTime;
                } else if (ticketCloseType == 5) {
                    ticketSolvedTime = cancelTime;
                }

                ticketOperations = null;
                row.setField("ticket_solved_time", ticketSolvedTime);
                row.setField("to_be_confirm_close_times", toBeConfirmCloseTimes);
            }
            return row;
        }
    }

    public static class TicketStaffPostProcessFunction extends BroadcastProcessFunction<Row, Row, Row> {

        private final MapStateDescriptor<String, Integer> staffPostStateDes = new MapStateDescriptor<>(
                "staffPostBroadcastState", BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.INT_TYPE_INFO);

        @Override
        public void processElement(Row row, BroadcastProcessFunction<Row, Row, Row>.ReadOnlyContext ctx,
                Collector<Row> out) throws Exception {

            // 通过 ReadOnlyContext ctx 取到的广播状态对象，是一个 “只读 ” 的对象；
            ReadOnlyBroadcastState<String, Integer> staffPostMap = ctx.getBroadcastState(staffPostStateDes);

            List<JsonNode> ticketOperations = getTicketOperationList(row);

            // ticketOperations 不为空
            if (ticketOperations.isEmpty()) {
                LOG.error("ticketOperations is empty");
            }

            // 服务通道
            String serviceChannel = Optional
                    .ofNullable(row.getFieldAs("service_channel"))
                    .orElse("")
                    .toString();

            // 获取staff_post, 遍历ticketOperations
            for (JsonNode operation : ticketOperations) {
                Integer operatorPost = null; // 操作人岗位
                String operator = operation.get("operator").asText();
                String customerFields = operation.get("customer_fields").asText();
                String key = operator + "-" + serviceChannel;
                Integer value = staffPostMap.get(key);

                // 从customer_fields字段中获取operator_post
                if (contentIsNotEmpty(customerFields) && isValidJson(customerFields)) {
                    operatorPost = JsonPath
                            .using(conf)
                            .parse(customerFields)
                            .read("$.operator_post", Integer.class);
                }

                // 如果value不为空，说明该员工在该服务通道下有岗位
                // 如果value为空，说明该员工在该服务通道下没有岗位，如果operator(user_id)为数字，则是一线，否则是"其他"
                if (operatorPost == null) {
                    operatorPost = value != null ? value : StringUtils.isNumeric(operator) ? 2 : 0;
                }

                ((ObjectNode) operation).put("operator_post", operatorPost);
            }

            row.setField("operation_data", mapper.writeValueAsString(ticketOperations));
            ticketOperations = null;
            out.collect(row);
        }

        @Override
        public void processBroadcastElement(Row row, BroadcastProcessFunction<Row, Row, Row>.Context ctx,
                Collector<Row> out) throws Exception {
            // 从上下文中，获取广播状态对象（可读可写的状态对象），然后将获得的这条  广播流数据， 拆分后，装入广播状态
            ctx.getBroadcastState(staffPostStateDes)
                    .put(row.getFieldAs("pk"), row.<Integer>getFieldAs("operator_post"));
        }
    }

    public static class TicketStaffInfoProcessFunction extends BroadcastProcessFunction<Row, Row, Row> {

        private final MapStateDescriptor<String, Tuple2<String, Integer>> staffInfoStateDesc = new MapStateDescriptor<>(
                "staffInfoBroadcastState", TypeInformation.of(String.class),
                TypeInformation.of(new TypeHint<Tuple2<String, Integer>>() {
                }));

        public static Object checkNull(Tuple2<String, Integer> tuple, Object defaultValue, int order) {
            if (tuple == null) {
                return defaultValue;
            }

            switch (order) {
                case 1:
                    return tuple.f0 != null ? tuple.f0 : defaultValue;
                case 2:
                    return tuple.f1 != null ? tuple.f1 : defaultValue;
                default:
                    throw new IllegalArgumentException("Invalid order value. It should be 1 or 2.");
            }
        }

        @Override
        public void processElement(Row row, BroadcastProcessFunction<Row, Row, Row>.ReadOnlyContext ctx,
                Collector<Row> out) throws Exception {

            // 通过 ReadOnlyContext ctx 取到的广播状态对象，是一个 “只读 ” 的对象；
            ReadOnlyBroadcastState<String, Tuple2<String, Integer>> state = ctx.getBroadcastState(staffInfoStateDesc);

            List<JsonNode> ticketOperations = getTicketOperationList(row);

            // 断言：ticketOperations 不为空
            assert !ticketOperations.isEmpty();

            // 获取staff_name, 遍历ticketOperations
            for (JsonNode operation : ticketOperations) {
                Integer operatorAssignCompanyId = null;
                final String operator = operation.get("operator").asText();
                final String responsible = operation.get("responsible").asText();
                String customerFields = operation.get("customer_fields").asText();
                final String nextOperator = operation.get("next_operator").asText();
                final String currentOperator = operation.get("current_operator").asText();
                final String nextResponsible = operation.get("next_responsible").asText();

                // 从customer_fields字段中获取operator_post
                if (contentIsNotEmpty(customerFields) && isValidJson(customerFields)) {
                    operatorAssignCompanyId = JsonPath
                            .using(conf)
                            .parse(customerFields)
                            .read("$.assign_company_id", Integer.class);
                }

                // 操作人所属公司
                int operatorCompanyId = (int) checkNull(state.get(operator), -1, 2);

                // 下一操作人所属公司
                int nextOperatorCompanyId = (int) checkNull(state.get(nextOperator), -1, 2);

                // 操作人姓名
                String operatorName = (String) checkNull(state.get(operator), operator, 1);

                // 当前客户代表姓名
                String responsibleName = (String) checkNull(state.get(responsible), responsible, 1);

                // 下一操作人姓名
                String nextOperatorName = (String) checkNull(state.get(nextOperator), nextOperator, 1);

                // 当前操作人姓名
                String currentOperatorName = (String) checkNull(state.get(currentOperator), currentOperator, 1);

                // 下一客户代表姓名
                String nextResponsibleName = (String) checkNull(state.get(nextResponsible), nextResponsible, 1);

                ((ObjectNode) operation).put("operator_name", operatorName);
                ((ObjectNode) operation).put("responsible_name", responsibleName);
                ((ObjectNode) operation).put("next_operator_name", nextOperatorName);
                ((ObjectNode) operation).put("current_operator_name", currentOperatorName);
                ((ObjectNode) operation).put("next_responsible_name", nextResponsibleName);
                ((ObjectNode) operation).put("next_operator_company_id", nextOperatorCompanyId);
                ((ObjectNode) operation).put("operator_company_id", operatorAssignCompanyId
                        == null ? operatorCompanyId : operatorAssignCompanyId);
            }

            row.setField("operation_data", mapper.writeValueAsString(ticketOperations));
            ticketOperations = null;
            out.collect(row);
        }

        @Override
        public void processBroadcastElement(Row row, BroadcastProcessFunction<Row, Row, Row>.Context ctx,
                Collector<Row> out) throws Exception {
            // 从上下文中，获取广播状态对象（可读可写的状态对象），然后将获得的这条广播流数据拆分后，装入广播状态

            String uid = row.getFieldAs("uid");
            String userId = row.getFieldAs("user_id");
            int assignCompanyId = row.getFieldAs("assign_company_id")
                    == null ? -1 : row.getFieldAs("assign_company_id");

            ctx.getBroadcastState(staffInfoStateDesc).put(uid, Tuple2.of(userId, assignCompanyId));
        }
    }

    /**
     * 工单拒单统计
     */
    public static class TicketDenyInfoMapFunction implements MapFunction<Row, Row> {

        @Override
        public Row map(Row row) throws Exception {
            List<JsonNode> ticketOperations = getTicketOperationList(row);

            // 辅助字段
            String time2;
            String time1 = null;
            String time3 = null;
            int firstResponseQueue = 0; // 首次响应队列
            String operationDenyReason = ""; // 运维拒绝原因
            String secondLineDenyReason = ""; // 1.5线拒绝原因
            String productResearchDenyReason = ""; // 产研拒绝原因
            String backendFirstDenyOperator = ""; // 后端首次拒单人
            int customerWaitingImpatienceDuration = 0; // 用户等待的不耐烦时长
            String verticalProductResearchDenyReason = ""; // 垂直产研拒绝原因

            List<String> staffResponseTimeList = new ArrayList<>();
            List<String> customerResponseTimeList = new ArrayList<>();

            // 辅助字段
            boolean isFirstUrgent = true;
            boolean operationDenyFlag = true; // 运维是否拒绝
            boolean secondLineDenyFlag = true; // 1.5线是否拒单
            boolean productResearchDenyFlag = true; // 产研是否拒单
            boolean firstResponseQueueFlag = true; // 是否首次响应队列
            boolean verticalProductResearchDenyFlag = true; // 垂直产研是否拒单
            ArrayList<Integer> elementIndexList = new ArrayList<>(); // 存放后端拒单的流水

            ListIterator<JsonNode> it = ticketOperations.listIterator();
            while (it.hasNext()) {
                int idx = it.nextIndex();
                JsonNode operation = it.next();
                final int post = operation.get("post").asInt(); // 当前岗位
                final String remark = operation.get("remark").asText(); // 备注
                final int factAssign = operation.get("fact_assign").asInt(); // 实派队列
                final int operatorPost = operation.get("operator_post").asInt(); // 客服岗位
                final int operatorType = operation.get("operator_type").asInt(); // 操作人类型
                final int operationType = operation.get("operation_type").asInt(); // 操作类型
                final String externReply = operation.get("extern_reply").asText(); // 外部回复
                final String operateTime = operation.get("operate_time").asText(); // 操作时间

                // 计算首次响应队列
                if ((operatorType == 2 && contentIsNotEmpty(externReply)) || operationType == 44) {
                    if (firstResponseQueueFlag && operationType != 1) {
                        firstResponseQueue = factAssign;
                        firstResponseQueueFlag = false;
                    }
                }

                // 计算拒绝原因
                if ((operationType == 22 || operationType == 61) && StringUtils.isNotEmpty(remark)) {
                    Pattern regexp = Pattern.compile("拒绝原因： (.*?)<br/>");
                    Matcher matcher = regexp.matcher(remark);
                    if (post == 4 && operationDenyFlag && matcher.matches()) {
                        operationDenyFlag = false;
                        operationDenyReason = matcher.group(1);
                    } else if (post == 3 && secondLineDenyFlag && matcher.matches()) {
                        secondLineDenyFlag = false;
                        secondLineDenyReason = matcher.group(1);
                    } else if (post == 5 && productResearchDenyFlag && matcher.matches()) {
                        productResearchDenyFlag = false;
                        productResearchDenyReason = matcher.group(1);
                    } else if (post == 11 && verticalProductResearchDenyFlag && matcher.matches()) {
                        verticalProductResearchDenyFlag = false;
                        verticalProductResearchDenyReason = matcher.group(1);
                    }
                }

                // 计算后端首次拒绝人
                if (backendPostList.contains(post) && backendPostList.contains(operatorPost)
                        && (operationType == 22 || operationType == 61)) {
                    elementIndexList.add(idx);
                }

                if (operationType == 4 && operatorType == 1 && isFirstUrgent) {
                    time1 = operateTime;
                    isFirstUrgent = false;
                }

                if (operatorType == 2 && contentIsNotEmpty(externReply)) {
                    staffResponseTimeList.add(operateTime);
                }

                if (operatorType == 1 && contentIsNotEmpty(externReply)) {
                    customerResponseTimeList.add(operateTime);
                }
            }

            if (StringUtils.isNotEmpty(time1)) {
                final String finalTime = time1;
                time2 = staffResponseTimeList.stream().filter(x -> finalTime.compareTo(x) > 0)
                        .max(Comparator.naturalOrder()).orElse(null);

                if (StringUtils.isNotEmpty(time2)) {
                    time3 = customerResponseTimeList.stream()
                            .filter(x -> time2.compareTo(x) < 0 && x.compareTo(finalTime) < 0)
                            .min(Comparator.naturalOrder()).orElse(null);
                }
            }

            if (StringUtils.isNotEmpty(time3)) {
                customerWaitingImpatienceDuration = (int) timeDelta(time3, time1);
            }

            // 获取后端拒单流水中最早单流水记录
            if (!elementIndexList.isEmpty()) {
                int min = Collections.min(elementIndexList);
                backendFirstDenyOperator = ticketOperations.get(min).get("operator_name").asText();
            }

            row.setField("first_response_queue", firstResponseQueue);
            row.setField("operation_deny_reason", operationDenyReason);
            row.setField("second_line_deny_reason", secondLineDenyReason);
            row.setField("backend_first_deny_operator", backendFirstDenyOperator);
            row.setField("product_research_deny_reason", productResearchDenyReason);
            row.setField("customer_waiting_impatience_duration", customerWaitingImpatienceDuration);
            row.setField("vertical_product_research_deny_reason", verticalProductResearchDenyReason);
            ticketOperations = null;
            return row;
        }
    }

    /**
     * 工单处理时长
     */
    public static class TicketDealDurationMapFunction implements MapFunction<Row, Row> {

        @Override
        public Row map(Row row) throws Exception {
            int agentDuration = 0; // 供应商处理时长
            int unknownDuration = 0; // 其他角色处理时长
            int customerDuration = 0; // 客户停留总时长(秒)
            int firstLineDuration = 0; // 一线处理时长
            int operationDuration = 0; // 运维处理时长
            int secondLineDuration = 0; // 1.5线处理时长
            int ticketDealDuration = 0; // 工单总时长(秒)
            int agentCustomerDuration = 0; // 客户在供应商停留时长
            int productResearchDuration = 0; // 产研处理时长
            int unknownCustomerDuration = 0; // 客户在其他停留时长
            int firstLineCustomerDuration = 0; // 客户在一线停留时长
            int secondLineCustomerDuration = 0; // 客户在1.5线停留时长
            int verticalProductResearchDuration = 0; // 垂直产研处理时长
            int tegQueueDuration = 0; // TEG队列处理时长

            // 辅助字段
            int customerDurationStartFlag = 0;
            String createTime = row.getFieldAs("ticket_create_time");
            String ticketSolvedTime = row.getFieldAs("ticket_solved_time");

            if (StringUtils.isNotEmpty(ticketSolvedTime)
                    && !ticketSolvedTime.equals("1970-01-01 00:00:00")
                    && StringUtils.isNotEmpty(createTime)) {
                ticketDealDuration = (int) timeDelta(createTime, ticketSolvedTime);
            }

            List<JsonNode> ticketOperations = getTicketOperationList(row);
            for (JsonNode operation : ticketOperations) {
                final int post = operation.get("post").asInt();
                final int duration = operation.get("duration").asInt();
                final int operatorType = operation.get("operator_type").asInt(); // 操作人类型
                final int operationType = operation.get("operation_type").asInt(); // 操作类型
                final String operateTime = operation.get("operate_time").asText(); // 操作时间
                final int targetStatus = operation.get("target_status").asInt(); // 状态
                final int factAssign = operation.get("fact_assign").asInt();

                if (!ticketSolvedTime.equals("1970-01-01 00:00:00") && operateTime.compareTo(ticketSolvedTime) > 0) {
                    break;
                }

                if (operatorType == 2) {
                    if (factAssign == 4218 || factAssign == 4217) {
                        tegQueueDuration += duration;
                    }
                }

                // 计算各岗位处理时长
                if ((operatorType == 1 || operatorType == 2 || operatorType == 6) || operationType == 15) {
                    if (post == 1) {
                        agentDuration += duration;
                    } else if (post == 2) {
                        firstLineDuration += duration;
                    } else if (post == 3) {
                        secondLineDuration += duration;
                    } else if (post == 0) {
                        unknownDuration += duration;
                    }

                    if (operatorType == 1 || operatorType == 2 || operatorType == 6) {
                        if (post == 4) {
                            operationDuration += duration;
                        } else if (post == 5) {
                            productResearchDuration += duration;
                        } else if (post == 11) {
                            verticalProductResearchDuration += duration;
                        }
                    }
                }

                if ((operatorType == 1 || operatorType == 2)) {
                    //停留时长计算
                    if (customerDurationStartFlag > 0) {
                        if (targetStatus != 4 && targetStatus != 25 && targetStatus != 5) {
                            customerDurationStartFlag = 0;
                        }

                        customerDuration += duration;

                        if (post == 1) {
                            agentCustomerDuration += duration;
                        }

                        if (post == 2) {
                            firstLineCustomerDuration += duration;
                        }

                        if (post == 3) {
                            secondLineCustomerDuration += duration;
                        }

                        if (post == 0) {
                            unknownCustomerDuration += duration;
                        }
                    }

                    if (targetStatus == 4 || targetStatus == 25 || targetStatus == 5) {
                        customerDurationStartFlag = 1;
                    }
                }
            }

            row.setField("agent_duration", agentDuration);
            row.setField("unknown_duration", unknownDuration);
            row.setField("customer_duration", customerDuration);
            row.setField("operation_duration", operationDuration);
            row.setField("first_line_duration", firstLineDuration);
            row.setField("second_line_duration", secondLineDuration);
            row.setField("ticket_deal_duration", ticketDealDuration);
            row.setField("agent_customer_duration", agentCustomerDuration);
            row.setField("product_research_duration", productResearchDuration);
            row.setField("unknown_customer_duration", unknownCustomerDuration);
            row.setField("first_line_customer_duration", firstLineCustomerDuration);
            row.setField("second_line_customer_duration", secondLineCustomerDuration);
            row.setField("vertical_product_research_duration", verticalProductResearchDuration);
            row.setField("agent_engineer_duration", agentDuration - agentCustomerDuration);
            row.setField("ticket_staff_deal_duration", ticketDealDuration - customerDuration);
            row.setField("unknown_engineer_duration", unknownDuration - unknownCustomerDuration);
            row.setField("first_line_engineer_duration", firstLineDuration - firstLineCustomerDuration);
            row.setField("second_line_engineer_duration", secondLineDuration - secondLineCustomerDuration);
            row.setField("teg_queue_duration", tegQueueDuration);
            ticketOperations = null;
            return row;
        }
    }


    /**
     * 工单转单
     */
    public static class TicketTransferInfoMapFunction implements MapFunction<Row, Row> {

        @Override
        public Row map(Row row) throws Exception {
            String measures = ""; //处理结果
            String operationHandler = ""; // 运维处理人
            int isTransferFirstLine = 0; // 是否转过1线
            int transferFirstLineTimes = 0; // 转1线次数
            int transferOperationTimes = 0; // 转运维次数
            int transferFirstLineQueue = -1; // 转1线队列
            String lastOperator = ""; // 最后一次客服处理人
            int isDeniedByOperation = 0; // 是否被运维拒绝过
            int transferSecondLineTimes = 0; // 转1.5线次数
            String transferFirstLineStaff = ""; // 转1线客服
            int isDeniedBySecondLine = 0; // 是否被1.5线拒绝过
            int secondLineRefuseTimes = 0; // 被一点五线拒绝次数
            int firstSecondLineAssign = -1; // 1.5线首次接单队列
            int lastOperationFactAssign = -1; // 最新运维实派队列
            String secondLineLastRefuser = ""; // 1.5线最后拒单人
            int secondLineSolvedDuration = 0; // 1.5线解决问题时长
            int transferProductionResearchTimes = 0; // 转产研次数
            String currentFirstLineOperator = ""; // 1线当前处理人
            String firstSecondLineOperator = ""; // 1.5线首次接单人
            int operationHandlerCompanyId = -1; // 运维处理人所属公司
            int firstSecondLineFactAssign = -1; // 1.5线首次实派队列
            int isDeniedByProductionResearch = 0; // 是否被产研拒绝过
            String currentSecondLineOperator = ""; // 1.5线当前处理人
            int currentSecondLineFactAssign = -1; // 当前1.5线实派队列
            int isTransferCompanyInSecondLine = 0; // 1.5线是否转供应商
            int firstSecondLineResponseDuration = 0; // 1.5线首次响应时长
            String firstTransferSecondLineTime = ""; // 首次转1.5线的时间
            String firstTransferOperationStaff = ""; // 首次操作转运维的人
            int lastProductionResearchFactAssign = -1; // 最新产研实派队列
            String firstSecondLineFactOperator = ""; // 1.5线首次真实受理人
            int isTransferInnerStaffInSecondLine = 0; // 1.5线是否转正式员工
            String firstTransferSecondLineStaff = ""; // 首次操作转1.5线的人
            String secondLineLastCompanyOperator = ""; //1.5线供应商最终处理人
            Set<String> secondLineOperators = new HashSet<>(); // 1.5线经手人
            //StringBuilder content = new StringBuilder(); // 工单流水中交互内容
            int transferVerticalProductionResearchTimes = 0; // 转垂直产研次数
            int hotlineFirstTransferDealDuration = 0; // 热线首次转单后总处理时长
            String transferFirstLineTime = "1970-01-01 00:00:00"; // 转1线时间
            int firstTransferOperationStaffPost = -1; // 首次操作转运维的人的岗位
            int firstSecondLineOperatorCompanyId = -1; // 1.5线首次接单人所属公司
            String secondLineFirstProcessingTime = ""; // 1.5线首次转"处理"中时间
            int currentSecondLineOperatorCompanyId = -1; // 1.5线当前处理人所属公司
            int hotlineFirstTransferResponseDuration = 0; // 热线首次转单后首次响应时长
            int firstTransferProductionResearchStaffPost = -1; // 首次操作转产研的人的岗位
            int fstTransVertProductionResearchStaffPost = -1; // 首次操作转垂直产研的人的岗位
            String firstSecondLineServiceTime = "1970-01-01 00:00:00"; // 1.5线首次服务时间
            String hotlineFirstTransferTime = "1970-01-01 00:00:00"; // 热线首次转单开始时间
            int secondLineLastCompanyOperatorCompanyId = -1; // 1.5线供应商最终处理人所属公司
            String firstTransferOperationTime = "1970-01-01 00:00:00"; // 首次操作转运维的时间
            String firstTransferComplaintQueueTime = "1970-01-01 00:00:00"; // 首次转投诉队列时间
            String firstTransferProductionResearchTime = "1970-01-01 00:00:00"; //首次转转产研时间
            String hotlineFirstTransferResponseTime = "1970-01-01 00:00:00"; // 热线首次转单后首次响应时间
            HashMap<String, Integer> secondLineOperatorCompanyIds = new HashMap<>(); // 1.5线经手人所属公司
            String firstLineLastOperator = ""; // 1线最后一次处理人
            int firstLineLastOperatorCompanyId = -1; // 1线最后一次处理人所属公司
            int firstLineLastOperatorFactAssign = -1; // 1线最后一次处理人实派队列
            String productionResearchOperator = ""; // 产研处理人
            int isTransferTgw = 0; // 是否转过tgw队列
            String lastOperatorNew = ""; //最后处理人
            int lastOperatorPost = -1; // 最后处理人岗位

            // 辅助字段
            boolean isFirstAssignFlag = true;
            boolean isFirstCheckFlag = false;
            int secondLineFirstFactAssign = -1;
            String secondLineFirstOperator = "";
            String secondLineFirstFactTime = "";
            boolean isFirstSecondLineFactFlag = true;
            String firstSecondLineServiceEndTime = "";
            boolean isFirstTransferOperationFlag = true;
            boolean isFirstTransferSecondLineFlag = true;
            boolean isFirstSecondLineOperatorFlag1 = true;
            boolean isFirstSecondLineOperatorFlag2 = true;
            boolean isFirstSecondLineOperatorFlag3 = true;
            boolean firstTransferProductionResearchFlag = true;
            boolean isFirstTransferProductionResearchFlag = true;
            boolean isFirstTransferComplaintQueueTimeFlag = true;
            ArrayList<String> transferPostList = new ArrayList<>();
            TreeMap<String, Map<String, Integer>> operatorTreeMap = new TreeMap<>();

            // 辅助字段
            boolean isFirstTransferVerticalProductionResearchFlag = true;

            // 服务通道
            String serviceChannel = Optional
                    .ofNullable(row.getFieldAs("service_channel"))
                    .orElse("")
                    .toString();

            List<Integer> operationTypeInExcludeList = new ArrayList<>(Arrays.asList(5, 8, 11, 12, 15, 28, 44));

            List<JsonNode> ticketOperations = getTicketOperationList(row);
            ListIterator<JsonNode> it = ticketOperations.listIterator();

            String closeTime = row.getFieldAs("close_time"); // 结单时间
            String ticketSolvedTime = row.getFieldAs("ticket_solved_time"); // 工单解决时间

            boolean isCloseFlag = ticketOperations.stream().anyMatch(op -> op.get("operation_type").asInt() == 16);

            // 初始化变量
            JsonNode lastFirstLineOperation = null;
            JsonNode lastSecondLineOperation = null;
            JsonNode lastOperatorOption = null;

            while (it.hasNext()) {
                int idx = it.nextIndex();
                JsonNode preOperation = null;
                JsonNode operation = it.next();
                final int post = operation.get("post").asInt();
                String innerReply = operation.get("inner_reply").asText();
                String externReply = operation.get("extern_reply").asText();
                final int factAssign = operation.get("fact_assign").asInt();
                final int nextAssign = operation.get("next_assign").asInt();
                final int targetPost = operation.get("target_post").asInt();
                final int targetStatus = operation.get("target_status").asInt();
                final int operatorType = operation.get("operator_type").asInt(); // 操作人类型
                final int operatorPost = operation.get("operator_post").asInt(); // 操作人岗位
                final int operationType = operation.get("operation_type").asInt(); // 操作类型
                final String operateTime = operation.get("operate_time").asText(); // 操作时间
                final String operatorName = operation.get("operator_name").asText(); // 操作人姓名
                final int operatorCompanyId = operation.get("operator_company_id").asInt(); // 操作人公司
                final String nextOperatorName = operation.get("next_operator_name").asText(); // 下一操作人姓名
                final int nextOperatorCompanyId = operation.get("next_operator_company_id").asInt(); // 下一操作人公司

                // 工单内外部回复内容
                // content.append(ContentFilter(innerReply + externReply)).append("\n");

                boolean passesFilter = !operatorName.equalsIgnoreCase("SYSTEM")
                        && operatorPost != -1
                        && (!isCloseFlag || (StringUtils.isNotEmpty(closeTime)
                        && !closeTime.equals("1970-01-01 00:00:00")
                        && operateTime.compareTo(closeTime) <= 0));

                if (passesFilter) {
                    // 检查是否为符合条件的最后一个一线客服操作
                    if (firstLineOperationTypes.contains(operationType)
                            && operatorPost == 2
                            && operatorType == 2) {
                        lastFirstLineOperation = operation;
                    }

                    // 检查是否为符合条件的最后一个1.5线客服操作
                    if (secondLineOperationTypes.contains(operationType)
                            && operatorPost == 3
                            && operatorType == 2) {
                        lastSecondLineOperation = operation;
                    }

                    // 检查是否为符合条件的最后处理人
                    if ((operatorPost == 2 && firstLineOperationTypes.contains(operationType))
                            || (operatorPost == 3 && secondLineOperationTypes.contains(operationType))) {
                        lastOperatorOption = operation;
                    }
                }


                if (factAssign == 6210 || factAssign == 6209) {
                    isTransferTgw = 1;
                }

                if (post == 5 && operatorPost == 5
                        && operateTime.compareTo(ticketSolvedTime) <= 0
                        && !ticketSolvedTime.equals("1970-01-01 00:00:00")) {
                    productionResearchOperator = operatorName;
                }

                if (idx > 0) {
                    preOperation = ticketOperations.get(idx - 1); // 获取当前流水前一条流水
                }

                if (!transferPostList.contains(String.valueOf(post))) {
                    transferPostList.add(String.valueOf(post));
                }

                if (post == 3) {
                    currentSecondLineFactAssign = factAssign;
                } else if (operatorPost == 2 && !operatorName.equalsIgnoreCase("SYSTEM")) {
                    currentFirstLineOperator = operatorName;
                }

                if (post == 4 && operatorPost == 4) {
                    operationHandler = operatorName;
                    operationHandlerCompanyId = operatorCompanyId;
                }
                // 首次转投诉队列时间
                if (factAssign == 4220 && isFirstTransferComplaintQueueTimeFlag && idx > 1) {
                    isFirstTransferComplaintQueueTimeFlag = false;
                    firstTransferComplaintQueueTime = ticketOperations.get(idx - 1).get("operate_time").asText();
                }

                // 首次转1.5线的时间
                if (operatorType == 2) {
                    if (StringUtils.isNotEmpty(closeTime) && !closeTime.equals("1970-01-01 00:00:00")) {
                        if (operateTime.compareTo(closeTime) <= 0 && !operatorName.equalsIgnoreCase("SYSTEM")) {
                            // 最后一次客服处理人
                            lastOperator = operatorName;
                            if (operatorPost == 3) {
                                secondLineOperators.add(operatorName);
                                secondLineOperatorCompanyIds.put(operatorName, operatorCompanyId);
                                // 一点五线服务最终商处理人
                                if (post == 3 && companyIds.contains(operatorCompanyId)) {
                                    secondLineLastCompanyOperator = operatorName;
                                    secondLineLastCompanyOperatorCompanyId = operatorCompanyId;
                                }
                            }
                        }
                    } else {
                        if (!operatorName.equalsIgnoreCase("SYSTEM")) {
                            lastOperator = operatorName;
                            if (operatorPost == 3) {
                                secondLineOperators.add(operatorName);
                                secondLineOperatorCompanyIds.put(operatorName, operatorCompanyId);
                                if (post == 3 && companyIds.contains(operatorCompanyId)) {
                                    secondLineLastCompanyOperator = operatorName;
                                    secondLineLastCompanyOperatorCompanyId = operatorCompanyId;
                                }
                            }
                        }
                    }

                }

                // 计算首次转产研的时间
                if (operationType == 5 || operationType == 3) {
                    if (firstTransferProductionResearchFlag && targetPost == 5) {
                        firstTransferProductionResearchFlag = false;
                        firstTransferProductionResearchTime = operateTime;
                    }
                }

                if (operationType == 5) {
                    switch (targetPost) {
                        case 2:
                            isTransferFirstLine = 1;
                            transferFirstLineTimes += 1;
                            transferFirstLineQueue = factAssign;
                            transferFirstLineTime = operateTime;
                            if (!operatorName.equalsIgnoreCase("SYSTEM")) {
                                transferFirstLineStaff = operatorName;
                            }
                            break;

                        case 3:
                            if (StringUtils.isEmpty(firstTransferSecondLineTime)) {
                                firstTransferSecondLineTime = operateTime;
                            }
                            transferSecondLineTimes += 1;
                            if (firstSecondLineAssign < 0) {
                                firstSecondLineAssign = nextAssign;
                            }
                            if (isFirstTransferSecondLineFlag && operatorType == 2
                                    && !operatorName.equalsIgnoreCase("SYSTEM")) {
                                isFirstTransferSecondLineFlag = false;
                                firstTransferSecondLineStaff = operatorName;
                            }
                            if (isFirstSecondLineOperatorFlag1 && !nextOperatorName.equalsIgnoreCase("SYSTEM")) {
                                secondLineFirstFactTime = operateTime;
                                secondLineFirstFactAssign = factAssign;
                                isFirstSecondLineOperatorFlag1 = false;
                                secondLineFirstOperator = nextOperatorName;
                                operatorTreeMap.put(operateTime, new HashMap<String, Integer>() {{
                                    put(nextOperatorName, nextOperatorCompanyId);
                                }});

                                if (isFirstSecondLineOperatorFlag3) {
                                    isFirstSecondLineOperatorFlag3 = false;
                                }
                            }
                            break;
                        case 4:
                            transferOperationTimes += 1;
                            if (isFirstTransferOperationFlag && !operatorName.equalsIgnoreCase("SYSTEM")) {
                                isFirstTransferOperationFlag = false;
                                firstTransferOperationStaffPost = post;
                                firstTransferOperationTime = operateTime;
                                firstTransferOperationStaff = operatorName;
                            }
                            break;
                        case 5:
                            transferProductionResearchTimes += 1;
                            if (isFirstTransferProductionResearchFlag) {
                                isFirstTransferProductionResearchFlag = false;
                                firstTransferProductionResearchStaffPost = post;
                            }
                            break;
                        case 11:
                            transferVerticalProductionResearchTimes += 1;
                            if (isFirstTransferVerticalProductionResearchFlag) {
                                isFirstTransferVerticalProductionResearchFlag = false;
                                fstTransVertProductionResearchStaffPost = post;
                            }
                            break;
                        default:
                            break;
                    }
                }

                if (operationType == 3) {
                    switch (targetPost) {
                        case 3:
                            if (preOperation != null && isFirstTransferSecondLineFlag && operatorType == 2) {
                                isFirstTransferSecondLineFlag = false;
                                String preOperator = preOperation.get("operator_name").asText();
                                String preNextOperator = preOperation.get("next_operator_name").asText();
                                firstTransferSecondLineStaff =
                                        preOperator.equalsIgnoreCase("SYSTEM") ? preOperator : preNextOperator;
                            }

                            if (StringUtils.isEmpty(secondLineFirstProcessingTime)) {
                                secondLineFirstProcessingTime = operateTime;
                            }

                            if (isFirstSecondLineOperatorFlag2 && !operatorName.equalsIgnoreCase("SYSTEM")) {
                                secondLineFirstFactTime = operateTime;
                                secondLineFirstFactAssign = factAssign;
                                secondLineFirstOperator = operatorName;
                                isFirstSecondLineOperatorFlag2 = false;
                                operatorTreeMap.put(operateTime, new HashMap<String, Integer>() {{
                                    put(nextOperatorName, nextOperatorCompanyId);
                                }});
                            }
                            break;
                        case 4:
                            transferOperationTimes += 1;
                            if (isFirstTransferOperationFlag) {
                                isFirstTransferOperationFlag = false;
                                firstTransferOperationTime = operateTime;
                            }
                            break;
                        default:
                            break;
                    }
                }

                if (operationType == 6) { // 派单
                    if (operatorType == 3) { // 系统
                        if (targetPost == 3) {
                            if (!isFirstSecondLineOperatorFlag3 && isFirstSecondLineOperatorFlag1) {
                                secondLineFirstFactTime = operateTime;
                                secondLineFirstFactAssign = factAssign;
                                isFirstSecondLineOperatorFlag1 = false;
                                secondLineFirstOperator = nextOperatorName;
                                operatorTreeMap.put(operateTime, new HashMap<String, Integer>() {{
                                    put(nextOperatorName, nextOperatorCompanyId);
                                }});
                            }
                        }
                    }
                    if (factAssign == 808 && firstSecondLineAssign < 0) {
                        firstSecondLineAssign = factAssign;
                    }
                }

                if (StringUtils.isNotEmpty(secondLineFirstOperator)) {
                    if (StringUtils.isEmpty(secondLineFirstProcessingTime)
                            && (targetStatus == 22 || operationType == 61)
                            && secondLineFirstOperator.equals(operatorName)) {
                        secondLineFirstProcessingTime = operateTime;
                    }

                    if (operationType != 2 && isFirstSecondLineFactFlag) {
                        if ((operationType == 22 || operationType == 61) || (operationType == 3 && targetPost != 3)) {
                            firstSecondLineFactAssign = -1;
                            secondLineFirstOperator = "";
                            secondLineFirstFactTime = "";
                            isFirstSecondLineOperatorFlag1 = true;
                            isFirstSecondLineOperatorFlag2 = true;
                        } else if (secondLineFirstOperator.equals(operatorName)) {
                            isFirstSecondLineFactFlag = false;
                            firstSecondLineFactAssign = secondLineFirstFactAssign;
                            firstSecondLineFactOperator = secondLineFirstOperator;
                            firstSecondLineServiceTime = secondLineFirstFactTime;
                        }
                    }

                    // 计算1.5线首次响应时长
                    if (!isFirstSecondLineFactFlag && StringUtils.isEmpty(firstSecondLineServiceEndTime)) {
                        if (operatorType == 2) {
                            if (serviceChannel.equals("27") && StringUtils.isNotEmpty(innerReply)) {
                                if (operationTypeInExcludeList.contains(operationType) && post == 3) {
                                    firstSecondLineServiceEndTime = operateTime;
                                }
                            } else if (post == 3 || operatorPost == 3) {
                                if (contentIsNotEmpty(externReply) || operationType == 44) {
                                    firstSecondLineServiceEndTime = operateTime;
                                }
                            }
                        }
                    }

                    if (StringUtils.isNotEmpty(firstSecondLineServiceEndTime)) {
                        if (StringUtils.isNotEmpty(firstSecondLineServiceTime)) {
                            firstSecondLineResponseDuration = (int) timeDelta(firstSecondLineServiceTime,
                                    firstSecondLineServiceEndTime);
                        }
                    }
                }

                // 获取处理结果
                if (operatorType == 2 && StringUtils.isNotEmpty(innerReply)) {
                    if (factAssign == 4220 || serviceChannel.equals("38")) {
                        measures = innerReply;
                    }
                }

                // 是否拒单
                if (operationType == 22 || operationType == 61) {
                    switch (post) {
                        case 3:
                            if (isDeniedBySecondLine != 1 && operatorType != 6) {
                                isDeniedBySecondLine = 1;
                            }
                            secondLineRefuseTimes += 1;
                            secondLineLastRefuser = operatorName;
                            break;
                        case 4:
                            isDeniedByOperation = 1;
                            break;
                        case 5:
                            isDeniedByProductionResearch = 1;
                            break;
                        default:
                            break;
                    }
                }

                if (!operatorTreeMap.isEmpty()) {
                    // 1.5线首次接单人
                    firstSecondLineOperator = operatorTreeMap.get(operatorTreeMap.firstKey()).keySet().stream()
                            .findFirst().orElse("");

                    //1.5线首次接单人所属公司
                    firstSecondLineOperatorCompanyId = operatorTreeMap.get(operatorTreeMap.firstKey()).values()
                            .stream()
                            .findFirst().orElse(-1);
                }

                if (operatorPost == 4 && operationType != 16) {
                    lastOperationFactAssign = factAssign;
                } else if ((operatorPost == 5 || operatorPost == 0) && operationType != 16) {
                    lastProductionResearchFactAssign = factAssign;
                }

                // 计算热线转单相关时长
                if (serviceChannel.equals("2")) {
                    if (isFirstAssignFlag && (operationType == 5 || operationType == 6)) {
                        hotlineFirstTransferTime = operateTime;
                        isFirstAssignFlag = false;
                        isFirstCheckFlag = true;
                    } else if (isFirstCheckFlag && operationType == 2) {
                        hotlineFirstTransferResponseTime = operateTime;
                        isFirstCheckFlag = false;
                    }
                }
            }

            if (!secondLineOperatorCompanyIds.isEmpty()) {
                if (secondLineOperatorCompanyIds.values().stream().anyMatch(e -> e == 0 || e == 217)) {
                    isTransferInnerStaffInSecondLine = 1; // 1.5线是否转内部员工
                }

                // 0 和 217都对应于腾讯云，将其统一转换为0
                if (secondLineOperatorCompanyIds.values().stream()
                        .map(x -> x == 217 ? 0 : x)
                        .filter(x -> x != 0)
                        .distinct()
                        .count() > 1) {
                    isTransferCompanyInSecondLine = 1;
                }
            }

            if (!operatorTreeMap.isEmpty()) {
                // 1.5线首次接单人
                firstSecondLineOperator =
                        operatorTreeMap
                                .get(operatorTreeMap.firstKey())
                                .keySet()
                                .stream()
                                .findFirst()
                                .orElse("");

                //1.5线首次接单人所属公司
                firstSecondLineOperatorCompanyId =
                        operatorTreeMap
                                .get(operatorTreeMap.firstKey())
                                .values()
                                .stream()
                                .findFirst()
                                .orElse(-1);
            }

            if (!ticketSolvedTime.equals("1970-01-01 00:00:00") && StringUtils.isNotEmpty(firstSecondLineServiceTime)) {
                secondLineSolvedDuration = (int) timeDelta(firstSecondLineServiceTime, ticketSolvedTime);
            }

            if (!hotlineFirstTransferTime.equals("1970-01-01 00:00:00") && !closeTime.equals("1970-01-01 00:00:00")) {
                hotlineFirstTransferDealDuration = (int) timeDelta(closeTime, hotlineFirstTransferTime);
            }

            if (!hotlineFirstTransferResponseTime.equals("1970-01-01 00:00:00")
                    && !hotlineFirstTransferTime.equals("1970-01-01 00:00:00")) {
                hotlineFirstTransferResponseDuration =
                        (int) timeDelta(hotlineFirstTransferResponseTime, hotlineFirstTransferTime);
            }

            // 提取一线客服操作信息
            if (lastFirstLineOperation != null) {
                firstLineLastOperator = lastFirstLineOperation.get("operator_name").asText();
                firstLineLastOperatorCompanyId = lastFirstLineOperation.get("operator_company_id").asInt();
                firstLineLastOperatorFactAssign = lastFirstLineOperation.get("fact_assign").asInt();
            }

            // 提取1.5线客服操作信息
            if (lastSecondLineOperation != null) {
                currentSecondLineOperator = lastSecondLineOperation.get("operator_name").asText();
                currentSecondLineOperatorCompanyId = lastSecondLineOperation.get("operator_company_id").asInt();
            }

            // 提取最后处理人信息
            if (lastOperatorOption != null) {
                lastOperatorNew = lastOperatorOption.get("operator_name").asText();
                lastOperatorPost = lastOperatorOption.get("operator_post").asInt();
            }

            row.setField("measures", measures);
            row.setField("operation_handler", operationHandler);
            row.setField("last_customer_operator", lastOperator);
            row.setField("is_transfer_first_line", isTransferFirstLine);
            row.setField("is_denied_by_operation", isDeniedByOperation);
            row.setField("is_denied_by_second_line", isDeniedBySecondLine);
            row.setField("second_line_refuse_times", secondLineRefuseTimes);
            row.setField("transfer_first_line_time", transferFirstLineTime);
            row.setField("first_second_line_assign", firstSecondLineAssign);
            row.setField("second_line_last_refuser", secondLineLastRefuser);
            row.setField("transfer_operation_times", transferOperationTimes);
            row.setField("transfer_first_line_queue", transferFirstLineQueue);
            row.setField("transfer_first_line_staff", transferFirstLineStaff);
            row.setField("transfer_first_line_times", transferFirstLineTimes);
            row.setField("first_second_line_operator", firstSecondLineOperator);
            row.setField("transfer_second_line_times", transferSecondLineTimes);
            row.setField("last_operation_fact_assign", lastOperationFactAssign);
            row.setField("second_line_solved_duration", secondLineSolvedDuration);
            row.setField("current_first_line_operator", currentFirstLineOperator);
            row.setField("operation_handler_company_id", operationHandlerCompanyId);
            row.setField("current_second_line_operator", currentSecondLineOperator);
            row.setField("first_second_line_fact_assign", firstSecondLineFactAssign);
            row.setField("first_transfer_operation_time", firstTransferOperationTime);
            row.setField("first_second_line_service_time", firstSecondLineServiceTime);
            row.setField("transfer_type", String.join("->", transferPostList));
            row.setField("hotline_first_transfer_line_time", hotlineFirstTransferTime);
            row.setField("first_transfer_operation_staff", firstTransferOperationStaff);
            row.setField("first_second_line_fact_operator", firstSecondLineFactOperator);
            row.setField("current_second_line_fact_assign", currentSecondLineFactAssign);
            row.setField("first_transfer_second_line_staff", firstTransferSecondLineStaff);
            row.setField("is_denied_by_production_research", isDeniedByProductionResearch);
            row.setField("second_line_last_company_operator", secondLineLastCompanyOperator);
            row.setField("is_transfer_company_in_second_line", isTransferCompanyInSecondLine);
            row.setField("transfer_production_research_times", transferProductionResearchTimes);
            row.setField("first_transfer_complaint_queue_time", firstTransferComplaintQueueTime);
            row.setField("first_transfer_operation_staff_post", firstTransferOperationStaffPost);
            row.setField("first_second_line_response_duration", firstSecondLineResponseDuration);
            row.setField("second_line_operators", mapper.writeValueAsString(secondLineOperators));
            row.setField("hotline_first_transfer_response_time", hotlineFirstTransferResponseTime);
            row.setField("last_production_research_fact_assign", lastProductionResearchFactAssign);
            row.setField("hotline_first_transfer_deal_duration", hotlineFirstTransferDealDuration);
            row.setField("first_second_line_operator_company_id", firstSecondLineOperatorCompanyId);
            row.setField("is_transfer_inner_staff_in_second_line", isTransferInnerStaffInSecondLine);
            row.setField("current_second_line_operator_company_id", currentSecondLineOperatorCompanyId);
            row.setField("first_transfer_production_research_time", firstTransferProductionResearchTime);
            row.setField("hotline_first_transfer_response_duration", hotlineFirstTransferResponseDuration);
            row.setField("second_line_last_company_operator_company_id", secondLineLastCompanyOperatorCompanyId);
            row.setField("transfer_vertical_production_research_times", transferVerticalProductionResearchTimes);
            row.setField("first_transfer_production_research_staff_post", firstTransferProductionResearchStaffPost);
            row.setField("second_line_operator_company_ids", mapper.writeValueAsString(secondLineOperatorCompanyIds));
            row.setField("first_transfer_vert_production_research_staff_post", fstTransVertProductionResearchStaffPost);
            row.setField("first_line_last_operator", firstLineLastOperator);
            row.setField("first_line_last_operator_company_id", firstLineLastOperatorCompanyId);
            row.setField("first_line_last_operator_fact_assign", firstLineLastOperatorFactAssign);
            row.setField("production_research_handler", productionResearchOperator);
            row.setField("first_transfer_second_line_time", firstTransferSecondLineTime);
            row.setField("is_transfer_tgw", isTransferTgw);
            row.setField("last_ofc_operator", lastOperatorNew);
            row.setField("last_ofc_operator_post", lastOperatorPost);
            ticketOperations = null;
            return row;
        }
    }

    /**
     * 工单响应信息处理逻辑，涉及的字段有：
     * average_reply_duration
     * longest_reply_duration
     * last_to_be_added_time
     * longest_reply_staff
     */
    public static class TicketReplyInfoMapFunction implements MapFunction<Row, Row> {

        @Override
        public Row map(Row row) throws Exception {
            int customerUrgeTimes = 0; // 客户催单次数
            int connectCustomerTimes = 0; // 外呼次数
            int averageReplyDuration = 0; // 平均回复时长
            int longestReplyDuration = 0; // 最长对外回复时间
            int hotlineFirstLineQueue = -1; // 首次转热线一线队列
            int isTransferHotlineFirstLine = 0; // 是否转热线一线
            String longestReplyStaff = ""; // 最长回复时间对应的人
            String lastToBeAddedTime = "1970-01-01 00:00:00"; // 最后操作'待客户补充'的时间

            // 辅助字段
            String operator = "";
            boolean customerAsk = false;
            boolean isTransferHotlineFirstLineFlag = false; // 是否转热线一线
            String customerAskTime = "1970-01-01 00:00:00";
            String customerServiceTime = "1970-01-01 00:00:00";
            IdentityHashMap<String, Integer> ticketResponseMap = new IdentityHashMap<>();

            // 服务通道
            String serviceChannel = Optional
                    .ofNullable(row.getFieldAs("service_channel"))
                    .orElse("")
                    .toString();

            // 计算过程
            List<JsonNode> ticketOperations = getTicketOperationList(row);
            for (JsonNode operation : ticketOperations) {
                final int nextAssign = operation.get("next_assign").asInt(); // 下一操作人
                final int operatorType = operation.get("operator_type").asInt(); // 操作人类型
                final int operationType = operation.get("operation_type").asInt(); // 操作类型
                final String operateTime = operation.get("operate_time").asText(); // 操作时间
                final String externReply = operation.get("extern_reply").asText(); // 外部回复内容
                final String operatorName = operation.get("operator_name").asText(); // 操作人姓名

                // service_channel ==2，若流水中存在next_assign字段不为空的记录，则为1，否则为0
                if (!isTransferHotlineFirstLineFlag && serviceChannel.equals("2") && nextAssign != 0) {
                    isTransferHotlineFirstLine = 1;
                    hotlineFirstLineQueue = nextAssign;
                    isTransferHotlineFirstLineFlag = true;

                }

                if (operationType == 11) {
                    lastToBeAddedTime = operateTime;
                }

                if (operationType == 4) {
                    customerUrgeTimes += 1;
                }

                if (operatorType == 2 && operationType == 44) {
                    connectCustomerTimes += 1;
                }

                if (!customerAsk && (operatorType == 1 || operationType == 1)) {
                    customerAskTime = operateTime;
                    customerAsk = true;
                }

                if (customerAsk) {
                    if (operatorType == 2 && contentIsNotEmpty(externReply) && operationType != 1) {
                        customerServiceTime = operateTime;
                        operator = operatorName;
                        customerAsk = false;
                    }
                }

                if (!customerAskTime.equals("1970-01-01 00:00:00")
                        && !customerServiceTime.equals("1970-01-01 00:00:00")) {
                    int responseDuration = (int) timeDelta(customerAskTime, customerServiceTime);
                    customerAskTime = "1970-01-01 00:00:00";
                    customerServiceTime = "1970-01-01 00:00:00";
                    ticketResponseMap.put(operator, responseDuration);
                }
            }

            if (!ticketResponseMap.isEmpty()) {
                averageReplyDuration = Optional.of(
                                ticketResponseMap
                                        .values()
                                        .stream()
                                        .mapToInt(Integer::intValue).sum() / ticketResponseMap.size())
                        .orElse(0);

                longestReplyDuration = ticketResponseMap.values().stream().max(Integer::compareTo).orElse(0);

                int finalLongestReplyDuration = longestReplyDuration;

                longestReplyStaff = ticketResponseMap
                        .entrySet()
                        .stream()
                        .filter(entry -> entry.getValue() == finalLongestReplyDuration)
                        .map(Entry::getKey)
                        .findFirst()
                        .orElseGet(""::toString);
            }

            row.setField("customer_urge_times", customerUrgeTimes);
            row.setField("longest_reply_staff", longestReplyStaff);
            row.setField("last_to_be_added_time", lastToBeAddedTime);
            row.setField("connect_customer_times", connectCustomerTimes);
            row.setField("average_reply_duration", averageReplyDuration);
            row.setField("longest_reply_duration", longestReplyDuration);
            row.setField("hotline_first_line_queue", hotlineFirstLineQueue);
            row.setField("is_transfer_hotline_first_line", isTransferHotlineFirstLine);
            ticketOperations = null;
            return row;
        }
    }
}