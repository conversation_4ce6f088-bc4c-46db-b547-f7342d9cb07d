package com.tencent.andata.etl.utils;

import com.tencent.andata.etl.lookup.TicketOperationQuery;
import com.tencent.andata.utils.DbResultUtils;
import com.tencent.andata.utils.lookup.jdbc.AbstractJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.Lazy;
import io.vavr.control.Try;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public class TicketOperationUtils {

    // 懒加载查询器，线程安全，使用通用工具方法
    private static final Lazy<TicketOperationQuery> ticketOperationQueryLazy =
            Lazy.of(() -> DbResultUtils.initializeQuery(
                    "cdc.database.mysql.work",
                    TicketOperationQuery::new,
                    DatabaseEnum.MYSQL,
                    AbstractJDBCLookupQuery::open // 使用初始化器打开连接
            ));

    public static JsonNode getTicketOperation(Long ticketId) {
        return Try.of(() -> ticketOperationQueryLazy.get().query(ticketId))
                .getOrNull();
    }
}