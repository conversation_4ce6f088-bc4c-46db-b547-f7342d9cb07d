package com.tencent.andata.etl.scene.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Scene implements Serializable {

    // 主键
    public long id;
    // 场景名称
    public String name;
    // 场景状态, 0-挂起, 1-启用
    public int status;
    // 删除状态, 1-删除, 0-正常
    public int disabled;
    // 父 ID, NULL 表示根节点, 其他值表示其父节点 ID
    public long parentId;
    // obs成本id
    public String obsId;
    //标签形态
    public String property;
    // 更新时间
    public Timestamp updateTime;
    // 更改时间
    public Timestamp modifyTime;
    // 子场景列表，保证线程安全
    public List<Scene> children = Collections.synchronizedList(new ArrayList<>());
}
