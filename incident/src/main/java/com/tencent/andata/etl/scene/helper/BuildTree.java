package com.tencent.andata.etl.scene.helper;

import com.tencent.andata.etl.scene.model.Scene;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class BuildTree {

    private static final ObjectMapper mapper = new ObjectMapper();

    /*
        构建归档多叉树
    */
    public static Scene buildTree(List<Scene> scenes) {
        Scene root = null;
        for (Scene scene : scenes) {
            if (StringUtils.equals(scene.getName().toUpperCase(), "__ROOT__")) {
                root = scene;
            } else {
                for (Scene parent : scenes) {
                    if (parent.getId() == scene.getParentId()) {
                        parent.getChildren().add(scene);
                        break;
                    }
                }
            }
        }
        return root;
    }

    /*
        递归输出归档层级
    */
    public static void displayTree(Scene scene, int level) {
        if (scene == null) {
            return;
        }
        for (int i = 0; i < level; i++) {
            System.out.print("--");
        }
        System.out.println(scene.getName());
        for (Scene child : scene.getChildren()) {
            displayTree(child, level + 1);
        }
    }

    /*
        归档展开（递归）
     */
    public static List<Scene> flattenScene(Scene scene) {
        List<Scene> flattenedScenes = Collections.synchronizedList(new ArrayList<>());
        if (scene == null) {
            return flattenedScenes;
        }

        flattenedScenes.add(scene);
        Iterator<Scene> itr = scene.children.iterator();
        while (itr.hasNext()) {
            flattenedScenes.addAll(flattenScene(itr.next()));
        }

        return flattenedScenes;
    }

    public static Scene convertJsonStrToScene(String jsonStr) throws JsonProcessingException {
        JsonNode json = mapper.readValue(jsonStr, JsonNode.class);
        return Scene.builder()
                .id(json.get("id").asLong())
                .status(json.get("status").asInt())
                .disabled(json.get("disabled").asInt())
                .parentId(json.get("parent_id").asLong())
                .name(json.get("name").asText(""))
                .obsId(json.get("obs_id").asText(""))
                .property(json.get("property").asText(""))
                .children(Collections.synchronizedList(new ArrayList<>()))
                .updateTime(Timestamp.valueOf(json.get("update_time").asText("1970-01-01 00:00:00")))
                .modifyTime(Timestamp.valueOf(json.get("modify_time").asText("1970-01-01 00:00:00")))
                .build();
    }
}
