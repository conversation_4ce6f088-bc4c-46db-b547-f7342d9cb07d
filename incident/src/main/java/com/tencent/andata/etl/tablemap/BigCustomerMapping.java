package com.tencent.andata.etl.tablemap;

public class BigCustomerMapping {

    public static String mysqlTopCustomerTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"t029_big_customer_v2\",\n"
            + "        \"fTable\":\"mysql_source_t029_big_customer_v2\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_customer_channel\",\n"
            + "        \"fTable\":\"mysql_source_t_customer_channel\"\n"
            + "    }\n"
            + "]";



    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_rt_key_customer_information\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_rt_key_customer_information\",\n"
            + "        \"primaryKey\": \"uin,b_id\"\n"
            + "    }\n"
            + "]";
}