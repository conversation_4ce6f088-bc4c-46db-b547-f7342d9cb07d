package com.tencent.andata.etl.tablemap;

public class DwdIncidentTicketOperationOverseasMapping {
    public static String mysqlTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"t102_duty\",\n"
            + "        \"fTable\": \"mysql_source_t102_duty\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t201_ticket\",\n"
            + "        \"fTable\":\"mysql_source_t201_ticket\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t201_ticket_extra\",\n"
            + "        \"fTable\":\"mysql_source_t201_ticket_extra\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t204_ticket_operation_extra\",\n"
            + "        \"fTable\": \"mysql_source_t204_ticket_operation_extra\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t202_ticket_operation\",\n"
            + "        \"fTable\": \"mysql_source_t202_ticket_operation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t210_ticket_alarm_record\",\n"
            + "        \"fTable\": \"mysql_source_t210_ticket_alarm_record\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_t202_ticket_operation_new_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_operation_new\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket_extra\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_extra\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket_operation_extra\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_operation_extra\",\n"
            + "        \"primaryKey\": \"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket_operation\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_operation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_incident_ticket_alarm_record\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_incident_ticket_alarm_record\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_duty\",\n"
            + "        \"fTable\": \"iceberg_sink_dim_incident_duty\",\n"
            + "        \"primaryKey\": \"duty_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_operation\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_operation\",\n"
            + "        \"primaryKey\": \"operation_id,operate_time\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_extra\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_incident_ticket_extra\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_operation_extra\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_operation_extra\",\n"
            + "        \"primaryKey\": \"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_ticket_service_channel\",\n"
            + "        \"fTable\": \"iceberg_source_dim_incident_ticket_service_channel\",\n"
            + "        \"primaryKey\": \"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_ticket_status\",\n"
            + "        \"fTable\": \"iceberg_source_dim_incident_ticket_status\",\n"
            + "        \"primaryKey\": \"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_ticket_priority\",\n"
            + "        \"fTable\": \"iceberg_source_dim_incident_ticket_priority\",\n"
            + "        \"primaryKey\": \"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_service_scenes\",\n"
            + "        \"fTable\": \"iceberg_source_dim_service_scenes\",\n"
            + "        \"primaryKey\": \"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_ticket_unsatisfy_reason\",\n"
            + "        \"fTable\": \"iceberg_source_dim_incident_ticket_unsatisfy_reason\",\n"
            + "        \"primaryKey\": \"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_ticket_feedback_channel\",\n"
            + "        \"fTable\": \"iceberg_source_dim_incident_ticket_feedback_channel\",\n"
            + "        \"primaryKey\": \"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_ticket_source_channel\",\n"
            + "        \"fTable\": \"iceberg_source_dim_incident_ticket_source_channel\",\n"
            + "        \"primaryKey\": \"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_ticket_extern_status\",\n"
            + "        \"fTable\": \"iceberg_source_dim_incident_ticket_extern_status\",\n"
            + "        \"primaryKey\": \"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_incident_category\",\n"
            + "        \"fTable\": \"iceberg_source_dim_incident_category\",\n"
            + "        \"primaryKey\": \"dim_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_alarm_record\",\n"
            + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_alarm_record\",\n"
            + "        \"primaryKey\": \"id,create_time\"\n"
            + "    }\n"
            + "]";

    public static String mysqlAccountTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_users\",\n"
            + "        \"fTable\":\"mysql_source_t_users\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_companies\",\n"
            + "        \"fTable\":\"mysql_source_t_companies\"\n"
            + "    }\n"
            + "]";
}
