package com.tencent.andata.etl.tablemap;

public class DwmIncidentTicketAssistantMapping {
    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"dwd_incident_ticket_base_info\",\n"
            + "        \"fTable\": \"iceberg_source_dwd_incident_ticket_base_info\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_customer_staff_info\",\n"
            + "        \"fTable\": \"pg_source_dim_customer_staff_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_company_info\",\n"
            + "        \"fTable\": \"pg_source_dim_company_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_t_incident_ticket_scenes\",\n"
            + "        \"fTable\": \"pg_source_dim_t_incident_ticket_scenes\"\n"
            + "    }"
            + "]";
}
