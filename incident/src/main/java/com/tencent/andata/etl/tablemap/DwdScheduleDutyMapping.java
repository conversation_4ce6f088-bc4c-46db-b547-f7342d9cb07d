package com.tencent.andata.etl.tablemap;

public class DwdScheduleDutyMapping {

    public static String mysqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"t103_schedule\",\n"
            + "        \"fTable\":\"mysql_source_t103_schedule\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t104_schedule_duty_map\",\n"
            + "        \"fTable\":\"mysql_source_t104_schedule_duty_map\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t101_work_shift\",\n"
            + "        \"fTable\":\"mysql_source_t101_work_shift\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t102_duty\",\n"
            + "        \"fTable\":\"mysql_source_t102_duty\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t907_company_schedule\",\n"
            + "        \"fTable\":\"mysql_source_t907_company_schedule\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t906_company_work\",\n"
            + "        \"fTable\":\"mysql_source_t906_company_work\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t908_company_schedule_duties\",\n"
            + "        \"fTable\":\"mysql_source_t908_company_schedule_duties\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t903_company_user\",\n"
            + "        \"fTable\":\"mysql_source_t903_company_user\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwm_customer_service_schedule\",\n"
            + "        \"fTable\": \"pgsql_sink_customer_service_schedule\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwm_company_service_schedule\",\n"
            + "        \"fTable\": \"pgsql_sink_company_service_schedule\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_customer_service_schedule\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_customer_service_schedule\",\n"
            + "        \"primaryKey\":\"schedule_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_customer_service_schedule_duty_map\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_customer_service_schedule_duty_map\",\n"
            + "        \"primaryKey\":\"map_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_customer_service_work_shift\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_customer_service_work_shift\",\n"
            + "        \"primaryKey\":\"work_shift_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwm_customer_service_schedule\",\n"
            + "        \"fTable\":\"iceberg_sink_customer_service_schedule\",\n"
            + "        \"primaryKey\":\"value_of_primary_key,date\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_company_schedule\",\n"
            + "        \"fTable\":\"iceberg_sink_company_schedule\",\n"
            + "        \"primaryKey\":\"schedule_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_company_work\",\n"
            + "        \"fTable\":\"iceberg_sink_company_work\",\n"
            + "        \"primaryKey\":\"company_id,work_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_company_schedule_duties\",\n"
            + "        \"fTable\":\"iceberg_sink_company_schedule_duties\",\n"
            + "        \"primaryKey\":\"schedule_id,duty_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwm_company_service_schedule\",\n"
            + "        \"fTable\":\"iceberg_sink_company_service_schedule\",\n"
            + "        \"primaryKey\":\"value_of_primary_key,date\"\n"
            + "    }\n"
            + "]";

    public static String starRocksTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwm_customer_service_schedule\",\n"
            + "        \"fTable\":\"flink_starrocks_dwm_customer_service_schedule\",\n"
            + "        \"primaryKey\": \"value_of_primary_key,date\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"dwm_company_service_schedule\",\n"
            + "        \"fTable\":\"flink_starrocks_dwm_company_service_schedule\",\n"
            + "        \"primaryKey\": \"value_of_primary_key,date\"\n"
            + "    }\n"
            + "]";
}
