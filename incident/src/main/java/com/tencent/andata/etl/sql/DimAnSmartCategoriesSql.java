package com.tencent.andata.etl.sql;

import java.util.stream.Collectors;
import org.apache.flink.table.api.Schema;

public class DimAnSmartCategoriesSql {

    public static String querySql = ""
            + "SELECT \n"
            + "  id AS id,\n"
            + "  Category AS category,\n"
            + "  Alias AS alias,\n"
            + "  Creator AS creator,\n"
            + "  Operator AS operator,\n"
            + "  createdAt AS created_at,\n"
            + "  updatedAt AS updated_at,\n"
            + "  Product AS product,\n"
            + "  Source AS source,\n"
            + "  PageType AS page_type\n"
            + "FROM mysql_source_ansmart_categories /*+ OPTIONS('server-time-zone'='Asia/Shanghai') */";

    public static String insertIntoPg(String srcName, String dstName, Schema schema) {
        return String.format("INSERT INTO %s /*+ OPTIONS('sink.parallelism'='1') */ SELECT %s FROM %s",
                dstName,
                schema.getColumns()
                        .stream()
                        .map(s -> String.format("`%s`", s.getName()))
                        .collect(Collectors.joining(",")),
                srcName);
    }

    public static String insertIntoIceberg(String srcName, String dstName, Schema schema) {
        return String.format("INSERT INTO %s "
                        + "/*+ OPTIONS('sink.parallelism'='1', "
                        + "'equality-field-columns'='id', "
                        + "'upsert-enabled'='true') */ "
                        + "SELECT %s FROM %s",
                dstName,
                schema.getColumns()
                        .stream()
                        .map(s -> String.format("`%s`", s.getName()))
                        .collect(Collectors.joining(",")),
                srcName);
    }
}