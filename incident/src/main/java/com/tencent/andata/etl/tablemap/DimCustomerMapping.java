package com.tencent.andata.etl.tablemap;

public class DimCustomerMapping {

    public static String mysqlWorkTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"t008_customer\",\n"
            + "        \"fTable\":\"mysql_source_t008_customer\"\n"
            + "    }\n"
            + "]";


    public static String pgsqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_customer\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_customer\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"dim_customer\",\n"
            + "        \"fTable\": \"iceberg_sink_dim_customer\",\n"
            + "        \"primaryKey\": \"pk\"\n"
            + "    }\n"
            + "]";
}
