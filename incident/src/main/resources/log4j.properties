### è®¾ç½®logçº§å«ä¸ºDEBUG, å¹¶å°logåå«ååç»stdout, d, e
log4j.rootLogger=WARN, stdout, D, E
### stdoutç¸å³éç½® ###
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Threshold=INFO
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%C{1}.%M:%L] [%t] - %m%n
### Dç¸å³éç½®ï¼ è¾åºDEBUGåä»¥ä¸çº§å«çæ¥å¿å°logs/log.logæä»¶ ###
log4j.appender.D=org.apache.log4j.FileAppender
log4j.appender.D.File=./incident/src/main/resources/logs/log.log
log4j.appender.D.Append=true
log4j.appender.D.Threshold=DEBUG
log4j.appender.D.layout=org.apache.log4j.PatternLayout
log4j.appender.D.layout.ConversionPattern=[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%C{1}.%M:%L] [%t] - %m%n
### Eç¸å³éç½®ï¼ è¾åºERRORçº§å«çæ¥å¿å°logs/error.logæä»¶ ###
log4j.appender.E=org.apache.log4j.FileAppender
log4j.appender.E.File=./incident/src/main/resources/logs/error.log
log4j.appender.E.Append=true
log4j.appender.E.Threshold=ERROR
log4j.appender.E.layout=org.apache.log4j.PatternLayout
log4j.appender.E.layout.ConversionPattern=[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%C{1}.%M:%L] [%t] - %m%n
