# Andata RT 0.3 x Flink1.15

项目仍处在孵化阶段，暂时使用单仓模式管理代码，提升代码复用 & 抽象
后续可以考虑使用submodule的方式来进行组合

## 模块描述
base 基础信息，包括大客户信息、trtc大客户信息等基础公共信息
change 变更管理
common-tools 公共包，待抽出至内部源

## 如何从1.13升级至1.15
1. 在Oceanus上使用复制功能将1.13的任务复制一份为1.15的任务
2. 停止Oceanus上旧的任务
3. 使用 https://zhiyan.woa.com/operate/542/task/#/task/result/10261 进行迁移
4. 迁移后即可在1.15任务中看到savepoint，正常启动即可

❕❕只支持同集群内的迁移

❕❕在确认任务迁移完成、新的任务已经成功运行并生成了新的checkpoint/savepoint之前，不要删除旧的Oceanus任务，否则会将旧的Savepoint一并删除

❕❕CDC版本暂时回滚至2.2.1，待冲突问题解决升级至2.3.0

❕❕打包的时候会触发mvn test，但当前pom依赖可能有问题。如果打包存在问题，可以使用： mvn package -Dmaven.test.skip 来进行打包

但如果我想要重新运行单元测试呢？那就得把有问题的包改一下了。