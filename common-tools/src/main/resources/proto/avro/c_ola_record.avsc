{"namespace": "com.tencent.andata.struct.avro.dts", "type": "record", "name": "IncidentOlaRecord", "fields": [{"name": "id", "type": "long"}, {"name": "tenant_id", "type": "long"}, {"name": "incident_id", "type": "string"}, {"name": "alarm_type", "type": ["null", "int"], "default": null}, {"name": "alarm_level", "type": "int"}, {"name": "urge_times", "type": "long"}, {"name": "create_time", "type": "long"}, {"name": "alarm_receiver", "type": ["null", "string"], "default": null}, {"name": "ticket", "type": ["null", "string"], "default": null}, {"name": "alarm_title", "type": ["null", "string"], "default": null}, {"name": "next_receiver", "type": ["null", "string"], "default": null}]}