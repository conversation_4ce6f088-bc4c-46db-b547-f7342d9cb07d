{"namespace": "com.tencent.andata.struct.avro.dts", "type": "record", "name": "Incident", "fields": [{"name": "id", "type": "long"}, {"name": "tenant_id", "type": "long"}, {"name": "process_id", "type": "string"}, {"name": "incident_id", "type": "string"}, {"name": "title", "type": "string"}, {"name": "content", "type": ["null", "string"], "default": null}, {"name": "status", "type": "int"}, {"name": "priority", "type": "int"}, {"name": "category_id", "type": "int"}, {"name": "current_operator", "type": "string"}, {"name": "service_rate", "type": "int"}, {"name": "service_comment", "type": ["null", "string"], "default": null}, {"name": "solution", "type": ["null", "string"], "default": null}, {"name": "progress", "type": ["null", "string"], "default": null}, {"name": "customer_id", "type": "string"}, {"name": "operators", "type": ["null", "string"], "default": null}, {"name": "staff_group_id", "type": "int"}, {"name": "is_deleted", "type": ["null", "int"], "default": null}, {"name": "close_time", "type": "long"}, {"name": "closer", "type": "string"}, {"name": "close_type", "type": "int"}, {"name": "source", "type": "string"}, {"name": "creator", "type": "string"}, {"name": "updater", "type": "string"}, {"name": "create_time", "type": "long"}, {"name": "update_time", "type": "long"}, {"name": "should_assign_queue", "type": "int"}, {"name": "fact_assign_queue", "type": "int"}, {"name": "agent", "type": "string"}, {"name": "conversation_id", "type": "string"}]}