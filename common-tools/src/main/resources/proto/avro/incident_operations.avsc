{"namespace": "com.tencent.andata.struct.avro.dts", "type": "record", "name": "IncidentOperation", "fields": [{"name": "id", "type": "long"}, {"name": "tenant_id", "type": "long"}, {"name": "operation_id", "type": "string"}, {"name": "incident_id", "type": "string"}, {"name": "operation_type", "type": "int"}, {"name": "operator_type", "type": "int"}, {"name": "operator", "type": "string"}, {"name": "operate_time", "type": "long"}, {"name": "before_status", "type": "int"}, {"name": "after_status", "type": "int"}, {"name": "before_current_operator", "type": "string"}, {"name": "after_current_operator", "type": "string"}, {"name": "req_labels", "type": ["null", "string"], "default": null}, {"name": "params", "type": ["null", "string"], "default": null}, {"name": "before_incident", "type": ["null", "string"], "default": null}, {"name": "after_incident", "type": ["null", "string"], "default": null}]}