package com.tencent.andata.utils.operator.map;

import com.tencent.andata.utils.RowDataSplitCleanFunction;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.cdc.conf.builder.WriteTypeEnum;
import com.tencent.andata.utils.cdc.conf.fieldgetter.BaseFieldGetter;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.data.RowData;

import java.util.Map;

import org.apache.flink.table.types.logical.RowType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RowDataFieldSwapMap implements MapFunction<RowData, RowData> {
    protected static final Logger LOG = LoggerFactory.getLogger(RowDataFieldSwapMap.class);
    private BaseFieldGetter srcFieldGetter;

    private BaseFieldGetter dstFieldGetter;

    private Map<String, Integer> namePosMap;

    private Boolean isChangeLog;

    private final Boolean needChangePos;


    public RowDataFieldSwapMap(Schema src, Schema dst, WriteTypeEnum writeTypeEnum) {
        this.isChangeLog = writeTypeEnum.equals(WriteTypeEnum.CHANGE_LOG);
        RowType srcRowType = TableUtils.convertFlinkSchemaToRowType(src, true);
        RowType dstRowType = TableUtils.convertFlinkSchemaToRowType(dst, true);
        this.namePosMap = CDCUtils.convertNamePosMap(srcRowType);
        this.srcFieldGetter = BaseFieldGetter.builder().setRowType(srcRowType).build();
        this.dstFieldGetter = BaseFieldGetter.builder().setRowType(dstRowType).build();
        this.needChangePos = !this.namePosMap.equals(
                CDCUtils.convertNamePosMap(dstRowType)
        );
        LOG.info(String.format("Swapper init. \r\n"
                + "SRC RowType: %s\r\n"
                + "DST RowType: %s\r\n"
                + "Need Change Position: %s\r\n", srcRowType, dstRowType, this.needChangePos));

    }

    @Override
    public RowData map(RowData rowData) throws Exception {
        RowData retRowData = rowData;
        if (this.isChangeLog) {
            // 增加Operation相关字段
            retRowData = RowDataSplitCleanFunction
                    .changeLogMap(
                            rowData,
                            srcFieldGetter,
                            dstFieldGetter,
                            namePosMap
                    );
        } else if (this.needChangePos) {
            retRowData = RowDataSplitCleanFunction.map(
                    rowData,
                    this.srcFieldGetter,
                    this.dstFieldGetter,
                    this.namePosMap
            );
        }
        // 如果都没命中，那么就是不需要Swap，直接透传
        return retRowData;
    }
}