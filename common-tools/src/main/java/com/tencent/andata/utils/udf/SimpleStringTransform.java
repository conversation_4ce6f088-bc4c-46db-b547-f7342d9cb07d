package com.tencent.andata.utils.udf;

import org.apache.flink.table.functions.ScalarFunction;

public class SimpleStringTransform extends ScalarFunction {

    public SimpleStringTransform() {
    }


    /**
     * 字符串数据转换
     *
     * @param data        字符串数据
     * @param defaultData 默认字符串值
     * @return 校验后的数据
     */
    public String eval(String data, String defaultData) {
        return data != null ? data : defaultData;
    }

    /**
     * 字符串转数字
     *
     * @param data        数字字符串
     * @param defaultData 默认字符串
     * @return 转换后的数据
     */
    public Integer eval(String data, Integer defaultData) {
        try {
            return Integer.parseInt(data);
        } catch (Exception e) {
            return defaultData;
        }
    }
}