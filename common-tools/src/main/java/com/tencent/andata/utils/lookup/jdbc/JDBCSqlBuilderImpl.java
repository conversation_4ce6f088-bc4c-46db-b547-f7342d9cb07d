package com.tencent.andata.utils.lookup.jdbc;


import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.util.Preconditions;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class JDBCSqlBuilderImpl implements JDBCQuerySqlBuilder {
    public static enum OrderType {
        ASC, DESC;
    }

    private List<String> conditionKeyList;
    private String tableName;
    private Integer limit;
    private List<String> selectField;
    private Map<String, String> selectFieldWithAlias;
    private boolean selectAll;
    private List<String> orderByList;
    private OrderType orderType;
    private Map<String, Integer> conditionFieldIndexMap;
    private DatabaseEnum databaseEnum;

    public Map<String, String> getSelectFieldWithAlias() {
        return selectFieldWithAlias;
    }

    public JDBCSqlBuilderImpl databaseEnum(DatabaseEnum databaseEnum) {
        this.databaseEnum = databaseEnum;
        return this;
    }

    public JDBCSqlBuilderImpl tableName(String tableName) {
        this.tableName = tableName;
        return this;
    }

    public JDBCSqlBuilderImpl conditionKeyList(List<String> conditionKeyList) {
        this.conditionKeyList = conditionKeyList;
        return this;
    }

    public JDBCSqlBuilderImpl selectFieldWithAlias(Map<String, String> selectFieldWithAlias) {
        this.selectFieldWithAlias = selectFieldWithAlias;
        return this;
    }

    public JDBCSqlBuilderImpl limit(Integer limit) {
        this.limit = limit;
        return this;
    }

    public JDBCSqlBuilderImpl selectField(List<String> selectField) {
        this.selectField = selectField;
        this.selectAll = false;
        return this;
    }

    public JDBCSqlBuilderImpl selectAll() {
        this.selectAll = true;
        return this;
    }

    public JDBCSqlBuilderImpl orderByList(List<String> orderByList, OrderType orderType) {
        this.orderByList = orderByList;
        this.orderType = orderType;
        return this;
    }

    public static JDBCSqlBuilderImpl builder() {
        return new JDBCSqlBuilderImpl();
    }


    @Override
    public Map<String, Integer> conditionKeyIndexMap() {
        return this.conditionFieldIndexMap;
    }

    @Override
    public String build() {
        Preconditions.checkNotNull(this.tableName);
        Preconditions.checkArgument(this.selectAll || this.selectField != null || this.selectFieldWithAlias != null);
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        // select 语句
        if (this.selectAll) {
            // all
            sb.append(" * ");
        } else if (this.selectFieldWithAlias != null) {
            // select a as b
            List<String> aliasList = new ArrayList<>();
            switch (this.databaseEnum) {
                case MYSQL:
                    this.selectFieldWithAlias.forEach((k, v) -> aliasList.add(String.format(" `%s` as %s ", k, v)));
                    break;
                case PGSQL:
                    this.selectFieldWithAlias.forEach((k, v) -> aliasList.add(String.format(" \"%s\" as %s ", k, v)));
                    break;
                default:
                    this.selectFieldWithAlias.forEach((k, v) -> aliasList.add(String.format(" %s as %s ", k, v)));
            }
            final String selectStr = String.join(",", aliasList);
            sb.append(selectStr);
        } else {
            // select a
            final String selectStr = this.selectField.stream().map(a -> String.format(" %s ", a)).collect(Collectors.joining(","));
            sb.append(selectStr);
        }
        // sekect table
        sb.append(String.format(" from %s ", this.tableName));
        // where条件字段的索引映射，下游statement设置参数是需要
        // where a = ? and b = ? -> {a:1, b:2}
        // TODO 这里暂时只考虑了简单equal条件，后续如有复杂条件、不等于之类的，可以抽象成conditionBuilder
        this.conditionFieldIndexMap = new HashMap<>();
        if (this.conditionKeyList != null) {
            List<String> whereConditionList = new ArrayList<>();
            for (int index = 0; index < this.conditionKeyList.size(); index++) {
                String whereKey = this.conditionKeyList.get(index);
                switch (this.databaseEnum) {
                    case MYSQL:
                        whereConditionList.add(String.format( "`%s` = ?", whereKey));
                        break;
                    case PGSQL:
                        whereConditionList.add(String.format( "\"%s\" = ?", whereKey));
                        break;
                    default:
                        whereConditionList.add(String.format( "%s = ?", whereKey));
                }
                this.conditionFieldIndexMap.put(whereKey, index + 1);
            }
            sb.append(" where ");
            sb.append(String.join(" and ", whereConditionList));
        }
        // 排序
        if (this.orderByList != null) {
            final String orderStr = this.orderByList.stream().map(a -> String.format(" %s ", a)).collect(Collectors.joining(","));
            sb.append(" order by ");
            sb.append(orderStr);
            sb.append(this.orderType == OrderType.ASC ? " " : " desc ");
        }
        // limit
        if (this.limit != null) {
            sb.append(String.format(" limit %s ", this.limit));
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        final JDBCSqlBuilderImpl limit1 = JDBCSqlBuilderImpl.builder().tableName("test1").selectFieldWithAlias(new HashMap<String, String>() {{
            put("field5", "field55");
            put("field6", "field66");
        }});


        System.out.println(limit1.build());
        System.out.println(limit1.conditionFieldIndexMap);

    }
}
