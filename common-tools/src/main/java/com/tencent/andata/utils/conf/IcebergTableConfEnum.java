package com.tencent.andata.utils.conf;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

public enum IcebergTableConfEnum {
    WRITE_METADATA_DELETE_AFTER_COMMIT_ENABLE("write.metadata.delete-after-commit.enabled", "true"),
    WRITE_METADATA_PREVIOUS_VERSIONS_MAX("write.metadata.previous-versions-max", "50"),
    COMMIT_MANIFEST_MERGE_ENABLED("commit.manifest-merge.enabled", "true"),
    WRITE_PARQUET_BLOOM_FILTER_MAX_BYTES("write.parquet.bloom-filter-max-bytes", "102400"),
    WRITE_POS_DELETE_HAS_SAME_SEQ_WITH_REFS_ENABLED("write.pos-delete-has-same-seq-with-refs.enabled", "true"),
    UPSERT_WRITE_PARQUET_BLOOM_FILTER_ENABLE_COL("write.parquet.bloom-filter-enable.%s", "true"),
    UPSERT_WRITE_PARQUET_BLOOM_FILTER_NDV_COL("write.parquet.bloom-filter-ndv.%s", "100000");

    public final String name;
    public final String value;

    IcebergTableConfEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public static Map<String, String> getDefaultIcebergTableConfMap(List<String> bloomFilterColNames) {
        Map<String, String> properties = new HashMap<>();
        for (IcebergTableConfEnum icebergTableConfEnum : IcebergTableConfEnum.class.getEnumConstants()) {
            if (!icebergTableConfEnum.name().startsWith("UPSERT")) {
                properties.put(icebergTableConfEnum.name, icebergTableConfEnum.value);
            } else if (bloomFilterColNames != null && !bloomFilterColNames.isEmpty()) {
                properties.put(
                        String.format(icebergTableConfEnum.name, bloomFilterColNames.get(0)),
                        icebergTableConfEnum.value
                );
            }
        }
        return properties;
    }
}