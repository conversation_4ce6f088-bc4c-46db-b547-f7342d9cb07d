package com.tencent.andata.utils.ddl.strategy;

import java.util.HashMap;

public class HbaseTableBuilderStrategy extends BaseTableBuilderStrategy {

    private final String columnFamily;


    public HbaseTableBuilderStrategy(String hbaseTableName, String columnFamily, String zookeeperQuorum,
            String zookeeperZnodeParent) {

        this.columnFamily = columnFamily;
        this.tableProperties = new HashMap<String, String>() {{
            put("connector", "hbase-2.2");
            put("zookeeper.quorum", zookeeperQuorum);
            put("zookeeper.znode.parent", zookeeperZnodeParent);
            put("table-name", hbaseTableName);
        }};
    }

    @Override
    public String getSchemaString() {
        String template = ""
                + " (\n"
                + "  `rowkey` STRING,\n"
                + "  `%s` ROW<`data` STRING>,\n"
                + "  PRIMARY KEY(`rowkey`) NOT ENFORCED\n"
                + " )";

        return String.format(template, this.columnFamily);
    }


    public HbaseTableBuilderStrategy lookupAsync(String value) {
        this.tableProperties.put("lookup.async", value);
        return this;
    }

    public HbaseTableBuilderStrategy lookupCacheSize(String value) {
        this.tableProperties.put("lookup.cache.max-rows", value);
        return this;
    }

    public HbaseTableBuilderStrategy lookupCacheTtl(String value) {
        this.tableProperties.put("lookup.cache.ttl", value);
        return this;
    }

    public HbaseTableBuilderStrategy sinkBufferFlushSize(String value) {
        this.tableProperties.put("sink.buffer-flush.max-size", value);
        return this;
    }

    public HbaseTableBuilderStrategy sinkBufferFlushInterval(String value) {
        this.tableProperties.put("sink.buffer-flush.interval", value);
        return this;
    }

    public HbaseTableBuilderStrategy sinkParallelism(String value) {
        this.tableProperties.put("sink.parallelism", value);
        return this;
    }
}
