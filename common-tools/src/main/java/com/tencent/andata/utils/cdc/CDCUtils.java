package com.tencent.andata.utils.cdc;

import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.cdc.conf.constant.CDCChangeLog;
import com.tencent.andata.utils.conf.IcebergTableConfEnum;
import com.tencent.andata.utils.connector.jdbc.catalog.AbstractJdbcCatalog;
import com.tencent.andata.utils.connector.jdbc.catalog.JdbcCatalogUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.catalog.ObjectPath;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.LocalZonedTimestampType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.types.logical.VarCharType;
import org.apache.iceberg.PartitionSpec;
import org.apache.iceberg.Table;
import org.apache.iceberg.catalog.TableIdentifier;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Properties;

public class CDCUtils {

    public static ArrayList<Tuple2<RowData.FieldGetter, LogicalType>> convertRowTypeToFieldGetterTupleList(
            RowType rowType) {
        List<RowType.RowField> fields = rowType.getFields();

        ArrayList<Tuple2<RowData.FieldGetter, LogicalType>> retFieldGetterList = new ArrayList<>();
        for (int i = 0; i < fields.size(); ++i) {
            LogicalType logicalType = fields.get(i).getType();
            Tuple2<RowData.FieldGetter, LogicalType> tuple = new Tuple2<>();
            tuple.setFields(RowData.createFieldGetter(logicalType, i), logicalType);
            retFieldGetterList.add(tuple);
        }
        return retFieldGetterList;
    }

    public static Map<String, Integer> convertNamePosMap(
            RowType rowType) {
        List<RowType.RowField> fields = rowType.getFields();
        Map<String, Integer> namePosMap = new HashMap<>();
        for (int i = 0; i < fields.size(); ++i) {
            namePosMap.put(fields.get(i).getName(), i);
        }
        return namePosMap;
    }

    public static Table getIcebergTable(TableIdentifier tableIdentifier,
                                        IcebergCatalogReader icebergCatalogReader,
                                        Schema flinkSchema,
                                        List<String> pkList) {
        RowType rowType = TableUtils.convertFlinkSchemaToRowType(flinkSchema, true);
        return getIcebergTable(tableIdentifier,
                icebergCatalogReader,
                TableUtils.convertRowTypeToIcebergSchema(rowType),
                pkList,
                null);
    }

    public static Table getIcebergTable(TableIdentifier tableIdentifier,
                                        IcebergCatalogReader icebergCatalogReader,
                                        org.apache.iceberg.Schema schema,
                                        List<String> pkList,
                                        PartitionSpec partitionSpec) {
        Table table;
        try {
            table = icebergCatalogReader.getTableInstance(tableIdentifier);
        } catch (TableNotExistException e) {
            table =
                    icebergCatalogReader
                            .getCatalog()
                            .createTable(
                                    tableIdentifier,
                                    schema,
                                    partitionSpec == null ? PartitionSpec.unpartitioned() : partitionSpec,
                                    IcebergTableConfEnum.getDefaultIcebergTableConfMap(pkList));
        }
        return table;
    }

    public static Schema getRdbmsSchema(DatabaseConf databaseConf,
                                        com.tencent.andata.utils.TableIdentifier identifier,
                                        Properties properties) throws TableNotExistException {
        DatabaseEnum dbEnum = identifier.getDatabaseEnum();
        AbstractJdbcCatalog catalog = JdbcCatalogUtils.createCatalog(
                String.format("%s_catalog", dbEnum),
                databaseConf.dbName,
                databaseConf.userName,
                databaseConf.password,
                String.format("jdbc:%s://%s:%d/", dbEnum, databaseConf.dbHost, databaseConf.dbPort),
                dbEnum
        );
        if (properties == null) {
            return catalog
                    .getTable(new ObjectPath(databaseConf.dbName, identifier.getTableName()))
                    .getUnresolvedSchema();
        } else {
            return catalog
                    .getTable(new ObjectPath(databaseConf.dbName, identifier.getTableName()), properties)
                    .getUnresolvedSchema();
        }
    }

    /**
     * 获取数据库表Schema信息
     *
     * @param databaseConf
     * @param dbTableName
     * @param dbType
     * @param properties
     * @return
     * @throws TableNotExistException
     */
    public static Schema getRdbmsSchema(DatabaseConf databaseConf,
                                        String dbTableName,
                                        DatabaseEnum dbType,
                                        Properties properties)
            throws TableNotExistException {
        com.tencent.andata.utils.TableIdentifier tableIdentifier = new com.tencent.andata.utils.TableIdentifier(
                dbType, "", "", dbTableName
        );
        return getRdbmsSchema(databaseConf, tableIdentifier, properties);
    }

    public static RowType covertToChangelogRowType(RowType rowType) {
        List<RowType.RowField> changelogTableFields = new ArrayList<RowType.RowField>();
        changelogTableFields.add(new RowType.RowField(CDCChangeLog.OP, new VarCharType()));
        changelogTableFields.add(new RowType.RowField(CDCChangeLog.OP_TIME, new LocalZonedTimestampType()));
        changelogTableFields.addAll(rowType.getFields());
        return new RowType(changelogTableFields);
    }

    public static void setCheckpointConf(CheckpointConfig checkpointConfig) {
        checkpointConfig.setCheckpointInterval(600 * 1000L);
        checkpointConfig.setMinPauseBetweenCheckpoints(30 * 1000L);
        checkpointConfig.setMaxConcurrentCheckpoints(1);
        checkpointConfig.setCheckpointTimeout(120 * 1000L);
        checkpointConfig.setTolerableCheckpointFailureNumber(5);
    }
}