package com.tencent.andata.utils.udf;

import com.tencent.andata.utils.AsyncHttpClient;
import com.tencent.andata.utils.JSONUtils;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisFuture;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.async.RedisAsyncCommands;
import java.io.IOException;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.flink.types.Row;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class AsyncGetShortUrl extends RichAsyncFunction<Row, Row> {

    private static final Logger LOG = LoggerFactory.getLogger(AsyncGetShortUrl.class);
    private static CloseableHttpAsyncClient httpAsyncClient;
    private String api;
    private String url;
    private JSONUtils jsonUtils;
    private RedisClient redisClient;
    private RedisAsyncCommands<String, String> async;
    private StatefulRedisConnection<String, String> connection;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        LOG.info("start open ...");

        this.jsonUtils = new JSONUtils();

        // redis
        this.redisClient = RedisClient.create(url);
        this.connection = redisClient.connect();
        async = connection.async();

        httpAsyncClient = AsyncHttpClient.getHttpClient();
        httpAsyncClient.start();
        LOG.info("end open.");
    }


    @Override
    public void close() throws IOException {
        LOG.info("start close ...");
        connection.close();
        redisClient.shutdown();
        httpAsyncClient.close();
        LOG.info("end close.");
    }

    @Override
    public void timeout(Row input, ResultFuture<Row> resultFuture) throws Exception {
        super.timeout(input, resultFuture);
    }

    @Override
    public void asyncInvoke(Row row, ResultFuture<Row> resultFuture) {
        long ticketId = Long.parseLong(row.getFieldAs("ticket_id").toString());

        RedisFuture<String> redisFuture = async.get("dim:dwd_incident_ticket_short_url:" + ticketId);

        // async query and get result
        CompletableFuture.supplyAsync(() -> {
            try {
                return redisFuture.get();
            } catch (Exception e) {
                LOG.error("dim:dwd_incident_ticket_short_url: {}, AsyncGetRedisShortUrl error: {}", ticketId, e);
                return "";
            }
        }).thenAccept(result -> {
            if (StringUtils.isNotEmpty(result)) {
                // return result
                row.setField("short_url", result);
                resultFuture.complete(Collections.singleton(row));
            }
        });

        // 获取row中的长链接，并构造post请求参数
        JSONObject params = new JSONObject();
        params.put("link", row.<String>getFieldAs("url"));
        HttpPost postBody = AsyncHttpClient.doHttpPost(this.api, params.toString(), ContentType.APPLICATION_JSON);
        Future<HttpResponse> future = httpAsyncClient.execute(postBody, null);

        CompletableFuture.supplyAsync(() -> {
            // 用try包住，处理get不到值时的报错程序
            try {
                HttpResponse response = future.get();
                if (response.getStatusLine().getStatusCode() == 200) {
                    //拿出响应的实例对象
                    String content = EntityUtils.toString(response.getEntity(), "UTF-8");
                    ObjectNode resp = this.jsonUtils.getJSONObjectNodeByString(content);
                    String shortUrl = resp.get("shortlink").asText();

                    row.setField("short_url", shortUrl);
                    // 将短链接放入缓存
                    async.set("dim:dwd_incident_ticket_short_url:" + ticketId, shortUrl);
                }
                return row;
            } catch (Exception e) {
                LOG.error("ticket_id: {}, AsyncGetShortUrl error: {}", ticketId, e);
                return row;
            }
        }).thenAcceptAsync((Row result) -> resultFuture.complete(Collections.singleton(result)));
    }
}
