package com.tencent.andata.utils.connector.jdbc.catalog;

import com.tencent.andata.utils.struct.DatabaseEnum;

import static org.apache.flink.util.Preconditions.checkArgument;


public class JdbcCatalogUtils {

    /**
     * 验证jdbc url
     *
     * @param url url
     */
    public static void validateJdbcUrl(String url) {
        String[] parts = url.trim().split("\\/+");

        checkArgument(parts.length == 2);
    }


    public static AbstractJdbcCatalog createCatalog(
            String catalogName,
            String defaultDatabase,
            String username,
            String pwd,
            String baseUrl,
            DatabaseEnum databaseEnum) {
        switch (databaseEnum) {
            case MYSQL:
            case ROCKS:
                return new MySqlCatalog(catalogName, defaultDatabase, username, pwd, baseUrl);
            case PGSQL:
                return new PostgresCatalog(catalogName, defaultDatabase, username, pwd, baseUrl);
            default:
                throw new UnsupportedOperationException(
                        String.format("Catalog for '%s' is not supported yet.", databaseEnum));
        }
    }
}
