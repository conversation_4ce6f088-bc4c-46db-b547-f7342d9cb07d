package com.tencent.andata.utils.rowdata;

import com.tencent.andata.utils.TableIdentifier;
import java.io.Serializable;
import lombok.Getter;

/**
 * 嵌入在RowData里的配置，可自定义添加，随着RowData流动
 */
@Getter
public class MessageRowDataAttr implements Serializable {
    // TODO：这里后面集成PK信息进来
    private TableIdentifier src;

    private TableIdentifier srcTable;
    private TableIdentifier dstTable;

    public MessageRowDataAttr() {
    }

    public MessageRowDataAttr(TableIdentifier src) {
        this.src = src;
    }

    public TableIdentifier getSrc() {
        return src;
    }

    public TableIdentifier getSrcTable() {
        return this.getSrc();
    }


    public void setSrc(TableIdentifier src) {
        this.src = src;
    }

    public void setSrcTable(TableIdentifier srcTable) {
        this.setSrc(srcTable);
    }

    @Override
    public String toString() {
        return "MessageRowDataAttr{"
                + "srcTable=" + src
                + '}';
    }
}
