package com.tencent.andata.utils.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class UnicodeToString extends ScalarFunction {


    public String eval(String data) {
        if (StringUtils.isEmpty(data)) {
            return "";
        }
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(data);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            data = data.replace(matcher.group(1), ch + "");
        }
        return data;
    }
}
