package com.tencent.andata.utils;

import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

public class HTTPUtils {

    /**
     * 调用Get请求
     *
     * @param url 链接
     * @return 请求内容
     * @throws Exception
     */
    public static HttpResponse getHttp(String url) throws Exception {
        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建http GET请求
        HttpGet httpGet = new HttpGet(url);
        // 执行请求
        HttpResponse response = httpclient.execute(httpGet);
        return response;
    }
}
