package com.tencent.andata.utils.udf;


import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import lombok.Builder;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.concurrent.ExecutorThreadFactory;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.AsyncConnection;
import org.apache.hadoop.hbase.client.AsyncTable;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.client.ScanResultConsumer;
import org.apache.hadoop.hbase.filter.PrefixFilter;
import org.apache.hadoop.hbase.util.Threads;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder

public class AsyncScanHbase extends RichAsyncFunction<Tuple3<Row, String, String>, Row> {

    private static final Logger LOG = LoggerFactory.getLogger(AsyncScanHbase.class);
    private static final int THREAD_POOL_SIZE = 16;
    private static final ObjectMapper MAPPER = new ObjectMapper()
            .setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
    private final String qualifier;
    private final String hTableName;
    private final String columnFamily;
    private final String hbaseZookeeperQuorum;
    private final String zookeeperZnodeParent;
    private transient AsyncConnection asyncConnection;
    private transient AsyncTable<ScanResultConsumer> table;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        LOG.info("start open ...");
        final ExecutorService threadPool = Executors.newFixedThreadPool(THREAD_POOL_SIZE,
                new ExecutorThreadFactory("hbase-async-lookup-worker", Threads.LOGGING_EXCEPTION_HANDLER));

        org.apache.hadoop.conf.Configuration conf = HBaseConfiguration.create();
        conf.set("hbase.zookeeper.quorum", this.hbaseZookeeperQuorum);
        conf.set("zookeeper.znode.parent", this.zookeeperZnodeParent);

        CompletableFuture<AsyncConnection> asyncConnectionFuture = ConnectionFactory.createAsyncConnection(conf);

        // wait for the async connection to be ready
        try {
            asyncConnection = asyncConnectionFuture.get();
            table = asyncConnection.getTable(TableName.valueOf(hTableName), threadPool);
        } catch (InterruptedException | ExecutionException e) {
            LOG.error("Exception while creating connection to HBase.", e);
            throw new RuntimeException("Cannot create connection to HBase.", e);
        }
        LOG.info("end open.");
    }

    @Override
    public void asyncInvoke(Tuple3<Row, String, String> inputData, ResultFuture<Row> output) {
        Scan scan = new Scan();
        scan.setCaching(1000);
        scan.setBatch(1000);

        // 解析输入数据，获取rowkey前缀，以及需要增加的列
        Row row = inputData.f0;
        String prefix = row.getFieldAs(inputData.f1).toString();

        scan.withStartRow(prefix.getBytes());
        scan.setFilter(new PrefixFilter(prefix.getBytes()));
        scan.addColumn(this.columnFamily.getBytes(), this.qualifier.getBytes());

        ArrayList<JsonNode> jsonNodes = new ArrayList<>();
        CompletableFuture<List<Result>> completableFuture = table.scanAll(scan);
        completableFuture.thenAcceptAsync(consumer(results -> {
            results.forEach(r -> Arrays.stream(r.rawCells()).forEach(consumer(cell -> {
                jsonNodes.add(MAPPER.readTree(CellUtil.cloneValue(cell)));
            })));

            row.setField(inputData.f2, MAPPER.writeValueAsString(jsonNodes));
            output.complete(Collections.singleton(row));
        })).exceptionally(throwable -> {
            LOG.error("Exception while executing async scan：" + throwable.getMessage());
            output.completeExceptionally(throwable);
            return null;
        });
    }

    @Override
    public void close() {
        LOG.info("start close ...");
        if (null != table) {
            table = null;
        }
        if (null != asyncConnection) {
            try {
                asyncConnection.close();
                asyncConnection = null;
            } catch (IOException e) {
                // ignore exception when close.
                LOG.warn("exception when close connection", e);
            }
        }
        LOG.info("end close.");
    }
}
