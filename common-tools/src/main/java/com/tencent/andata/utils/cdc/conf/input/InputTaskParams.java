package com.tencent.andata.utils.cdc.conf.input;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

public class InputTaskParams {
    @JsonAlias({"dbName", "srcDBName"})
    public String srcDBName;
    @JsonAlias({"dbSchemaName", "srcDBSchemaName"})
    public String srcDBSchemaName;
    @JsonAlias({"dbTableName", "srcDBTableName"})
    public String srcDBTableName;
    @JsonAlias({"dbType", "srcDBType"})
    public DatabaseEnum srcDBType;
    @JsonAlias({"hiveName", "dstDBName"})
    public String dstDBName;
    @JsonAlias({"hiveTableName", "dstTableName"})
    public String dstTableName;
    @JsonAlias({"sinkType", "dstDBType"})
    public DatabaseEnum dstDBType;
    @JsonAlias({"startMode", "startMode"})
    // LAST:只同步增量 ALL:全量扫描后同步增量（默认）
    public String startMode;

    public String[] primaryKeys;
    public String[] jdbcConf;
    public String isChangeLog;
    @JsonProperty("partitionArgs")
    public InputPartitionArgs[] inputPartitionArgs;

    public InputPartitionArgs[] getPartitionSpec() {
        return inputPartitionArgs;
    }

    public void setPartitionSpec(InputPartitionArgs[] inputPartitionArgs) {
        this.inputPartitionArgs = inputPartitionArgs;
    }

    /***
     * fromJson .
     * @param json .
     * @return .
     * @throws IOException .
     * @throws IllegalAccessException .
     * @throws IllegalArgumentException .
     */
    public static InputTaskParams[] fromJson(String json)
            throws IOException, IllegalAccessException, IllegalArgumentException {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 解析Json数据生成InputTaskParams
        // TODO：这里对数据必填进行校验，后面最好使用bean validate api来做这个事情，先这样
        // From xxx (required) with xxx(optional) To xxx (required) with xxx(optional) IN xxx mode (optional)
        final List<String> notNullField = Arrays.asList(
                "dbName", "dbTableName", "dbType", "hiveName", "hiveTableName", "dstDBType"
        );
        InputTaskParams[] inputParams = objectMapper.readValue(json, InputTaskParams[].class);
        for (Field field : InputTaskParams.class.getDeclaredFields()) {
            if (notNullField.contains(field.getName())) {
                for (InputTaskParams inputParam : inputParams) {
                    // 验证某列不为空
                    if (field.get(inputParam) == null) {
                        throw new IllegalArgumentException(
                                String.format(
                                        "fields %s should not be null. current conf: %s",
                                        notNullField, inputParam
                                )
                        );
                    }
                }
            }
        }
        return inputParams;
    }

    @Override
    public String toString() {
        return "InputTaskParams{" 
            +                 "dbName='" + srcDBName + '\'' 
            +                 ", dbSchemaName='" + srcDBSchemaName + '\'' 
            +                 ", dbTableName='" + srcDBTableName + '\'' 
            +                 ", dbType='" + srcDBType + '\'' 
            +                 ", hiveName='" + dstDBName + '\'' 
            +                 ", hiveTableName='" + dstTableName + '\'' 
            +                 ", primaryKeys=" + Arrays.toString(primaryKeys) 
            +                 ", jdbcConf=" + Arrays.toString(jdbcConf) 
            +                 ", isChangeLog='" + isChangeLog + '\'' 
            +                 ", sinkType='" + dstDBType + '\'' 
            +                 ", inputPartitionArgs=" + Arrays.toString(inputPartitionArgs) 
            +                 '}';
    }
}