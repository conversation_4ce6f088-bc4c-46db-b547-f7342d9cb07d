package com.tencent.andata.utils.lookup.jdbc;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

public interface JDBCQueryParser<IN, OUT> extends Serializable {

    public PreparedStatement setStatement(IN in, PreparedStatement statement, Map<String, Integer> conditionKeyIndexMap) throws SQLException;

    public OUT parseResultSet(ResultSet rs) throws SQLException;
}