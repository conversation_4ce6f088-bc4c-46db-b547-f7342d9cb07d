package com.tencent.andata.utils.ddl.strategy;

import com.tencent.andata.utils.struct.mq.kafka.KafkaConf;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.types.DataType;
import org.apache.flink.util.Preconditions;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class KafkaTableBuilderStrategy extends BaseTableBuilderStrategy {

    private final KafkaConf kafkaConf;
    private TableSchema tableSchema;
    private String primaryKey;

    public KafkaTableBuilderStrategy(KafkaConf kafkaConf) {
        this.kafkaConf = kafkaConf;
        this.tableProperties = new HashMap<String, String>() {{
            put("connector", "kafka");
            put("topic", kafkaConf.topics);
            put("properties.bootstrap.servers", kafkaConf.brokers);
            put("properties.group.id", kafkaConf.consumerGroup);
            // 默认从group offset消费
            put("scan.startup.mode", "group-offsets");
        }};
        // 将Conf中的参数设置到connector中
        for (Map.Entry<Object, Object> entry : kafkaConf.properties().entrySet()) {
            final String key = entry.getKey().toString();
            this.tableProperties.put(String.format("properties.%s", key), entry.getValue().toString());
        }
    }

    public KafkaTableBuilderStrategy schema(TableSchema schema) {
        this.tableSchema = schema;
        return this;
    }

    public KafkaTableBuilderStrategy primaryKey(String primaryKey) {
        this.tableProperties.put("connector", "upsert-kafka");
        this.primaryKey = primaryKey;
        return this;
    }

    public KafkaTableBuilderStrategy startEarliest() {
        this.tableProperties.put("scan.startup.mode", "earliest-offset");
        return this;
    }

    public KafkaTableBuilderStrategy startLatest() {
        this.tableProperties.put("scan.startup.mode", "latest-offset");
        return this;
    }

    public KafkaTableBuilderStrategy jsonValueFormat() {
        this.tableProperties.put("value.format", "json");
        return this;
    }

    public KafkaTableBuilderStrategy jsonKeyFormat() {
        this.tableProperties.put("key.format", "json");
        return this;
    }

    public KafkaTableBuilderStrategy ignoreParseError() {
        this.tableProperties.put("value.json.ignore-parse-errors", "true");
        return this;
    }

    public KafkaTableBuilderStrategy commitWhenCheckPoint() {
        this.tableProperties.put("properties.commit.offsets.on.checkpoint", "true");
        this.tableProperties.put("properties.enable.auto.commit", "false");
        return this;
    }

    public KafkaTableBuilderStrategy readCommitted() {
        this.tableProperties.put("properties.isolation.level", "read_committed");
        return this;
    }


    @Override
    public String getSchemaString() throws TableNotExistException {
        Preconditions.checkNotNull(tableSchema);
        // 获取字段名和字段类型
        final String[] fieldNames = tableSchema.getFieldNames();
        final String[] fieldTypes = Arrays
                .stream(tableSchema.getFieldDataTypes())
                .map(t -> t.getLogicalType().toString())
                .toArray(a -> new String[fieldNames.length]);
        // 拼接
        String schemaStr = IntStream.range(0, fieldNames.length)
                .boxed()
                .map(i -> String.format("%s %s", fieldNames[i], fieldTypes[i]))
                .collect(Collectors.joining(",\n"));
        // 拼接主键
        if (this.primaryKey != null) {
            schemaStr += String.format(",\n PRIMARY KEY (%s) NOT ENFORCED", this.primaryKey);
        }
        return String.format("(\n%s\n)", schemaStr);
    }
}
