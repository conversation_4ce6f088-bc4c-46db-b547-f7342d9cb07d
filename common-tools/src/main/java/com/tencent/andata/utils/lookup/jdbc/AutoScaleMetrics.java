package com.tencent.andata.utils.lookup.jdbc;

import java.io.Serializable;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Hikari连接池自动扩缩容监控指标类
 * 
 * 提供扩缩容操作的统计信息和性能指标
 * 
 * <AUTHOR>
 */
public class AutoScaleMetrics implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // 扩容统计
    private final AtomicLong scaleUpCount = new AtomicLong(0);
    private final AtomicLong lastScaleUpTime = new AtomicLong(0);
    private final AtomicLong totalScaleUpConnections = new AtomicLong(0);
    
    // 缩容统计
    private final AtomicLong scaleDownCount = new AtomicLong(0);
    private final AtomicLong lastScaleDownTime = new AtomicLong(0);
    private final AtomicLong totalScaleDownConnections = new AtomicLong(0);
    
    // 性能指标
    private final AtomicLong totalTickCount = new AtomicLong(0);
    private final AtomicLong errorTickCount = new AtomicLong(0);
    private final AtomicLong maxPoolSizeReached = new AtomicLong(0);
    private final AtomicLong minPoolSizeReached = new AtomicLong(0);
    
    // 利用率统计
    private volatile double lastUtilization = 0.0;
    private volatile double maxUtilization = 0.0;
    private volatile double minUtilization = 1.0;
    private final AtomicLong highUtilizationCount = new AtomicLong(0);
    private final AtomicLong lowUtilizationCount = new AtomicLong(0);
    
    // 等待线程统计
    private volatile int lastAwaitingThreads = 0;
    private volatile int maxAwaitingThreads = 0;
    private final AtomicLong awaitingThreadsDetectedCount = new AtomicLong(0);
    
    // 创建时间
    private final long createdTime = System.currentTimeMillis();
    
    /**
     * 记录扩容操作
     */
    public void recordScaleUp(int addedConnections) {
        scaleUpCount.incrementAndGet();
        lastScaleUpTime.set(System.currentTimeMillis());
        totalScaleUpConnections.addAndGet(addedConnections);
    }
    
    /**
     * 记录缩容操作
     */
    public void recordScaleDown(int removedConnections) {
        scaleDownCount.incrementAndGet();
        lastScaleDownTime.set(System.currentTimeMillis());
        totalScaleDownConnections.addAndGet(removedConnections);
    }
    
    /**
     * 记录tick执行
     */
    public void recordTick(double utilization, int awaitingThreads, int currentPoolSize, int maxPoolSize, int minPoolSize) {
        totalTickCount.incrementAndGet();
        
        // 更新利用率统计
        lastUtilization = utilization;
        if (utilization > maxUtilization) {
            maxUtilization = utilization;
        }
        if (utilization < minUtilization) {
            minUtilization = utilization;
        }
        
        // 统计高低利用率次数
        if (utilization >= 0.75) {
            highUtilizationCount.incrementAndGet();
        }
        if (utilization <= 0.25) {
            lowUtilizationCount.incrementAndGet();
        }
        
        // 更新等待线程统计
        lastAwaitingThreads = awaitingThreads;
        if (awaitingThreads > maxAwaitingThreads) {
            maxAwaitingThreads = awaitingThreads;
        }
        if (awaitingThreads > 0) {
            awaitingThreadsDetectedCount.incrementAndGet();
        }
        
        // 记录边界触达
        if (currentPoolSize >= maxPoolSize) {
            maxPoolSizeReached.incrementAndGet();
        }
        if (currentPoolSize <= minPoolSize) {
            minPoolSizeReached.incrementAndGet();
        }
    }
    
    /**
     * 记录tick执行错误
     */
    public void recordTickError() {
        errorTickCount.incrementAndGet();
    }
    
    /**
     * 重置所有统计数据
     */
    public void reset() {
        scaleUpCount.set(0);
        lastScaleUpTime.set(0);
        totalScaleUpConnections.set(0);
        
        scaleDownCount.set(0);
        lastScaleDownTime.set(0);
        totalScaleDownConnections.set(0);
        
        totalTickCount.set(0);
        errorTickCount.set(0);
        maxPoolSizeReached.set(0);
        minPoolSizeReached.set(0);
        
        lastUtilization = 0.0;
        maxUtilization = 0.0;
        minUtilization = 1.0;
        highUtilizationCount.set(0);
        lowUtilizationCount.set(0);
        
        lastAwaitingThreads = 0;
        maxAwaitingThreads = 0;
        awaitingThreadsDetectedCount.set(0);
    }
    
    /**
     * 获取运行时长（毫秒）
     */
    public long getUptimeMs() {
        return System.currentTimeMillis() - createdTime;
    }
    
    /**
     * 获取平均扩容间隔（毫秒）
     */
    public double getAverageScaleUpInterval() {
        long count = scaleUpCount.get();
        if (count <= 1) {
            return 0.0;
        }
        return (double) getUptimeMs() / count;
    }
    
    /**
     * 获取平均缩容间隔（毫秒）
     */
    public double getAverageScaleDownInterval() {
        long count = scaleDownCount.get();
        if (count <= 1) {
            return 0.0;
        }
        return (double) getUptimeMs() / count;
    }
    
    /**
     * 获取错误率
     */
    public double getErrorRate() {
        long total = totalTickCount.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) errorTickCount.get() / total;
    }
    
    /**
     * 获取高利用率比例
     */
    public double getHighUtilizationRate() {
        long total = totalTickCount.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) highUtilizationCount.get() / total;
    }
    
    /**
     * 获取低利用率比例
     */
    public double getLowUtilizationRate() {
        long total = totalTickCount.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) lowUtilizationCount.get() / total;
    }
    
    // Getter方法
    public long getScaleUpCount() {
        return scaleUpCount.get();
    }
    
    public long getLastScaleUpTime() {
        return lastScaleUpTime.get();
    }
    
    public long getTotalScaleUpConnections() {
        return totalScaleUpConnections.get();
    }
    
    public long getScaleDownCount() {
        return scaleDownCount.get();
    }
    
    public long getLastScaleDownTime() {
        return lastScaleDownTime.get();
    }
    
    public long getTotalScaleDownConnections() {
        return totalScaleDownConnections.get();
    }
    
    public long getTotalTickCount() {
        return totalTickCount.get();
    }
    
    public long getErrorTickCount() {
        return errorTickCount.get();
    }
    
    public long getMaxPoolSizeReached() {
        return maxPoolSizeReached.get();
    }
    
    public long getMinPoolSizeReached() {
        return minPoolSizeReached.get();
    }
    
    public double getLastUtilization() {
        return lastUtilization;
    }
    
    public double getMaxUtilization() {
        return maxUtilization;
    }
    
    public double getMinUtilization() {
        return minUtilization;
    }
    
    public long getHighUtilizationCount() {
        return highUtilizationCount.get();
    }
    
    public long getLowUtilizationCount() {
        return lowUtilizationCount.get();
    }
    
    public int getLastAwaitingThreads() {
        return lastAwaitingThreads;
    }
    
    public int getMaxAwaitingThreads() {
        return maxAwaitingThreads;
    }
    
    public long getAwaitingThreadsDetectedCount() {
        return awaitingThreadsDetectedCount.get();
    }
    
    public long getCreatedTime() {
        return createdTime;
    }
    
    @Override
    public String toString() {
        return "AutoScaleMetrics{" +
                "scaleUpCount=" + scaleUpCount.get() +
                ", scaleDownCount=" + scaleDownCount.get() +
                ", totalTickCount=" + totalTickCount.get() +
                ", errorRate=" + String.format("%.2f%%", getErrorRate() * 100) +
                ", lastUtilization=" + String.format("%.2f%%", lastUtilization * 100) +
                ", maxUtilization=" + String.format("%.2f%%", maxUtilization * 100) +
                ", minUtilization=" + String.format("%.2f%%", minUtilization * 100) +
                ", uptimeMs=" + getUptimeMs() +
                '}';
    }
}
