package com.tencent.andata.utils.sink.partition;

import com.tencent.andata.utils.sink.partition.strategy.BasePartitionStrategy;


public class PartitionField {

    public BasePartitionStrategy strategy;
    public String field;

    public PartitionField(BasePartitionStrategy strategy, String field) {
        this.strategy = strategy;
        this.field = field;
    }

    @Override
    public String toString() {
        return "PartitionField{"
                + "strategy=" + strategy
                + ", field='" + field + '\''
                + '}';
    }
}
