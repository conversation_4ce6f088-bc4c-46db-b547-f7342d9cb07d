package com.tencent.andata.utils;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.nio.reactor.ConnectingIOReactor;
import org.apache.http.nio.reactor.IOReactorException;

import java.util.Map;

/**
 * http异步连接
 */
public class AsyncHttpClient {

    private static volatile CloseableHttpAsyncClient client = null;

    /***
     * 初始化client
     * @param connectTimeout .
     * @param socketTimeout .
     * @param maxConnTol .
     * @param maxPerRoute .
     */
    public static void initClient(int connectTimeout,
                                  int socketTimeout,
                                  int maxConnTol,
                                  int maxPerRoute) {
        //配置io线程
        IOReactorConfig ioReactorConfig = IOReactorConfig.custom().
                setIoThreadCount(Runtime.getRuntime().availableProcessors())
                .setSoKeepAlive(true)
                .build();
        //设置连接池大小
        ConnectingIOReactor ioReactor = null;
        try {
            ioReactor = new DefaultConnectingIOReactor(ioReactorConfig);
        } catch (IOReactorException e) {
            e.printStackTrace();
        }
        assert ioReactor != null;
        PoolingNHttpClientConnectionManager connManager = new PoolingNHttpClientConnectionManager(
                ioReactor);
        connManager.setMaxTotal(maxConnTol);//最大连接数设置
        connManager.setDefaultMaxPerRoute(20);//per route最大连接数设置

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(connectTimeout)//连接超时,连接建立时间,三次握手完成时间
                .setSocketTimeout(socketTimeout)//请求超时,数据传输过程中数据包之间间隔的最大时间
                .setConnectionRequestTimeout(20000)//使用连接池来管理连接,从连接池获取连接的超时时间
                .build();

        client = HttpAsyncClients.custom()
                .setConnectionManager(connManager)
                .setDefaultRequestConfig(requestConfig)
                .build();
        client.start();
    }

    /**
     * 获取异步httpclient,手动设置超时时间
     */
    public static CloseableHttpAsyncClient getHttpClient(int connectTimeout,
                                                         int socketTimeout,
                                                         int maxConnTol,
                                                         int maxPerRoute) {
        if (client == null) {
            synchronized (AsyncHttpClient.class) {
                if (client == null) {
                    initClient(connectTimeout, socketTimeout, maxConnTol, maxPerRoute);
                }
            }
        }
        return client;
    }

    /**
     * 获取异步httpclient
     */
    public static CloseableHttpAsyncClient getHttpClient() {
        return getHttpClient(2000, 2000, 20, 20);
    }

    public static HttpPost doHttpPost(String api, String params, ContentType contentType) {
        HttpPost post = new HttpPost(api);
        StringEntity entity = new StringEntity(params, contentType);
        post.setEntity(entity);
        return post;
    }

    public static HttpPost doHttpPost(String api, Map<String, String> headers, String params, ContentType contentType) {
        HttpPost post = new HttpPost(api);
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            post.setHeader(entry.getKey(), entry.getValue());
        }
        StringEntity entity = new StringEntity(params, contentType);
        post.setEntity(entity);
        return post;
    }
}