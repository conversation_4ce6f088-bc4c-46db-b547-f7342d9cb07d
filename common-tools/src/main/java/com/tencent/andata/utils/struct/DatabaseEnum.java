package com.tencent.andata.utils.struct;

import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;

public enum DatabaseEnum {
    MYSQL {
        @Override
        public String toString() {
            return "mysql";
        }
    },
    PGSQL {
        @Override
        public String toString() {
            return "postgresql";
        }
    },

    ICEBERG {
        @Override
        public String toString() {
            return "iceberg";
        }
    },
    CK {
        @Override
        public String toString() {
            return "clickhouse";
        }
    },
    ROCKS {
        @Override
        public String toString() {
            return "mysql";
        }
    },
    DATA_BUS {
        @Override
        public String toString() {
            return "dbus";
        }
    }
}