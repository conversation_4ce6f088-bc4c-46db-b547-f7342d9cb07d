package com.tencent.andata.utils.factory;

import com.tencent.andata.utils.IcebergCatalogReader;
import java.util.HashMap;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.iceberg.BaseTable;
import org.apache.iceberg.flink.FlinkSchemaUtil;

@Deprecated
public class IcebergTableDDLFactory extends BaseTableDDLFactory {

    private IcebergCatalogReader icebergCatalogReader;
    private org.apache.iceberg.Table icebergTable;
    private String primaryKeyName;

    /**
     * Iceberg Connector相关
     *
     * @param dbName Iceberg 库名
     * @param tableName Iceberg 表名
     * @throws TableNotExistException
     */
    public IcebergTableDDLFactory(String dbName, String tableName) throws TableNotExistException {
        icebergCatalogReader = new IcebergCatalogReader();
        icebergTable = icebergCatalogReader.getTableInstance(dbName, tableName);

        String version = String.valueOf(((BaseTable) icebergTable).operations().current().formatVersion());

        // 获取表property
        String[] params = icebergTable.name().split("\\.");
        this.tableProperties = new HashMap<String, String>() {{
            put("connector", "iceberg");
            put("uri", "thrift://ss-qe-nginx-tauth.tencent-distribute.com:8108");
            put("warehouse", icebergTable.location());
            put("format-version", version);
            put("catalog-name", params[0]);
            put("catalog-database", params[1]);
            put("catalog-table", params[2]);
            put("write.distribution-mode", "hash");
            // 实现Upsert入库还需要在Insert SQL 中添加OPTIONS('equality-field-columns'='pk')
            put("write.upsert.enabled", "true");
            put("cache-enabled", "false");
        }};
    }

    /**
     * 设置主键
     *
     * @param primaryKeyName 主键字段名
     * @return
     */
    public IcebergTableDDLFactory setPrimaryKeyName(String primaryKeyName) {
        this.primaryKeyName = primaryKeyName;
        return this;
    }

    /**
     * 返回DDL
     *
     * @return
     */
    public String getTableSchemaDDL() {
        // 获取Schema DDL
        String icebergTableSchemalDDL = getTableSchema().toString();

        // 如果有主键则添加主键
        if (this.primaryKeyName != null) {
            String pkDDL = String.format("PRIMARY KEY (`%s`) NOT ENFORCED", primaryKeyName);
            icebergTableSchemalDDL = String.format(
                    "%s  ,%s\n" + ")",
                    // 去掉DDL的最后一个反括号
                    icebergTableSchemalDDL.substring(0, icebergTableSchemalDDL.length() - 1),
                    pkDDL
            );
        }
        return icebergTableSchemalDDL;
    }

    /**
     * 返回Schema
     *
     * @return
     */
    public Schema getTableSchema() {
        return FlinkSchemaUtil.toSchema(icebergTable.schema()).toSchema();
    }
}