package com.tencent.andata.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;

public class TimeUtil {

    // Instant表示的一定是utc时间
    public static Instant convert2Instant(LocalDateTime localDateTime) {
        return localDateTime.toInstant(ZoneOffset.of("+8"));
    }

    public static Instant convert2Instant(LocalDate localDate) {
        return localDate.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant();
    }

    public static ZonedDateTime convert2ZonedDateTime(Instant instant) {
        return instant.atZone(ZoneId.of("Asia/Shanghai"));
    }

    public static LocalDateTime convert2LocalDateTime(Instant instant) {
        return LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Shanghai"));
    }

    public static LocalDate convert2LocalDate(Instant instant) {
        return instant.atZone(ZoneId.of("Asia/Shanghai")).toLocalDate();
    }
}
