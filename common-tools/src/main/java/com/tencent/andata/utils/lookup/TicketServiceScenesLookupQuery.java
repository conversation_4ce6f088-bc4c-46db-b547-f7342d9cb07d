//package com.tencent.andata.utils.lookup;
//
//import com.google.common.base.CaseFormat;
//import com.tencent.andata.customization.antool.beans.ServiceScenes;
//import com.tencent.andata.utils.struct.DatabaseConf;
//import org.apache.commons.beanutils.BeanUtils;
//
//import java.sql.Connection;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.ResultSetMetaData;
//import java.util.List;
//
//public class TicketServiceScenesLookupQuery extends PostgresLookupQuery<Integer, ServiceScenes> {
//    public TicketServiceScenesLookupQuery(DatabaseConf databaseConf) {
//        super(databaseConf);
//    }
//
//
//    @Override
//    public PreparedStatement buildStatement(Connection conn) {
//        return null;
//    }
//
//    @Override
//    public ServiceScenes doQuery(PreparedStatement sts, Integer serviceScene) throws Exception {
//        this.statement.setInt(1, serviceScene);
//        final ResultSet resultSet = this.statement.executeQuery();
//        //创建对象
//        ServiceScenes res = new ServiceScenes();
//        while (resultSet.next()) {  //行遍历
//            final ResultSetMetaData metaData = resultSet.getMetaData();
//            for (int i = 0; i < metaData.getColumnCount(); i++) {  //列遍历
//                //获取列名
//                String columnName = metaData.getColumnName(i + 1);
//                //获取列值
//                Object value = resultSet.getObject(columnName);
//                // 统一下换线转驼峰
//                columnName = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, columnName.toLowerCase());
//
//                //给T对象赋值
//                BeanUtils.setProperty(res, columnName, value);
//            }
//            break;
//        }
//        return res;
//    }
//
//    @Override
//    public List<ServiceScenes> doQueryList(PreparedStatement sts, Integer integer, boolean all, int cnt) throws Exception {
//        return null;
//    }
//
//
//
//}
