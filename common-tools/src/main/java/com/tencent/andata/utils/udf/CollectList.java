package com.tencent.andata.utils.udf;

import java.util.ArrayList;
import java.util.List;
import org.apache.flink.table.functions.AggregateFunction;

public class CollectList extends AggregateFunction<String[], List<String>> {

    public void retract(List<String> acc, String conlum) {
        acc.remove(conlum);
    }

    public void accumulate(List<String> acc, String conlum) {
        acc.add(conlum);
    }

    @Override
    public String[] getValue(List list) {
        return (String[]) list.toArray(new String[0]);
    }

    @Override
    public List<String> createAccumulator() {
        return new ArrayList<>();
    }

    public void resetAccumulator(List<String> list) {
        list.clear();
    }
}
