package com.tencent.andata.utils;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;
import static java.util.concurrent.TimeUnit.MILLISECONDS;
import static org.apache.flink.table.api.DataTypes.INT;
import static org.apache.flink.table.api.DataTypes.TIMESTAMP_LTZ;
import static org.apache.flink.table.api.Schema.newBuilder;

import com.tencent.andata.utils.conf.IcebergTableConfEnum;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.BaseTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.CDCTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.HbaseTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.JDBCTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.StarRocksTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.Tuple2;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.shaded.guava30.com.google.common.base.CaseFormat;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.catalog.Column;
import org.apache.flink.table.catalog.ResolvedSchema;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.LogicalTypeFamily;
import org.apache.flink.table.types.logical.LogicalTypeRoot;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.types.logical.TimestampType;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.iceberg.BaseTable;
import org.apache.iceberg.TableMetadata;
import org.apache.iceberg.TableOperations;
import org.apache.iceberg.UpdateProperties;
import org.apache.iceberg.flink.FlinkSchemaUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * TableUtils提供了一系列用于Flink表操作的工具方法
 * 该工具类遵循SOLID原则，采用函数式和面向对象的混合设计
 *
 * <AUTHOR>
 */
@Slf4j
public class TableUtils implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(TableUtils.class);

    // MySQL server ID相关常量 - 支持并行度的范围分配
    private static final int SERVER_ID_RANGE_SIZE = 1000; // 每个表分配100个server ID的范围
    private static final int TIMESTAMP_MODULO = 1000000; // 时间戳取模值
    private static final long MYSQL_SERVER_ID_MIN = 5400L;
    private static final long MYSQL_SERVER_ID_MAX = 4294967295L; // MySQL允许的最大值


    /**
     * 构造函数
     */
    public TableUtils() {
    }

    /**
     * 创建ExecutorService线程池，用于并行处理表注册任务
     *
     * @return ExecutorService实例
     */
    private static ExecutorService createExecutorService() {
        return new ThreadPoolExecutor(
                ThreadPoolConfig.CORE_POOL_SIZE,
                ThreadPoolConfig.MAX_POOL_SIZE,
                ThreadPoolConfig.KEEP_ALIVE_TIME,
                ThreadPoolConfig.TIME_UNIT,
                new LinkedBlockingQueue<>(ThreadPoolConfig.QUEUE_CAPACITY));
    }

    /**
     * 安全关闭线程池并等待任务完成
     *
     * @param service 线程池
     * @param timeout 超时时间(分钟)
     * @return 是否正常完成
     * @throws InterruptedException 线程中断异常
     */
    private static boolean shutdownAndWait(ExecutorService service, long timeout) throws InterruptedException {
        service.shutdown();
        return service.awaitTermination(timeout, TimeUnit.MINUTES);
    }

    /**
     * 处理主键配置，支持单主键和复合主键
     *
     * @param strategy 表构建策略
     * @param primaryKey 主键字符串，多个主键用逗号分隔
     */
    private static void configurePrimaryKey(BaseTableBuilderStrategy strategy, String primaryKey) {
        Option.of(primaryKey)
                .filter(StringUtils::isNotEmpty)
                .forEach(pk -> {
                    String[] keys = pk.split(",");
                    boolean isSingleKey = keys.length == 1;

                    if (strategy instanceof StarRocksTableBuilderStrategy) {
                        StarRocksTableBuilderStrategy starRocksStrategy = (StarRocksTableBuilderStrategy) strategy;
                        if (isSingleKey) {starRocksStrategy.primaryKeyName(pk);} else {starRocksStrategy.primaryKeyList(keys);}
                    } else if (strategy instanceof IcebergTableBuilderStrategy) {
                        IcebergTableBuilderStrategy icebergStrategy = (IcebergTableBuilderStrategy) strategy;
                        if (isSingleKey) {icebergStrategy.primaryKeyName(pk);} else {icebergStrategy.primaryKeyList(keys);}
                    }
                    // CDCTableBuilderStrategy没有主键方法，不需要处理
                });
    }

    /**
     * 通过DDL注册Flink Table
     *
     * @param tableEnv tableEnv
     * @param tableDDL tableDDL
     */
    public static void registerTable(StreamTableEnvironment tableEnv, String tableDDL) {
        // LOG.info(tableDDL);
        tableEnv.executeSql(tableDDL);
    }

    /**
     * 将JSON节点列表转换为Java列表
     *
     * @param tableMapping ArrayNode类型的表映射
     * @return 转换后的JsonNode列表
     */
    private static List<JsonNode> convertTableMapping(ArrayNode tableMapping) {
        return StreamSupport.stream(tableMapping.spliterator(), true)
                .collect(Collectors.toList());
    }

    /**
     * rdbTable 用Schema注册成 Flink Table
     *
     * @param dbConf 数据库配置
     * @param dbType 数据库类型(postgresql)
     * @param tableMapping rdbms Table 到 Flink Table 的映射
     * @param tableEnv tableEnv
     * @param tableType 表注册类型，source or sink
     */
    public static void pgdbTable2FlinkTable(DatabaseConf dbConf,
            ArrayNode tableMapping,
            DatabaseEnum dbType,
            StreamTableEnvironment tableEnv,
            String tableType) {
        List<JsonNode> datasets = convertTableMapping(tableMapping);

        // 根据表类型使用不同的策略
        Function<JsonNode, String> ddlBuilder = "source".equals(tableType)
                ? table -> buildSourceTableDDL(table, dbType, dbConf)
                : table -> buildSinkTableDDL(table, dbConf);

        // 注册表
        datasets.forEach(table -> Try.run(() -> {
            String ddl = ddlBuilder.apply(table);
            registerTable(tableEnv, ddl);
        }).onFailure(e -> LOG.error("注册表失败: {}", e.getMessage(), e)));
    }

    /**
     * 构建源表DDL
     *
     * @param table 表配置节点
     * @param dbType 数据库类型
     * @param dbConf 数据库配置
     * @return 构建的DDL字符串
     */
    private static String buildSourceTableDDL(JsonNode table, DatabaseEnum dbType, DatabaseConf dbConf) {
        String fTableName = table.get("fTable").asText();
        String dbTableName = table.get("rdbTable").asText();

        return Try.of(() -> FlinkTableDDL.builder()
                        .tableBuilderStrategy(new CDCTableBuilderStrategy(dbTableName, dbType, dbConf)
                                .property("debezium.bigint.unsigned.handling.mode", "long"))
                        .processTimeFiled("process_time")
                        .flinkTableName(fTableName)
                        .build())
                .onFailure(e -> LOG.error("构建源表DDL失败: {}", e.getMessage(), e))
                .get();

    }

    /**
     * 构建接收表DDL
     *
     * @param table 表配置节点
     * @param dbConf 数据库配置
     * @return 构建的DDL字符串
     */
    private static String buildSinkTableDDL(JsonNode table, DatabaseConf dbConf) {
        String fSchema = table.get("fSchema").asText();
        String fTableName = table.get("fTable").asText();
        String dbTableName = table.get("rdbTable").asText();

        return Try.of(() -> FlinkTableDDL.builder()
                        .tableBuilderStrategy(new JDBCTableBuilderStrategy(dbTableName, DatabaseEnum.PGSQL, dbConf))
                        .processTimeFiled("process_time")
                        .flinkTableName(fTableName)
                        .flinkTableSchema(fSchema)
                        .build())
                .onFailure(e -> LOG.error("构建Sink表DDL失败: {}", e.getMessage(), e))
                .get();
    }

    /**
     * 根据数据库类型创建对应的表构建策略
     *
     * @param fTable Flink表名
     * @param rdbTable 关系型数据库表名
     * @param primaryKey 主键
     * @param partitionPattern 分区模式
     * @param serverId 服务器ID
     * @param dbType 数据库类型
     * @param dbConf 数据库配置
     * @param properties 额外属性
     * @return 表名和表构建策略的元组
     * @throws Exception 异常
     */
    private static Tuple2<String, BaseTableBuilderStrategy> createTableBuilderStrategy(
            String fTable, String rdbTable, String primaryKey, String partitionPattern,
            String serverId, DatabaseEnum dbType, DatabaseConf dbConf, Properties properties) throws Exception {

        BaseTableBuilderStrategy strategy;

        switch (dbType) {
            case MYSQL:
                strategy = createMySQLStrategy(rdbTable, dbConf, partitionPattern, serverId, properties);
                break;
            case PGSQL:
                strategy = createPgSQLStrategy(rdbTable, dbConf, properties);
                break;
            case ROCKS:
                strategy = createStarRocksStrategy(rdbTable, dbConf, primaryKey);
                break;
            default:
                throw new RuntimeException(String.format("Input DBType %s cannot be recognized.", dbType));
        }

        return new Tuple2<>(fTable, strategy);
    }

    /**
     * 创建MySQL表构建策略
     *
     * @param rdbTable 关系型数据库表名
     * @param dbConf 数据库配置
     * @param partitionPattern 分区模式
     * @param serverId 服务器ID，用于避免CDC连接冲突
     * @param properties 额外属性
     * @return CDCTableBuilderStrategy实例
     */
    private static CDCTableBuilderStrategy createMySQLStrategy(String rdbTable, DatabaseConf dbConf,
            String partitionPattern, String serverId, Properties properties) {
        return Try.of(() -> {
                    CDCTableBuilderStrategy strategy = new CDCTableBuilderStrategy(
                            rdbTable, DatabaseEnum.MYSQL, dbConf, partitionPattern)
                            .property("scan.incremental.close-idle-reader.enabled", "true")
                            .property("debezium.bigint.unsigned.handling.mode", "long")
                            //.property("scan.startup.timestamp-millis", "1752651767000") // 设置起始时间
                            .property("jdbc.properties.useSSL", "false")
                            //.property("scan.startup.mode", "timestamp")  // 控制CDC读取数据的起始方式
                            .property("connection.pool.size", "50")
                            .serverTimeZone("Asia/Shanghai")
                            .property("connect.max-retries", "10")
                            .property("connect.timeout", "120s")
                            .property("server-id", serverId); // 设置唯一的server-id避免冲突

                    // 应用额外属性
                    if (properties != null) {
                        properties.forEach((k, v) -> strategy.property(k.toString(), v.toString()));
                    }
                    return strategy;
                }).onFailure(e -> LOG.error("创建MySQL表构建策略失败: {}", e.getMessage(), e))
                .getOrElseThrow(e -> new RuntimeException("创建MySQL表构建策略失败", e));
    }

    /**
     * 创建PostgreSQL表构建策略
     */
    private static JDBCTableBuilderStrategy createPgSQLStrategy(String rdbTable, DatabaseConf dbConf, Properties properties) throws Exception {

        JDBCTableBuilderStrategy strategy = new JDBCTableBuilderStrategy(
                rdbTable, DatabaseEnum.PGSQL, dbConf);

        // 应用额外属性
        if (properties != null) {
            properties.forEach((k, v) -> strategy.property(k.toString(), v.toString()));
        }

        return strategy;
    }

    /**
     * 创建StarRocks表构建策略
     */
    private static StarRocksTableBuilderStrategy createStarRocksStrategy(String rdbTable, DatabaseConf dbConf, String primaryKey) {

        StarRocksTableBuilderStrategy strategy = new StarRocksTableBuilderStrategy(
                rdbTable, DatabaseEnum.ROCKS, dbConf, 8080)
                .property("sink.parallelism", "4")
                .property("sink.max-retries", "1000")
                .property("sink.properties.format", "json")
                .property("sink.connect.timeout-ms", "60000")
                .property("sink.ignore.update-before", "true")
                .property("sink.buffer-flush.max-rows", "500000")
                .property("sink.buffer-flush.interval-ms", "300000")
                .property("sink.buffer-flush.max-bytes", "67108864")
                .property("sink.properties.ignore_json_size", "true")
                .property("sink.properties.strip_outer_array", "true")
                .property("sink.label-prefix", String.format("sink-label-%s-%s", dbConf.dbName, rdbTable));

        // 配置主键
        configurePrimaryKey(strategy, primaryKey);

        return strategy;
    }

    /**
     * 生成唯一的MySQL server ID范围
     * 为支持并行度，每个表分配一个server ID范围而不是单个ID
     * 每个并行的CDC Reader将从这个范围中获取唯一的server ID
     *
     * @param fTable Flink表名
     * @param rdbTable 关系型数据库表名
     * @return server ID范围字符串，格式如"5400-6399"
     */
    public static String generateUniqueServerId(String fTable, String rdbTable) {
        // 基于表名和数据库名生成稳定的hash值，确保同一个表每次得到相同的范围
        String tableKey = fTable + "." + rdbTable;
        int hashValue = Math.abs(tableKey.hashCode());

        // 使用时间戳增加随机性，避免不同部署环境的冲突
        long timestamp = System.currentTimeMillis();

        // 计算server ID范围
        String serverIdRange = calculateServerIdRange(hashValue, timestamp);

        LOG.info("为表 {}.{} 分配server ID范围: {} (支持最大{}个并行度，用于避免MySQL CDC连接冲突)",
                fTable, rdbTable, serverIdRange, SERVER_ID_RANGE_SIZE);

        return serverIdRange;
    }

    /**
     * 计算MySQL CDC连接所需的server ID范围
     *
     * @param hashValue 基于表名生成的哈希值
     * @param timestamp 当前时间戳
     * @return server ID范围字符串，格式如"5400-6399"
     */
    private static String calculateServerIdRange(int hashValue, long timestamp) {
        long combinedHash = (hashValue + (timestamp % TIMESTAMP_MODULO)) % Integer.MAX_VALUE;

        // 计算基础server ID，确保不会超出MySQL允许的范围
        long maxRangeStart = MYSQL_SERVER_ID_MAX - SERVER_ID_RANGE_SIZE;

        // 生成server ID范围
        long rangeStart = MYSQL_SERVER_ID_MIN + (combinedHash % (maxRangeStart - MYSQL_SERVER_ID_MIN));
        long rangeEnd = rangeStart + SERVER_ID_RANGE_SIZE - 1;

        // 确保范围有效
        if (rangeEnd > MYSQL_SERVER_ID_MAX) {
            rangeStart = MYSQL_SERVER_ID_MAX - SERVER_ID_RANGE_SIZE + 1;
            rangeEnd = MYSQL_SERVER_ID_MAX;
        }

        return rangeStart + "-" + rangeEnd;
    }

    /**
     * 验证server ID范围是否有效
     *
     * @param serverIdRange server ID范围字符串，格式如"5400-6399"
     * @return 是否有效
     */
    private static boolean isValidServerIdRange(String serverIdRange) {
        try {
            String[] parts = serverIdRange.split("-");
            if (parts.length != 2) {
                return false;
            }

            long start = Long.parseLong(parts[0]);
            long end = Long.parseLong(parts[1]);

            return start >= MYSQL_SERVER_ID_MIN &&
                    end <= MYSQL_SERVER_ID_MAX &&
                    start <= end &&
                    (end - start + 1) <= SERVER_ID_RANGE_SIZE;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证单个server ID是否在MySQL允许的有效范围内
     *
     * @param serverId 要验证的server ID
     * @return 是否有效
     */
    private static boolean isValidServerId(long serverId) {
        return serverId >= MYSQL_SERVER_ID_MIN && serverId <= MYSQL_SERVER_ID_MAX;
    }


    /**
     * rdbTable 注册成 Flink Table
     *
     * @param dbConf 数据库配置
     * @param dbType 数据库类型(mysql, postgresql)
     * @param tableMapping rdbms Table 到 Flink Table 的映射
     * @param tableEnv tableEnv
     * @param properties 额外属性
     * @throws InterruptedException 线程中断异常
     */
    public static void rdbTable2FlinkTable(DatabaseConf dbConf,
            ArrayNode tableMapping,
            DatabaseEnum dbType,
            StreamTableEnvironment tableEnv,
            Properties properties) throws InterruptedException {

        List<JsonNode> datasets = convertTableMapping(tableMapping);
        ExecutorService service = createExecutorService();

        // 使用Try封装主逻辑
        Try.run(() -> {
                    List<Future<Tuple2<String, BaseTableBuilderStrategy>>> taskList =
                            datasets.stream()
                                    .map(table -> {
                                        final String fTable = table.get("fTable").asText();
                                        final String rdbTable = table.get("rdbTable").asText();
                                        final String serverId = generateUniqueServerId(fTable, rdbTable);
                                        final String primaryKey = Option.of(table.get("primaryKey")).map(JsonNode::asText).getOrElse("");
                                        final String partitionPattern = Option.of(table.get("partitionPattern")).map(JsonNode::asText).getOrElse("");

                                        return service.submit(() ->
                                                Try.of(() -> createTableBuilderStrategy(fTable, rdbTable, primaryKey, partitionPattern, serverId, dbType, dbConf, properties))
                                                        .onFailure(e -> LOG.error("创建表构建策略失败: {}", e.getMessage(), e))
                                                        .getOrElseThrow(e -> new RuntimeException("创建表构建策略失败", e))
                                        );
                                    }).collect(Collectors.toList());

                    // 检查线程池超时并注册表
                    if (shutdownAndWait(service, ThreadPoolConfig.DEFAULT_TIMEOUT_MINUTES)) {
                        registerTablesFromTasks(taskList, tableEnv);
                    } else {
                        LOG.warn("超出线程池等待时间，部分表可能未完成注册");
                    }
                })
                .onFailure(e -> {
                    if (e instanceof InterruptedException) {
                        service.shutdownNow(); // 立即关闭线程池
                        Thread.currentThread().interrupt(); // 恢复中断状态
                    }
                    throw new RuntimeException("表注册过程被中断: " + e.getMessage(), e);
                });
    }

    /**
     * 从任务列表中注册表
     * 使用vavr API优化
     *
     * @param taskList 任务列表
     * @param tableEnv 表环境
     */
    private static void registerTablesFromTasks(List<Future<Tuple2<String, BaseTableBuilderStrategy>>> taskList, StreamTableEnvironment tableEnv) {

        taskList.forEach(task -> Try.of(() -> {
            Tuple2<String, BaseTableBuilderStrategy> result = task.get();
            String ddl = FlinkTableDDL.builder()
                    .flinkTableName(result._1)
                    .tableBuilderStrategy(result._2)
                    .processTimeFiled("process_time")
                    .build();
            registerTable(tableEnv, ddl);
            return true;
        }).onFailure(e -> LOG.error("注册表失败: {}", e.getMessage(), e)));
    }

    /**
     * rdbTable 注册成 Flink Table - 重载方法
     *
     * @param dbConf 数据库配置
     * @param dbType 数据库类型(mysql, postgresql)
     * @param tableMapping rdbms Table 到 Flink Table 的映射
     * @param tableEnv tableEnv
     * @throws InterruptedException 线程中断异常
     */
    public static void rdbTable2FlinkTable(
            DatabaseConf dbConf,
            ArrayNode tableMapping,
            DatabaseEnum dbType,
            StreamTableEnvironment tableEnv) throws InterruptedException {
        rdbTable2FlinkTable(dbConf, tableMapping, dbType, tableEnv, null);
    }

    /**
     * 将行类型转换为Flink Schema
     *
     * @param rowType 行类型
     * @return Flink Schema对象
     */
    public static Schema convertRowType2FlinkSchema(RowType rowType) {
        return FlinkSchemaUtil.toSchema(rowType).toSchema();
    }

    /**
     * icebergTable 注册成 Flink Table
     * 优化版 - 使用函数式编程和Try-Catch模式处理异常
     *
     * @param icebergDbName iceberg数据库名称
     * @param tableMapping iceberg Table 到 Flink Table 的映射
     * @param tableEnv 表环境
     * @param catalog Iceberg目录读取器
     */
    public static void icebergTable2FlinkTable(String icebergDbName, ArrayNode tableMapping, StreamTableEnvironment tableEnv, IcebergCatalogReader catalog) {

        ExecutorService service = createExecutorService();
        List<JsonNode> datasets = convertTableMapping(tableMapping);
        List<Future<Tuple2<String, IcebergTableBuilderStrategy>>> taskList = new ArrayList<>();

        // 提交Iceberg表注册任务
        datasets.forEach(consumer(table -> {
            final String fTable = table.get("fTable").asText();
            final String tblName = table.get("icebergTable").asText();
            final String primaryKey = Optional.ofNullable(table.get("primaryKey"))
                    .map(JsonNode::asText)
                    .orElse("");

            taskList.add(service.submit(() -> createIcebergTableStrategy(
                    fTable, catalog, icebergDbName, tblName, primaryKey)));
        }));

        // 使用Try模式包装异常处理
        Try.run(() -> {
            if (shutdownAndWait(service, ThreadPoolConfig.DEFAULT_TIMEOUT_MINUTES)) {
                registerIcebergTablesFromTasks(taskList, tableEnv);
            } else {
                throw new RuntimeException("超出线程池等待时间，部分表可能未完成注册");
            }
        }).getOrElseThrow(e -> {
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt(); // 恢复中断状态
                return new RuntimeException("Iceberg表注册过程被中断: " + e.getMessage(), e);
            }
            return new RuntimeException("Iceberg表注册失败: " + e.getMessage(), e);
        });
    }

    /**
     * 创建Iceberg表策略
     *
     * @param fTable Flink表名
     * @param catalog 目录
     * @param icebergDbName 数据库名称
     * @param tblName 表名
     * @param primaryKey 主键
     * @return 表名和Iceberg表构建策略的元组
     */
    private static Tuple2<String, IcebergTableBuilderStrategy> createIcebergTableStrategy(String fTable,
            IcebergCatalogReader catalog,
            String icebergDbName,
            String tblName,
            String primaryKey) {

        return Try.of(() -> {
                    org.apache.iceberg.Table table = catalog
                            .getTableLoaderInstance(icebergDbName, tblName)
                            .loadTable();

                    // 获取iceberg表注册策略并设置通用配置
                    IcebergTableBuilderStrategy strategy = new IcebergTableBuilderStrategy(table)
                            .writeUpsertEnabled("true")
                            .property("sink.parallelism", "1")
                            .writeDistributionMode("hash")
                            .property("write.distribution-mode", "hash")
                            .property("write.format.default", "PARQUET")
                            .property("write.upsert.use-rocksdb-map", "true")
                            .property("write.parquet.bloom-filter-max-bytes", "102400")
                            .property("write.pos-delete-has-same-seq-with-refs.enabled", "true")
                            .writeBloomFilter(primaryKey);

                    // 配置主键
                    configurePrimaryKey(strategy, primaryKey);

                    return new Tuple2<>(fTable, strategy);
                })
                .onFailure(e -> LOG.error("创建Iceberg表策略失败: {}", e.getMessage(), e))
                .getOrElseThrow(e -> new RuntimeException("创建Iceberg表策略失败: " + e.getMessage(), e)); // 直接重新抛出原异常
    }

    /**
     * 从任务列表中注册Iceberg表
     *
     * @param taskList 任务列表
     * @param tableEnv 表环境
     */
    private static void registerIcebergTablesFromTasks(List<Future<Tuple2<String, IcebergTableBuilderStrategy>>> taskList, StreamTableEnvironment tableEnv) {
        taskList.forEach(task -> Try.of(() -> {
                    Tuple2<String, IcebergTableBuilderStrategy> result = task.get();
                    String ddl = FlinkTableDDL.builder()
                            .flinkTableName(result._1)
                            .tableBuilderStrategy(result._2)
                            .processTimeFiled("process_time")
                            .build();
                    registerTable(tableEnv, ddl);
                    return null; // 无返回值，仅用于Try封装
                })
                .onFailure(e -> LOG.error("注册Iceberg表失败: {}", e.getMessage(), e))
                .getOrElseThrow(e -> new RuntimeException("注册Iceberg表失败: " + e.getMessage(), e)));
    }

    /**
     * 获取insert into 语句
     * 优化版 - 使用枚举和模板方法模式简化代码
     *
     * @param srcName 源表名称
     * @param dstName 目标表名称
     * @param table 目标表映射成flink的table对象
     * @param dbType 数据库类型(postgresql, iceberg)
     * @return insert into 语句
     */
    public static String insertIntoSql(String srcName, String dstName, Table table, DatabaseEnum dbType) {
        String tableFields = getTableFieldsV2(table);

        switch (dbType) {
            case PGSQL:
            case MYSQL:
            case ROCKS:
                return String.format(SqlTemplates.INSERT_INTO, dstName, tableFields, srcName);
            case ICEBERG:
                return String.format(SqlTemplates.INSERT_INTO_WITH_HINTS, dstName, TableHints.ICEBERG_HINTS, tableFields, srcName);
            default:
                throw new RuntimeException(String.format("Input DBType %s cannot be recognized.", dbType));
        }
    }

    /**
     * 动态生成INSERT INTO语句，确保源视图和目标表之间的字段精确匹配。
     *
     * @param sourceViewName 要从中查询的视图名称。
     * @param sourceView 视图对应的Table对象，用于获取其Schema。
     * @param sinkTableName 要插入的目标表名。
     * @param sinkSchemaProvider 目标表对应的Table对象，用于获取其Schema。
     * @param dbType 数据库类型，用于获取正确的HINTS。
     * @return 动态生成的、健壮的INSERT INTO SQL语句。
     */
    public static String insertIntoSql(String sourceViewName, Table sourceView, String sinkTableName, Table sinkSchemaProvider, DatabaseEnum dbType) {
        ResolvedSchema sourceSchema = sourceView.getResolvedSchema();
        ResolvedSchema sinkSchema = sinkSchemaProvider.getResolvedSchema();

        log.info("进行动态SQL Schema比对: [源] {} -> [目标] {}", sourceViewName, sinkTableName);

        // 匹配源表和目标表的列
        List<Tuple2<Column, Column>> commonColumnPairs = matchColumns(sourceSchema, sinkSchema);

        // 对于Iceberg类型，执行详细的Schema比对和日志记录
        if (dbType.equals(DatabaseEnum.ICEBERG)) {
            logSchemaComparison(sourceViewName, sinkTableName, sourceSchema, sinkSchema, commonColumnPairs);
        }

        // 生成SQL子句
        String selectClause = buildSelectClause(commonColumnPairs);
        String sinkColumnsClause = buildSinkColumnsClause(commonColumnPairs);

        // 验证并构建最终SQL
        return buildFinalSql(sourceViewName, sinkTableName, selectClause, sinkColumnsClause);
    }

    /**
     * 匹配源表和目标表的列，支持不区分大小写的匹配。
     */
    private static List<Tuple2<Column, Column>> matchColumns(ResolvedSchema sourceSchema, ResolvedSchema sinkSchema) {
        // 创建从源列名的小写版本到其原始列对象的映射
        Map<String, Column> sourceColumnMap = sourceSchema.getColumns().stream()
                .collect(Collectors.toMap(
                        c -> c.getName().toLowerCase(),
                        Function.identity(),
                        (c1, c2) -> c1)); // 如果源中有重复的小写名，保留第一个

        // 遍历目标表的列，为每一列在源表中寻找匹配项
        return sinkSchema.getColumns().stream()
                .map(sinkColumn -> {
                    Column sourceColumn = sourceColumnMap.get(sinkColumn.getName().toLowerCase());
                    return sourceColumn != null ?
                            Optional.of(new Tuple2<>(sourceColumn, sinkColumn)) :
                            Optional.<Tuple2<Column, Column>>empty();
                })
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    /**
     * 记录Schema比对的详细信息和警告。
     */
    private static void logSchemaComparison(
            String sourceViewName,
            String sinkTableName,
            ResolvedSchema sourceSchema,
            ResolvedSchema sinkSchema,
            List<Tuple2<Column, Column>> commonColumnPairs) {

        Set<String> commonSinkColumnNames = commonColumnPairs.stream()
                .map(p -> p._2.getName().toLowerCase())
                .collect(Collectors.toSet());

        // 检查源表中独有的字段
        sourceSchema.getColumnNames().stream()
                .filter(name -> !commonSinkColumnNames.contains(name.toLowerCase()))
                .findFirst()
                .ifPresent(s -> log.warn("源视图 '{}' 中可能存在目标表中缺失的字段 (例如: {})", sourceViewName, s));

        // 检查目标表中独有的字段（排除process_time）
        sinkSchema.getColumnNames().stream()
                .filter(name -> !name.equalsIgnoreCase("process_time") &&
                        !commonSinkColumnNames.contains(name.toLowerCase()))
                .findFirst()
                .ifPresent(s -> log.warn("目标表 '{}' 中可能存在源视图中缺失的字段 (例如: {})", sinkTableName, s));

        // 检查配对列的名称和类型差异
        commonColumnPairs.forEach(pair -> {
            Column sourceColumn = pair._1;
            Column sinkColumn = pair._2;

            // 检查大小写不一致
            if (!sourceColumn.getName().equals(sinkColumn.getName())) {
                log.warn("字段大小写不匹配: 源字段='{}', 目标字段='{}'",
                        sourceColumn.getName(), sinkColumn.getName());
            }

            // 检查类型不匹配
            if (!Objects.equals(sourceColumn.getDataType(), sinkColumn.getDataType())) {
                log.warn("共有字段 '{}' 的类型不匹配: 源类型是 {}, 目标类型是 {}",
                        sinkColumn.getName(), sourceColumn.getDataType(), sinkColumn.getDataType());
            }
        });
    }

    /**
     * 构建SELECT子句，处理类型转换和字段重命名。
     */
    private static String buildSelectClause(List<Tuple2<Column, Column>> commonColumnPairs) {
        return commonColumnPairs.stream()
                .map(pair -> {
                    Column sourceColumn = pair._1;
                    Column sinkColumn = pair._2;
                    DataType sourceType = sourceColumn.getDataType();
                    DataType sinkType = sinkColumn.getDataType();

                    String sourceFieldName = String.format("`%s`", sourceColumn.getName());
                    String sinkFieldName = String.format("`%s`", sinkColumn.getName());



                    // 检查是否需要类型转换（忽略时间戳精度差异）
                    boolean needsCast = !Objects.equals(sourceType, sinkType) &&
                            !(sourceType.getLogicalType().is(LogicalTypeFamily.DATETIME) &&
                                    sinkType.getLogicalType().is(LogicalTypeFamily.DATETIME));

                    if (needsCast) {
                        return String.format("CAST(%s AS %s) AS %s", sourceFieldName, sinkType.nullable().toString(), sinkFieldName);
                    }

                    if (!sourceColumn.getName().equals(sinkColumn.getName())) {
                        // 字段名大小写不同时需要重命名
                        return String.format("%s AS %s", sourceFieldName, sinkFieldName);
                    }

                    return sinkFieldName;
                }).collect(Collectors.joining(", "));
    }

    /**
     * 检查数据类型是否为LocalDateTime类型
     */
    private static boolean isLocalDateTimeType(DataType dataType) {
        return dataType.toString().contains("java.time.LocalDateTime");
    }

    /**
     * 构建INSERT INTO的列清单。
     */
    private static String buildSinkColumnsClause(List<Tuple2<Column, Column>> commonColumnPairs) {
        return commonColumnPairs.stream()
                .map(pair -> String.format("`%s`", pair._2.getName()))
                .collect(Collectors.joining(", "));
    }

    /**
     * 构建最终的SQL语句并进行验证。
     */
    private static String buildFinalSql(
            String sourceViewName,
            String sinkTableName,
            String selectClause,
            String sinkColumnsClause) {

        if (selectClause.isEmpty()) {
            throw new RuntimeException(String.format("视图(%s)与目标表(%s)之间没有共同字段。", sourceViewName, sinkTableName));
        }

        String sql = String.format(
                "INSERT INTO %s (%s) SELECT %s FROM %s",
                sinkTableName, sinkColumnsClause, selectClause, sourceViewName);

        log.info("源表：{}, 目标表：{}, 生成的SQL：{}", sourceViewName, sinkTableName, sql);

        return sql;
    }

    /**
     * 行记录转化成json字符串
     * 优化版 - 使用流式处理和函数式风格提高性能
     *
     * @param table flink table 对象
     * @param rowkey hbase rowkey
     * @param sourceView sourceView
     * @return SQL字符串
     */
    public static String row2Json(Table table, String rowkey, String sourceView) {
        String columnValuePairs = table.getResolvedSchema()
                .getColumnNames()
                .stream()
                .map(column -> String.format("'%s' VALUE `%s`", column, column))
                .collect(Collectors.joining(", "));

        return String.format(SqlTemplates.ROW_TO_JSON, rowkey, columnValuePairs, sourceView);
    }

    /**
     * 将数据导入HBase
     * 优化版 - 使用常量模板提高可维护性
     *
     * @param sourceTable 源表名称
     * @param sinkTable hbase表名称
     * @return SQL字符串
     */
    public static String sinkToHbase(String sourceTable, String sinkTable) {
        return String.format(SqlTemplates.SINK_TO_HBASE, sinkTable, TableHints.HBASE_PARALLEL_HINTS, sourceTable);
    }

    /**
     * 升级Iceberg的版本
     * 优化版 - 增加错误处理和日志
     *
     * @param table iceberg表
     * @param version 版本(1/2)
     */
    public static void upgradeIcebergTableFormatVersion(org.apache.iceberg.Table table, int version) {
        Try.run(() -> {
            TableOperations operations = ((BaseTable) table).operations();
            TableMetadata metadata = operations.current();

            if (metadata.formatVersion() < version) {
                LOG.info("正在将表 {} 的格式版本从 {} 升级到 {}", table.name(), metadata.formatVersion(), version);
                operations.commit(metadata, metadata.upgradeToFormatVersion(version));
                LOG.info("表 {} 格式版本升级成功", table.name());
            } else {
                LOG.info("表 {} 当前格式版本为 {}，无需升级", table.name(), metadata.formatVersion());
            }

        }).onFailure(e -> LOG.error("升级表 {} 格式版本失败: {}", table.name(), e.getMessage(), e));
    }

    /**
     * 将flink table schema转换成RowType
     * 优化版 - 使用函数式编程和策略模式处理不同类型
     *
     * @param schema schema
     * @param compatibleWithTDW compatibleWithTDW
     * @return RowType
     */
    public static RowType convertFlinkSchemaToRowType(Schema schema, Boolean compatibleWithTDW) {
        List<RowType.RowField> logicalFields = schema.getColumns().stream()
                .map(column -> createRowField(column, compatibleWithTDW))
                .collect(Collectors.toList());

        return new RowType(logicalFields);
    }

    /**
     * 根据列和兼容性创建RowField
     *
     * @param column 列
     * @param compatibleWithTDW 是否与TDW兼容
     * @return 行字段
     */
    private static RowType.RowField createRowField(Schema.UnresolvedColumn column, Boolean compatibleWithTDW) {
        String name = column.getName();
        DataType type = (DataType) ((Schema.UnresolvedPhysicalColumn) column).getDataType();
        LogicalTypeRoot typeRoot = type.getLogicalType().getTypeRoot();

        // 与TDW & Spark建表保持一致，时间戳类型特殊处理
        if (typeRoot.equals(LogicalTypeRoot.TIMESTAMP_WITHOUT_TIME_ZONE) && compatibleWithTDW) {
            int precision = ((TimestampType) type.getLogicalType()).getPrecision();
            return new RowType.RowField(name, TIMESTAMP_LTZ(precision).getLogicalType());
        }

        // 小整数类型转为INT
        else if (typeRoot.equals(LogicalTypeRoot.TINYINT) || typeRoot.equals(LogicalTypeRoot.SMALLINT)) {
            return new RowType.RowField(name, INT().getLogicalType());
        }

        return new RowType.RowField(name, type.getLogicalType());
    }

    /**
     * 将RowType转换为Iceberg Schema
     *
     * @param rowType 行类型
     * @return Iceberg Schema
     */
    public static org.apache.iceberg.Schema convertRowTypeToIcebergSchema(RowType rowType) {
        return FlinkSchemaUtil.convert(FlinkSchemaUtil.toSchema(rowType));
    }

    /**
     * 根据Schema和字段名获取字段获取器
     * 优化版 - 使用函数式流式处理提高性能
     *
     * @param schema Schema
     * @param fieldNames 字段名列表
     * @return 字段获取器列表
     */
    public static List<RowData.FieldGetter> getFieldGettersUsingFlinkSchemaAndFieldName(Schema schema, List<String> fieldNames) {

        List<Schema.UnresolvedColumn> columns = schema.getColumns();
        List<String> colNames = columns.stream()
                .map(Schema.UnresolvedColumn::getName)
                .collect(Collectors.toList());

        return fieldNames.stream()
                .map(field -> {
                    int index = colNames.indexOf(field);
                    if (index >= 0) {
                        Schema.UnresolvedColumn column = columns.get(index);
                        DataType type = (DataType) ((Schema.UnresolvedPhysicalColumn) column).getDataType();
                        return RowData.createFieldGetter(type.getLogicalType(), index);
                    }
                    return null;
                })
                .filter(getter -> getter != null)
                .collect(Collectors.toList());
    }

    /**
     * 注册HBase表到Flink
     * 优化版 - 使用Try-Catch模式处理异常
     *
     * @param fTableName flink hbase table name
     * @param hbaseTableName hbase table name
     * @param columnFamilyName 列族名
     * @param tableEnv 表环境
     * @throws Exception 异常
     */
    public static void hbaseTable2FlinkTable(
            String fTableName, String hbaseTableName, String columnFamilyName,
            StreamTableEnvironment tableEnv) throws Exception {

        Try.of(() -> {
            RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();
            Properties properties = PropertyUtils.loadProperties("env.properties");
            String zkQuorum = rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_QUORUM");
            String zkNodeParent = rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_ZNODE_PARENT");

            // 创建HBase表构建策略
            HbaseTableBuilderStrategy strategy = new HbaseTableBuilderStrategy(
                    hbaseTableName, columnFamilyName, zkQuorum, zkNodeParent)
                    .sinkBufferFlushSize("50mb")
                    .sinkBufferFlushInterval("3s");

            // 注册表
            registerTable(tableEnv,
                    FlinkTableDDL.builder()
                            .flinkTableName(fTableName)
                            .tableBuilderStrategy(strategy)
                            .build());

            return true;
        }).getOrElseThrow(e -> new Exception("HBase表注册失败: " + e.getMessage(), e));
    }

    /**
     * 获取flink table 的字段信息并转换类型
     * 优化版 - 使用函数式编程减少循环
     *
     * @param table flink table
     * @return table columns with new type
     */
    public static String getTableFieldsV2(Table table) {
        ResolvedSchema schema = table.getResolvedSchema();
        List<Column> columns = schema.getColumns();

        return columns.stream()
                .filter(column -> !column.getName().equals("process_time"))
                .map(column -> {
                    String colName = column.getName();
                    String dataType = column.getDataType().getLogicalType().copy(true).toString();
                    String underscoreName = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, colName);

                    return String.format("CAST(`%s` AS %s) AS `%s`", colName, dataType, underscoreName);
                })
                .collect(Collectors.joining(", "));
    }

    /**
     * 获取表对应的Row类型信息
     *
     * @param table 输入表对象
     * @return 包含表字段类型和名称的RowTypeInfo对象
     * @throws NullPointerException 如果输入参数table为null
     */
    public static TypeInformation<Row> getTableRowTypeInformation(Table table) {
        ResolvedSchema schema = table.getResolvedSchema();

        // 获取字段名和数据类型
        String[] fieldNames = schema.getColumnNames().toArray(new String[0]);
        List<DataType> columnDataTypes = schema.getColumnDataTypes();

        // 转换为TypeInformation数组
        TypeInformation<?>[] fieldTypes = columnDataTypes.stream()
                .map(dataType -> {
                    LogicalType logicalType = dataType.getLogicalType();

                    // 特殊处理：LocalDateTime类型转换为Timestamp类型
                    if (isLocalDateTimeType(dataType)) {
                        log.info("检测到LocalDateTime类型，转换为Timestamp类型: {}", dataType);
                        return TypeInformation.of(java.sql.Timestamp.class);
                    }

                    Class<?> conversionClass = logicalType.getDefaultConversion();
                    return TypeInformation.of(conversionClass);
                })
                .toArray(TypeInformation[]::new);

        return new RowTypeInfo(fieldTypes, fieldNames);
    }

    /**
     * 将flink table 转换成TypeInformation RowData
     * 优化版 - 提取公共逻辑减少代码重复
     *
     * @param table 表
     * @return TypeInformation
     */
    public static TypeInformation<RowData> getTableTypeInformation(Table table) {
        ResolvedSchema schema = table.getResolvedSchema();
        return createTypeInformation(
                schema.getColumnNames().toArray(new String[0]),
                schema.toPhysicalRowDataType().getLogicalType().getChildren().toArray(new LogicalType[0])
        );
    }

    /**
     * 将TableSchema转换为TypeInformation
     * 修复使用弃用的TableSchema API
     *
     * @param schema Schema
     * @return TypeInformation
     */
    public static TypeInformation<RowData> convertSchemaToTypeInformation(Schema schema) {
        RowType rowType = convertFlinkSchemaToRowType(schema, false);
        String[] fieldNames = rowType.getFieldNames().toArray(new String[0]);
        LogicalType[] logicalTypes = rowType.getChildren().toArray(new LogicalType[0]);

        return createTypeInformation(fieldNames, logicalTypes);
    }

    /**
     * 创建TypeInformation
     *
     * @param fieldNames 字段名
     * @param fieldTypes 字段类型
     * @return TypeInformation
     */
    private static TypeInformation<RowData> createTypeInformation(String[] fieldNames, LogicalType[] fieldTypes) {
        return InternalTypeInfo.ofFields(fieldTypes, fieldNames);
    }

    /**
     * 设置Iceberg表最小属性
     * 优化版 - 增加错误处理和业务逻辑封装
     *
     * @param table Iceberg表
     * @param pkList 主键列表
     */
    public static void setIcebergTableMinimizedPropertiesIfNotSet(org.apache.iceberg.Table table, List<String> pkList) {

        Try.run(() -> {
            Map<String, String> tblDefaultConfMap = IcebergTableConfEnum.getDefaultIcebergTableConfMap(pkList);

            if (!isIcebergTablePropertiesSetMinimized(table, tblDefaultConfMap)) {
                String tableName = table.name();
                LOG.info("开始设置表 {} 的最小属性集", tableName);
                setIcebergTableProperties(table, tblDefaultConfMap);
                LOG.info("表 {} 最小属性设置完成", tableName);
            } else {LOG.info("表 {} 已设置最小属性，无需重新设置", table.name());}
        }).onFailure(e -> LOG.error("设置表 {} 最小属性失败: {}", table.name(), e.getMessage(), e));
    }

    /**
     * 检查Iceberg表是否已设置最小属性
     * 优化版 - 使用不可变集合避免副作用
     *
     * @param table Iceberg表
     * @param tblConfMap 表配置映射
     * @return 是否设置
     */
    public static boolean isIcebergTablePropertiesSetMinimized(org.apache.iceberg.Table table, Map<String, String> tblConfMap) {

        // 创建不可变集合副本，避免修改原集合
        Set<String> defaultTableKeySet = new HashSet<>(tblConfMap.keySet());
        Set<String> tableKeySet = table.properties().keySet();

        // 计算差集判断是否包含所有必需属性
        defaultTableKeySet.removeAll(tableKeySet);
        return defaultTableKeySet.isEmpty();
    }

    /**
     * 设置Iceberg表属性
     * 优化版 - 增加错误处理和日志
     *
     * @param table Iceberg表
     * @param properties 属性
     */
    public static void setIcebergTableProperties(org.apache.iceberg.Table table, Map<String, String> properties) {

        if (properties == null || properties.isEmpty()) {
            LOG.info("未提供属性，跳过表 {} 属性设置", table.name());
            return;
        }

        Try.run(() -> {
            LOG.info("开始为表 {} 设置 {} 个属性", table.name(), properties.size());
            UpdateProperties updateProperties = table.updateProperties();
            properties.forEach(updateProperties::set);
            updateProperties.commit();
            LOG.info("表 {} 属性设置完成", table.name());
        }).onFailure(e -> LOG.error("设置表 {} 属性失败: {}", table.name(), e.getMessage(), e));
    }

    /**
     * 将非Upsert表转换为ClickHouse折叠数据流
     * 优化版 - 使用常量和枚举提高可读性
     *
     * @param tblEnv 表环境
     * @param tbl 表
     * @param isVersionedCollapseTable 是否支持版本
     * @return 数据流
     */
    public static SingleOutputStreamOperator<Row> convertNonUpsertTableToCHCollapseDS(StreamTableEnvironment tblEnv, Table tbl, boolean isVersionedCollapseTable) {

        final int SIGN_INDEX = 0;
        final int DATA_VERSION_INDEX = 1;
        final int INVALID_SIGN = -1;
        final int VALID_SIGN = 1;

        return tblEnv.toChangelogStream(tbl)
                .map(r -> {
                    // 创建初始行
                    Row retRow = new Row(RowKind.INSERT, isVersionedCollapseTable ? 2 : 1);

                    // 添加版本信息（如果需要）
                    if (isVersionedCollapseTable) {
                        retRow.setField(DATA_VERSION_INDEX, System.currentTimeMillis());
                    }

                    // 根据行类型设置符号
                    switch (r.getKind()) {
                        case DELETE:
                        case UPDATE_BEFORE:
                            retRow.setField(SIGN_INDEX, INVALID_SIGN);
                            break;
                        case UPDATE_AFTER:
                        case INSERT:
                            retRow.setField(SIGN_INDEX, VALID_SIGN);
                            break;
                    }

                    // 连接行
                    return Row.join(retRow, r);
                });
    }

    /**
     * 将非Upsert表转换为ClickHouse折叠表
     * 优化版 - 使用Flink最新API处理折叠表转换
     *
     * @param tblEnv 表环境
     * @param tbl 表
     * @param isVersionedCollapseTable 是否支持版本
     * @return 表
     */
    public static Table convertNonUpsertTableToCHCollapseTable(StreamTableEnvironment tblEnv, Table tbl, boolean isVersionedCollapseTable) {
        // 获取变更流
        SingleOutputStreamOperator<Row> dataStream = convertNonUpsertTableToCHCollapseDS(tblEnv, tbl, isVersionedCollapseTable);

        // 创建字段名称和类型数组
        List<Column> origColumns = tbl.getResolvedSchema().getColumns();

        // 创建新的Schema
        Schema.Builder schemaBuilder = newBuilder()
                .column("sign", DataTypes.INT());

        // 添加版本字段（如果需要）
        if (isVersionedCollapseTable) {
            schemaBuilder.column("version", DataTypes.BIGINT());
        }

        // 添加原始字段
        origColumns.forEach(col -> schemaBuilder.column(col.getName(), col.getDataType()));

        // 手动创建DataStream并添加到表环境
        return tblEnv.fromDataStream(
                dataStream,
                newBuilder().fromSchema(schemaBuilder.build()).build()
        );
    }

    /**
     * 将Flink Schema转换为ResolvedSchema
     * 修复使用弃用的TableSchema API
     *
     * @param schema Schema
     * @return ResolvedSchema
     */
    public static ResolvedSchema convertFlinkSchemaToResolvedSchema(Schema schema) {
        RowType rowType = convertFlinkSchemaToRowType(schema, true);
        List<Column> columns = new ArrayList<>();

        for (int i = 0; i < rowType.getFieldCount(); i++) {
            RowType.RowField field = rowType.getFields().get(i);
            DataType dataType = DataTypes.of(field.getType());
            columns.add(Column.physical(field.getName(), dataType));
        }

        return ResolvedSchema.of(columns);
    }

    /**
     * 在表中获取getSchemaString异常时，提供兜底方案
     *
     * @param strategy 表构建策略
     * @return Schema字符串
     */
    private static String getSchemaStringWithFallback(BaseTableBuilderStrategy strategy) {
        try {
            return strategy.getSchemaString();
        } catch (Exception e) {
            LOG.error("获取表结构失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取表结构失败，请检查表是否存在", e);
        }
    }

    /**
     * 兼容层 - 支持旧代码调用
     * 从Schema转换为ResolvedSchema
     *
     * @param schema 要转换的Schema
     * @return ResolvedSchema
     * @deprecated 请使用 convertFlinkSchemaToResolvedSchema 替代
     */
    @Deprecated
    public static org.apache.flink.table.api.TableSchema convertFlinkSchemaToTableSchema(Schema schema) {
        LOG.warn("调用了已弃用的convertFlinkSchemaToTableSchema方法，请改用convertFlinkSchemaToResolvedSchema");
        RowType rowType = convertFlinkSchemaToRowType(schema, true);

        // 使用ResolvedSchema和TableSchema的桥接代码
        ResolvedSchema resolvedSchema = convertFlinkSchemaToResolvedSchema(schema);
        DataType[] dataTypes = resolvedSchema.getColumnDataTypes().toArray(new DataType[0]);
        String[] fieldNames = resolvedSchema.getColumnNames().toArray(new String[0]);

        return org.apache.flink.table.api.TableSchema.builder()
                .fields(fieldNames, dataTypes)
                .build();
    }

    /**
     * 兼容层 - 支持旧代码调用
     * 将TableSchema转换为TypeInformation
     *
     * @param schema TableSchema
     * @return TypeInformation
     * @deprecated 请使用 convertSchemaToTypeInformation 替代
     */
    @Deprecated
    public static TypeInformation<RowData> convertTableSchemaToTypeInformation(org.apache.flink.table.api.TableSchema schema) {
        LOG.warn("调用了已弃用的convertTableSchemaToTypeInformation方法，请改用convertSchemaToTypeInformation");
        return createTypeInformation(
                schema.getFieldNames(),
                schema.toPhysicalRowDataType().getLogicalType().getChildren().toArray(new LogicalType[0])
        );
    }

    /**
     * 将ResolvedSchema转换为Schema
     *
     * @param resolvedSchema 已解析的模式
     * @return Schema
     */
    public static Schema resolvedSchemaToSchema(ResolvedSchema resolvedSchema) {
        Schema.Builder builder = Schema.newBuilder();

        // 添加所有列
        resolvedSchema.getColumns().forEach(col ->
                builder.column(col.getName(), col.getDataType())
        );

        // 添加主键约束
        if (resolvedSchema.getPrimaryKey().isPresent()) {
            builder.primaryKey(resolvedSchema.getPrimaryKey().get().getColumns());
        }

        return builder.build();
    }

    /**
     * 测试server ID范围生成功能
     * 用于验证生成的server ID范围是否唯一且有效，支持并行度
     *
     * @param testCount 测试次数
     * @return 测试结果摘要
     */
    public static String testServerIdGeneration(int testCount) {
        Set<String> generatedRanges = new HashSet<>();
        int validCount = 0;
        int duplicateCount = 0;
        long minRangeStart = Long.MAX_VALUE;
        long maxRangeEnd = Long.MIN_VALUE;

        for (int i = 0; i < testCount; i++) {
            String serverIdRange = generateUniqueServerId("test_table_" + i, "test_rdb_table_" + i);

            // 解析范围
            String[] parts = serverIdRange.split("-");
            if (parts.length == 2) {
                try {
                    long start = Long.parseLong(parts[0]);
                    long end = Long.parseLong(parts[1]);
                    minRangeStart = Math.min(minRangeStart, start);
                    maxRangeEnd = Math.max(maxRangeEnd, end);
                } catch (NumberFormatException ignored) {
                    // 忽略解析错误
                }
            }

            if (generatedRanges.contains(serverIdRange)) {
                duplicateCount++;
            } else {
                generatedRanges.add(serverIdRange);
            }

            if (isValidServerIdRange(serverIdRange)) {
                validCount++;
            }
        }

        return String.format("测试结果: 总数=%d, 有效=%d, 重复=%d, 唯一=%d, 范围跨度=[%d-%d], 每个范围大小=%d",
                testCount, validCount, duplicateCount, generatedRanges.size(),
                minRangeStart, maxRangeEnd, SERVER_ID_RANGE_SIZE);
    }

    /**
     * 行为状态枚举，用于表示数据的有效性
     */
    private enum SignStatus {
        INVALID(-1),
        VALID(1);

        private final int value;

        SignStatus(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 线程池配置常量，避免硬编码
     */
    private static final class ThreadPoolConfig {

        private static final int CORE_POOL_SIZE = 10;
        private static final int MAX_POOL_SIZE = 60;
        private static final long KEEP_ALIVE_TIME = 0;
        private static final int QUEUE_CAPACITY = Integer.MAX_VALUE;
        private static final TimeUnit TIME_UNIT = MILLISECONDS;
        private static final long DEFAULT_TIMEOUT_MINUTES = 5;
    }

    /**
     * SQL格式化模板常量
     */
    private static final class SqlTemplates {

        private static final String INSERT_INTO = "INSERT INTO %s SELECT %s FROM %s";
        private static final String INSERT_INTO_WITH_HINTS = "INSERT INTO %s %s SELECT %s FROM %s";
        private static final String SINK_TO_HBASE = "INSERT INTO %s %s SELECT `rowkey`, ROW(`data`) FROM %s";
        private static final String ROW_TO_JSON = "SELECT (%s) AS `rowkey`, JSON_OBJECT(%s) AS `data` FROM %s";
    }

    /**
     * 表操作提示常量
     */
    private static final class TableHints {

        private static final String HBASE_PARALLEL_HINTS = "/*+ OPTIONS('sink.parallelism'='8') */";
        private static final String ICEBERG_HINTS = "/*+ OPTIONS('sink.parallelism'='1', 'upsert-enabled'='true') */";
    }
}