package com.tencent.andata.utils;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 实现LRUCache
 *
 * @param <K> key
 * @param <V> value
 */
public class LRUCache<K, V> extends LinkedHashMap<K, V> {
    private final int capacity;

    public LRUCache(int capacity) {
        // 指定accessOrder为true，则按照访问时间进行排序
        super(capacity, 0.75f, true);
        this.capacity = capacity;
    }

    @Override
    protected boolean removeEldestEntry(Map.Entry<K, V> eldest) {
        // 超过容量则删除
        return size() > capacity;
    }

    public static void main(String[] args) {
        final LRUCache<String, String> cache = new LRUCache<>(1);
        cache.put("1", "1");
        cache.put("2", "2");
        // {2=2}
        System.out.println(cache);
    }
}
