package com.tencent.andata.utils.udf;

import org.apache.flink.table.functions.ScalarFunction;

import java.sql.Timestamp;

public class StringTimestampTransform {
    public static class StringToLong extends ScalarFunction {
        public Long eval(String dateTimeString) {
            return Timestamp.valueOf(dateTimeString.substring(0, 19)).getTime();
        }

        public Long eval(String dateTimeString, String tz) {
            dateTimeString = dateTimeString.substring(0, 19);
            long timestamp = Timestamp.valueOf(dateTimeString.substring(0, 19)).getTime();
            if ("UTC".equalsIgnoreCase(tz)) {
                return timestamp - 28800000;
            } else if ("CST".equalsIgnoreCase(tz)) {
                return timestamp + 28800000;
            }
            return timestamp;
        }
    }

    public static class StringToSqlTimestamp extends ScalarFunction {
        public Timestamp eval(String dateTimeString) {
            return Timestamp.valueOf(dateTimeString.substring(0, 19));
        }

        public Timestamp eval(String dateTimeString, String tz) {
            dateTimeString = dateTimeString.substring(0, 19);
            long timestamp = Timestamp.valueOf(dateTimeString.substring(0, 19)).getTime();
            if ("UTC".equalsIgnoreCase(tz)) {
                return new Timestamp(timestamp - 28800000);
            } else if ("CST".equalsIgnoreCase(tz)) {
                return new Timestamp(timestamp + 28800000);
            }
            return new Timestamp(timestamp);
        }
    }
}
