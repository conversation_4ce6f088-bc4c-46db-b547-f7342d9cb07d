package com.tencent.andata.utils.operator.map;

import com.tencent.andata.utils.RowDataSplitCleanFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.LogicalType;

import java.util.ArrayList;


/**
 * 根据构造函数
 */
public class RowDataSplitCleanMap implements MapFunction<RowData, RowData> {


    private ArrayList<Tuple2<RowData.FieldGetter, LogicalType>> fieldGetterList;

    public RowDataSplitCleanMap(ArrayList<Tuple2<RowData.FieldGetter, LogicalType>> fieldGetterList) {
        this.fieldGetterList = fieldGetterList;
    }

    @Override
    public RowData map(RowData rowData) throws Exception {
        return RowDataSplitCleanFunction.map(rowData, fieldGetterList);
    }
}