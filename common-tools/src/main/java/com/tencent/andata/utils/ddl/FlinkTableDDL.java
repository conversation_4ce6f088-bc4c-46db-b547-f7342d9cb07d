package com.tencent.andata.utils.ddl;

import com.tencent.andata.utils.ddl.strategy.BaseTableBuilderStrategy;

import java.util.ArrayList;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FlinkTableDDL {

    Logger logger = LoggerFactory.getLogger(FlinkTableDDL.class);

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private String flinkTableName;
        private String flinkTableComment;
        private String flinkTableSchema;
        private ArrayList<String> flinkTablePartitionFields;
        private BaseTableBuilderStrategy tableBuilderStrategy;
        private String processTimeField;

        public Builder flinkTableName(String tableName) {
            this.flinkTableName = tableName;
            return this;
        }

        public Builder flinkTableSchema(String flinkTableSchema) {
            this.flinkTableSchema = flinkTableSchema;
            return this;
        }

        public Builder flinkTableComment(String flinkTableComment) {
            this.flinkTableComment = flinkTableComment;
            return this;
        }

        public Builder processTimeFiled(String field) {
            this.processTimeField = field;
            return this;
        }

        public Builder flinkTablePartitionFields(ArrayList<String> partitionFields) {
            this.flinkTablePartitionFields = partitionFields;
            return this;
        }

        public Builder tableBuilderStrategy(BaseTableBuilderStrategy tableBuilderStrategy) {
            this.tableBuilderStrategy = tableBuilderStrategy;
            return this;
        }

        /**
         * 构造Flink DDL
         *
         * @return
         * @throws TableNotExistException
         */
        public String build() throws TableNotExistException {
            String fTableSchema = "";
            if (!StringUtils.isBlank(flinkTableSchema)) {
                fTableSchema = flinkTableSchema;
            }
            else {
                fTableSchema = tableBuilderStrategy.getSchemaString().trim();
                if (this.processTimeField != null) {
                    String procTimeDDL = String.format("%s AS PROCTIME()", this.processTimeField);
                    fTableSchema = String.format(
                            "%s  ,%s\n)",
                            fTableSchema.substring(0, fTableSchema.length() - 1),
                            procTimeDDL
                    );
                }
            }
            return String.format("CREATE TABLE %s\n %s\n %s\n %s WITH (\n" + "%s\n" + ")",
                    flinkTableName,
                    fTableSchema,
                    flinkTableComment == null ? "" : String.format("COMMENT %s", flinkTableComment),
                    flinkTablePartitionFields == null ? "" : String.format(
                            "PARTITIONED BY (%s)",
                            String.join(",", flinkTablePartitionFields)
                    ),
                    tableBuilderStrategy.getPropertyString()
            );
        }
    }
}