package com.tencent.andata.utils.struct;

import com.google.common.base.CaseFormat;
import com.tencent.andata.singularity.settings.Settings;
import com.tencent.andata.utils.Map2ObjectReflector;
import java.io.Serializable;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.experimental.Tolerate;

@Data
@Builder
public class DatabaseConf implements Serializable {

    public String dbHost;
    public String dbName;
    public Integer dbPort;
    public String userName;
    public String password;

    @Tolerate
    public DatabaseConf() {
    }

    @SneakyThrows
    public static DatabaseConf fromSettings(String path) throws RuntimeException {
        // Rainbow配置的都是UPPER_UNDERSCORE，但Java的格式是LOWER_CAMEL
        // TODO: 这里做这个主要是为了兼容旧的逻辑。这块基本就是个Bean
        Map<String, String> settings = Settings
                .extractAll(path)
                .entrySet()
                .stream()
                .collect(
                        Collectors.toMap(
                                entry -> CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, entry.getKey()),
                                Map.Entry::getValue
                        )
                );
        return new Map2ObjectReflector<>(DatabaseConf.class).build(settings);
    }

    @Override
    public String toString() {
        return "DatabaseConf{"
                + "dbHost='" + dbHost + '\''
                + ", dbName='" + dbName + '\''
                + ", dbPort=" + dbPort
                + ", userName='" + userName + '\''
                + ", password='" + password + '\''
                + '}';
    }

}
