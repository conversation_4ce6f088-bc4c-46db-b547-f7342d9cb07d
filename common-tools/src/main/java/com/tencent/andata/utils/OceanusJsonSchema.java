package com.tencent.andata.utils;

import static org.apache.flink.table.api.DataTypes.BIGINT;
import static org.apache.flink.table.api.DataTypes.FIELD;
import static org.apache.flink.table.api.DataTypes.Field;
import static org.apache.flink.table.api.DataTypes.INT;
import static org.apache.flink.table.api.DataTypes.ROW;
import static org.apache.flink.table.api.DataTypes.STRING;

import java.util.ArrayList;
import java.util.List;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.RowType;

// 格式与Oceanus保持一致，降低后续切换成本 https://iwiki.woa.com/pages/viewpage.action?pageId=793342354
// 通过反射的方式塞进private需要有getter/setter，public则不需要

public class OceanusJsonSchema {

    public String type;
    public String[] fieldNames;
    public OceanusJsonSchemaFieldFormat[] fieldFormats;

    // 禁止类直接实例化，但通过反射可以创建对象，正好复合Json场景
    private OceanusJsonSchema() {
    }

    public DataType convertToDataType() {
        List<Field> fields = new ArrayList<>();
        if (fieldNames.length != fieldFormats.length) {
            throw new IllegalArgumentException(
                    ""
                            + "the length of field list "
                            + "is not equal to the length of "
                            + "field format list.");
        }

        for (int index = 0; index < fieldNames.length; index++) {
            fields.add(FIELD(fieldNames[index], convertField(fieldFormats[index])));
        }
        return ROW(fields.toArray(new Field[]{}));
    }

    public RowType convertToRowType() {
        return (RowType) convertToDataType().getLogicalType();
    }

    private DataType convertField(OceanusJsonSchemaFieldFormat fieldFormat) {
        switch (fieldFormat.type) {
            case "int":
                return INT();
            case "string":
                return STRING();
            case "long":
                return BIGINT();
            default:
                throw new IllegalArgumentException(
                        String.format("No type %s is listed in switch case.", fieldFormat.type));
        }
    }

    public static class OceanusJsonSchemaFieldFormat {

        public String type;

        private OceanusJsonSchemaFieldFormat() {
        }
    }
}
