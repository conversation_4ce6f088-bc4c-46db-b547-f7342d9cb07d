package com.tencent.andata.utils.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;


/**
 * 一行拆分多行
 */
public class ArrayToStringUDTF extends ScalarFunction {

    /**
     * 执行函数
     * @param array
     * @return
     */
    public String eval(String array, int index) {
        if (StringUtils.isEmpty(array) || array.length() < 10 || !array.contains(",")) {
            return "";
        }
        if (array.equals("[]") || array.equals("[\"\",\"\"]") || array.equals("[null,null]")) {
            return "";
        }
        // 去掉首尾的方括号
        array = array.substring(1, array.length() - 1);
        // 使用逗号分隔符将字符串分割为数组
        String[] resultArray = array.split("\",\"");
        // 去掉首尾的双引号
        for (int i = 0; i < resultArray.length; i++) {
            resultArray[i] = resultArray[i].replaceAll("^\"|\"$", "");
        }
        return resultArray[index];
    }


}
