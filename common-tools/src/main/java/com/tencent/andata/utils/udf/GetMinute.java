package com.tencent.andata.utils.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

public class GetMinute extends ScalarFunction {

    /**
     * 执行函数
     * @param time
     * @return
     */
    public int eval(String time) {
        if (StringUtils.isEmpty(time)) {
            return 0;
        }
        String[] timeList = time.split(":");
        if (timeList.length <= 1) {
            return 0;
        }
        int hour = Integer.parseInt(timeList[0]);
        int min = Integer.parseInt(timeList[1]);
        if (hour < 0) {
            hour = 0;
        }
        if (min < 0) {
            min = 0;
        }
        return hour * 60 + min;
    }

    /*public static void main(String[] arge){
        System.out.println(new GetMinute().eval("00:00:11"));
    }*/

}
