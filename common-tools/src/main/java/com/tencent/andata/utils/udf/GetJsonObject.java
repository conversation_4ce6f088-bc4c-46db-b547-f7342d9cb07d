package com.tencent.andata.utils.udf;

import com.google.common.collect.Iterators;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonFactory;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonParser.Feature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JavaType;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.type.TypeFactory;
import org.apache.flink.table.functions.ScalarFunction;

public class GetJsonObject extends ScalarFunction {
  private static final Pattern patternKey = Pattern.compile("^([a-zA-Z0-9_\\-:\\s]+).*");
  private static final Pattern patternIndex = Pattern.compile("\\[([0-9]+|\\*)]");

  public static final JsonFactory JSON_FACTORY = new JsonFactory();
  public static final ObjectMapper MAPPER = new ObjectMapper(JSON_FACTORY);
  public static final JavaType MAP_TYPE =
      TypeFactory.defaultInstance().constructMapType(Map.class, Object.class, Object.class);
  public static final JavaType LIST_TYPE =
      TypeFactory.defaultInstance().constructRawCollectionType(List.class);

  static {
    // Allows for unescaped ASCII control characters in JSON values
    JSON_FACTORY.enable(Feature.ALLOW_UNQUOTED_FIELD_NAMES);
  }

  // An LRU cache using a linked hash map
  static class HashCache<K, V> extends LinkedHashMap<K, V> {

    private static final int CACHE_SIZE = 16;
    private static final int INIT_SIZE = 32;
    private static final float LOAD_FACTOR = 0.6f;

    HashCache() {
      super(INIT_SIZE, LOAD_FACTOR);
    }

    private static final long serialVersionUID = 1;

    @Override
    protected boolean removeEldestEntry(Map.Entry<K, V> eldest) {
      return size() > CACHE_SIZE;
    }
  }

  Map<String, Object> extractObjectCache = new HashCache<>();
  Map<String, String[]> pathExprCache = new HashCache<>();
  Map<String, ArrayList<String>> indexListCache = new HashCache<>();
  Map<String, String> mKeyGroup1Cache = new HashCache<>();
  Map<String, Boolean> mKeyMatchesCache = new HashCache<>();

  public GetJsonObject() {}

  /**
   * 执行函数
   * @param jsonString
   * @param pathString
   * @return
   */
  public String eval(String jsonString, String pathString) {
    if (jsonString == null
        || jsonString.isEmpty()
        || pathString == null
        || pathString.isEmpty()
        || pathString.charAt(0) != '$') {
      return null;
    }

    int pathExprStart = 1;
    boolean unknownType = pathString.equals("$");
    boolean isRootArray = false;

    if (pathString.length() > 1) {
      if (pathString.charAt(1) == '[') {
        pathExprStart = 0;
        isRootArray = true;
      } else if (pathString.charAt(1) == '.') {
        isRootArray = pathString.length() > 2 && pathString.charAt(2) == '[';
      } else {
        return null;
      }
    }

    // Cache pathExpr
    String[] pathExpr = pathExprCache.computeIfAbsent(pathString, s -> s.split("\\.", -1));

    // Cache extractObject
    Object extractObject = extractObjectCache.get(jsonString);
    if (extractObject == null) {
      JavaType javaType = isRootArray ? LIST_TYPE : MAP_TYPE;
      try {
        extractObject = MAPPER.readValue(jsonString, javaType);
      } catch (Exception e) {
        return null;
      }
      extractObjectCache.put(jsonString, extractObject);
    }

    for (int i = pathExprStart; i < pathExpr.length; i++) {
      if (extractObject == null) {
        return null;
      }
      extractObject = extract(extractObject, pathExpr[i], i == pathExprStart && isRootArray);
    }

    String result;
    if (extractObject instanceof Map || extractObject instanceof List) {
      try {
        result = MAPPER.writeValueAsString(extractObject);
      } catch (Exception e) {
        return null;
      }
    } else if (extractObject != null) {
      result = extractObject.toString();
    } else {
      return null;
    }
    return result;
  }

  private Object extract(Object json, String path, boolean skipMapProc) {
    // skip MAP processing for the first path element if root is array
    if (!skipMapProc) {
      // Cache patternkey.matcher(path).matches()
      Matcher mKey = null;
      Boolean mKeyMatches = mKeyMatchesCache.get(path);
      if (mKeyMatches == null) {
        mKey = patternKey.matcher(path);
        mKeyMatches = mKey.matches() ? Boolean.TRUE : Boolean.FALSE;
        mKeyMatchesCache.put(path, mKeyMatches);
      }
      if (!mKeyMatches) {
        return null;
      }

      // Cache mkey.group(1)
      String mKeyGroup1 = mKeyGroup1Cache.get(path);
      if (mKeyGroup1 == null) {
        if (mKey == null) {
          mKey = patternKey.matcher(path);
          mKeyMatches = mKey.matches() ? Boolean.TRUE : Boolean.FALSE;
          mKeyMatchesCache.put(path, mKeyMatches);
          if (!mKeyMatches) {
            return null;
          }
        }
        mKeyGroup1 = mKey.group(1);
        mKeyGroup1Cache.put(path, mKeyGroup1);
      }
      json = extractJsonWithKey(json, mKeyGroup1);
    }
    // Cache indexList
    ArrayList<String> indexList = indexListCache.get(path);
    if (indexList == null) {
      Matcher mIndex = patternIndex.matcher(path);
      indexList = new ArrayList<>();
      while (mIndex.find()) {
        indexList.add(mIndex.group(1));
      }
      indexListCache.put(path, indexList);
    }

    if (indexList.size() > 0) {
      json = extractJsonWithindex(json, indexList);
    }

    return json;
  }

  private final transient AddingList jsonList = new AddingList();

  private static class AddingList extends ArrayList<Object> {
    private static final long serialVersionUID = 1L;

    @Override
    public java.util.Iterator<Object> iterator() {
      return Iterators.forArray(toArray());
    }

    @Override
    public void removeRange(int fromIndex, int toIndex) {
      super.removeRange(fromIndex, toIndex);
    }
  }

  @SuppressWarnings("unchecked")
  private Object extractJsonWithindex(Object json, ArrayList<String> indexList) {
    jsonList.clear();
    jsonList.add(json);
    for (String index : indexList) {
      int targets = jsonList.size();
      if (index.equalsIgnoreCase("*")) {
        for (Object array : jsonList) {
          if (array instanceof List) {
            jsonList.addAll((List<Object>) array);
          }
        }
      } else {
        for (Object array : jsonList) {
          int indexValue = Integer.parseInt(index);
          if (!(array instanceof List)) {
            continue;
          }
          List<Object> list = (List<Object>) array;
          if (indexValue >= list.size()) {
            continue;
          }
          jsonList.add(list.get(indexValue));
        }
      }
      if (jsonList.size() == targets) {
        return null;
      }
      jsonList.removeRange(0, targets);
    }
    if (jsonList.isEmpty()) {
      return null;
    }
    return (jsonList.size() > 1) ? new ArrayList<>(jsonList) : jsonList.get(0);
  }

  @SuppressWarnings("unchecked")
  private Object extractJsonWithKey(Object json, String path) {
    if (json instanceof List) {
      List<Object> jsonArray = new ArrayList<>();
      for (int i = 0; i < ((List<Object>) json).size(); i++) {
        Object jsonElem = ((List<Object>) json).get(i);
        Object jsonObj;
        if (jsonElem instanceof Map) {
          jsonObj = ((Map<String, Object>) jsonElem).get(path);
        } else {
          continue;
        }
        if (jsonObj instanceof List) {
          jsonArray.addAll((List<Object>) jsonObj);
        } else if (jsonObj != null) {
          jsonArray.add(jsonObj);
        }
      }
      return (jsonArray.size() == 0) ? null : jsonArray;
    } else if (json instanceof Map) {
      return ((Map<String, Object>) json).get(path);
    } else {
      return null;
    }
  }
}
