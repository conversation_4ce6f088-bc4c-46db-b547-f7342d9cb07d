package com.tencent.andata.utils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Modifier;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Map2ObjectReflector<T> {
    private static final Logger LOG = LoggerFactory.getLogger(Map2ObjectReflector.class);

    private final Constructor<? extends T> ctor;
    private final Class<? extends T> impl;

    public Map2ObjectReflector(Class<? extends T> impl) throws NoSuchMethodException {
        this.impl = impl;
        ctor = impl.getConstructor();
    }

    /**
     * @param kvData
     * @return
     * @throws InvocationTargetException
     * @throws InstantiationException
     * @throws IllegalAccessException
     */
    public T build(Map<String, String> kvData)
            throws InvocationTargetException, InstantiationException, IllegalAccessException {

        T retInstance = ctor.newInstance();
        // 反射读对应的属性然后赋值
        for (Field field : impl.getDeclaredFields()) {
            // 这里属于是特殊使用，最好还是不要破坏屏蔽性
            int modifiers = field.getModifiers();

            // 对于静态的、Final、Private、Transit，直接跳过

            if (Modifier.isPublic(modifiers)
                    && !Modifier.isFinal(modifiers)
                    && !Modifier.isStatic(modifiers)
                    && !Modifier.isTransient(modifiers)
            ) {
                String fieldValue = kvData.get(field.getName());
                field.setAccessible(true);
                try {
                    if (Integer.class.getTypeName().equals(field.getGenericType().getTypeName())) {
                        field.set(retInstance, Integer.parseInt(fieldValue));
                    } else {
                        field.set(retInstance, fieldValue);
                    }
                } catch (Exception e) {
                    LOG.error("err:: {}. Field Name: {}, Value::{}", e, field.getName(), fieldValue);
                    throw e;
                }
            }
        }
        return retInstance;
    }
}