package com.tencent.andata.utils;


import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

public class ExceptionWrapperUtil {

    public static <T> Consumer<T> consumer(ExceptionalConsumer<T> c) {
        return t -> {
            try {
                c.accept(t);
            } catch (Throwable e) {
                throw uncheck(e);
            }
        };
    }

    public static <T, R> Function<T, R> function(ExceptionalFunction<T, R> f) {
        return t -> {
            try {
                return f.apply(t);
            } catch (Throwable e) {
                throw uncheck(e);
            }
        };
    }

    public static <T> Supplier<T> supplier(ExceptionalSupplier<T> s) {
        return () -> {
            try {
                return s.get();
            } catch (Throwable e) {
                throw uncheck(e);
            }
        };
    }


    private static RuntimeException uncheck(Throwable t) {
        if (t instanceof Error) {
            throw (Error) t;
        }
        return t instanceof RuntimeException ? (RuntimeException) t : new RuntimeException(t);
    }

    @FunctionalInterface
    public interface ExceptionalConsumer<T> {

        void accept(T t) throws Throwable;
    }

    @FunctionalInterface
    public interface ExceptionalFunction<T, R> {

        R apply(T t) throws Throwable;
    }

    @FunctionalInterface
    public interface ExceptionalSupplier<T> {

        T get() throws Throwable;
    }
}