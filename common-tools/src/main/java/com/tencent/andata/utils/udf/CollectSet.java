package com.tencent.andata.utils.udf;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.flink.table.functions.AggregateFunction;


public class CollectSet extends AggregateFunction<String[], List<String>> {

    public void accumulate(List<String> acc, String column) {
        acc.add(column);
    }

    public void retract(List<String> acc, String column) {
        acc.remove(column);
    }

    @Override
    public String[] getValue(List list) {
        Set<String> set = new HashSet<String>(list);
        return (String[]) set.toArray(new String[0]);
    }

    @Override
    public List<String> createAccumulator() {
        return new ArrayList<>();
    }


    public void resetAccumulator(List<String> acc) {
        acc.clear();
    }
}
