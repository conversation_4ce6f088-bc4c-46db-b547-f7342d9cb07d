package com.tencent.andata.utils.ddl.strategy;

import com.tencent.andata.utils.connector.jdbc.catalog.JdbcCatalogUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.util.HashMap;
import java.util.Random;
import org.apache.flink.table.catalog.ObjectPath;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;

public class CDCTableBuilderStrategy extends BaseTableBuilderStrategy {

    private final DatabaseConf databaseConf;
    private final DatabaseEnum databaseEnum;
    private final String dbTableName;

    /**
     * PG CDC TABLE 生成策略
     *
     * @param dbTableName
     * @param databaseEnum
     * @param databaseConf
     * @throws Exception
     */
    public CDCTableBuilderStrategy(String dbTableName, DatabaseEnum databaseEnum, DatabaseConf databaseConf)
            throws Exception {
        this.dbTableName = dbTableName;
        this.databaseConf = databaseConf;
        this.databaseEnum = databaseEnum;
        int random = 1000 + new Random().nextInt(9000);
        this.tableProperties = new HashMap<String, String>() {{
            put("connector", String.format("%s-cdc", databaseEnum.toString()));
            put("port", databaseConf.dbPort.toString());
            put("database-name", databaseConf.dbName);
            put("username", databaseConf.userName);
            put("password", databaseConf.password);
            put("table-name", dbTableName);
            put("hostname", databaseConf.dbHost);
        }};
        if (databaseEnum == DatabaseEnum.PGSQL) {
            this.tableProperties.put("connector", "postgres-cdc");
            this.tableProperties.put("schema-name", "public");
            this.tableProperties.put(
                    "slot.name",
                    String.format(
                            "%s_%s",
                            databaseConf.dbName,
                            dbTableName
                    )
            );
        }
    }


    /**
     * CDC Table的生成策略
     */
    public CDCTableBuilderStrategy(String dbTableName, DatabaseEnum databaseEnum, DatabaseConf databaseConf,
            String partitionPattern)
            throws Exception {
        this.dbTableName = dbTableName;
        this.databaseConf = databaseConf;
        this.databaseEnum = databaseEnum;
        this.tableProperties = new HashMap<String, String>() {{
            put("connector", String.format("%s-cdc", databaseEnum.toString()));
            put("port", databaseConf.dbPort.toString());
            put("database-name", databaseConf.dbName);
            put("username", databaseConf.userName);
            put("password", databaseConf.password);
            put("table-name", dbTableName.concat(partitionPattern));
            put("hostname", databaseConf.dbHost);
        }};
        if (databaseEnum == DatabaseEnum.MYSQL) {
            this.tableProperties.put("connector", "mysql-cdc");
        } else if (databaseEnum == DatabaseEnum.PGSQL) {
            this.tableProperties.put("connector", "postgres-cdc");
            this.tableProperties.put("schema-name", "public");
//            this.tableProperties.put("debezium.slot.drop.on.stop", "true");
            this.tableProperties.put("slot.name", String.format("%s_%s_sql_cdc", databaseConf.dbName, dbTableName));
        } else {
            throw new Exception("Only support PGSQL and MYSQL CDC Connector!");
        }
    }

    public CDCTableBuilderStrategy property(String key, String value) {
        this.tableProperties.put(key, value);
        return this;
    }

    @Override
    public String getSchemaString() throws TableNotExistException {
        return JdbcCatalogUtils.createCatalog(
                        "cdc_catalog",
                        databaseConf.dbName,
                        databaseConf.userName,
                        databaseConf.password,
                        String.format("jdbc:%s://%s:%d/",
                                databaseEnum.toString(),
                                databaseConf.dbHost,
                                databaseConf.dbPort),
                        this.databaseEnum
                )
                .getTable(new ObjectPath(databaseConf.dbName, dbTableName))
                .getUnresolvedSchema().toString();
    }


    public CDCTableBuilderStrategy propertyVersion(String value) {
        this.tableProperties.put("property-version", value);
        return this;
    }

    public CDCTableBuilderStrategy serverTimeZone(String value) {
        this.tableProperties.put("server-time-zone", value);
        return this;
    }

    public CDCTableBuilderStrategy serverId(String value) {
        this.tableProperties.put("server-id", value);
        return this;
    }
}
