package com.tencent.andata.utils.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Arrays;

public class GetMidStringListToInt  extends ScalarFunction {

    /**
     * 中位数计算
     * @param midList 待计算中位数的 List
     * @return 返回中位数
     */
    public int eval(String midList) {
        int result = 0;
        if (StringUtils.isEmpty(midList)) {
            return 0;
        }
        String[] elements = midList.split(","); // 拆分字符串
        // 将字符串数组转换为整数数组
        int[] intArray = Arrays.stream(elements)
                .mapToInt(Integer::parseInt)
                .toArray();

        // 过滤非零项
        int[] nonZeroArray = Arrays.stream(intArray)
                .filter(num -> num > 0)
                .toArray();

        Arrays.sort(nonZeroArray);

        if (nonZeroArray.length % 2 == 0 && nonZeroArray.length > 0) {
            int middleIndex = nonZeroArray.length / 2;
            result = (nonZeroArray[middleIndex - 1] + nonZeroArray[middleIndex]) / 2;
        } else if (nonZeroArray.length % 2 != 0) {
            int middleIndex = nonZeroArray.length / 2;
            result = nonZeroArray[middleIndex];
        }
        return result;
    }
}