package com.tencent.andata.utils.cdc.conf.sink;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.apache.flink.table.types.logical.RowType;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 当dst字段数量小于src并且字段顺序不一致时会有问题
 */
@Deprecated
public class SwapChain implements Serializable {

    public LinkedList<Integer> posChain;

    /**
     * 一个SwapChain表示一条字段调换的链路
     * 如 1->4->3->2->1 表示
     * 第一个位置的数据放到第四个，第四个位置的数据放到第三个，... ，第二个位置的数据放到第一个
     * PS：第一个和最后一个数字必须相等
     */
    public SwapChain() {
        this.posChain = new LinkedList<Integer>();
    }

    /**
     * 获取Swap链
     *
     * @param srcRowType
     * @param dstRowType
     * @return
     */
    public static ArrayList<SwapChain> getChangeRuleFromRowType(RowType srcRowType, RowType dstRowType) {
        Set<String> diffSet = new HashSet<>(dstRowType.getFieldNames());
        diffSet.removeAll(new HashSet<>(srcRowType.getFieldNames()));
        // Guard Cause，防止dst字段数量比src多
        assert dstRowType.getFieldCount() <= srcRowType.getFieldCount() && diffSet.isEmpty() : "DstRowType Should "
                + "not contain column that src doesnt have";

        ArrayList<SwapChain> res = new ArrayList<>();
        // sink表的字段名和字段位置的映射 {a, b, c} -> {a:0, b:1, c:2}
        HashMap<String, Integer> sinkPosMap = new HashMap<>();
        for (int i = 0; i < srcRowType.getFields().size(); i++) {
            RowType.RowField rowField = srcRowType.getFields().get(i);
            sinkPosMap.put(rowField.getName(), i);
        }
        // 获取Src表的所有位置信息Set, 如果sink表的字段数量为5 -> posSet = {0, 1, 2, 3, 4}
        List<Integer> posSet = IntStream
                .range(0, dstRowType.getFieldCount() - 1)
                .boxed()
                .collect(Collectors.toList());
        // 依次处理表位置信息，处理一个消除一个，直到处理完
        while (!posSet.isEmpty()) {
            SwapChain sc = new SwapChain();
            // 首先获取第一个位置
            Integer srcPos = posSet.get(0);
            // 获取首个位置的字段名
            String fieldName = dstRowType.getFieldNames().get(srcPos);
            // 获取字段在Sink表的位置
            Integer sinkPos = sinkPosMap.get(fieldName);
            // 字段名对应的位置相同，不用置换
            if (srcPos.equals(sinkPos)) {
                posSet.remove(srcPos);
                continue;
            }
            // swapChain增加位置
            sc.add(srcPos);
            sc.add(sinkPos);
            // 位置信息清除
            posSet.remove(srcPos);
            posSet.remove(sinkPos);
            // 使用startPos记录首个位置，因为一条SwapChain必须首尾数字相同
            Integer startPos = srcPos;
            // 使用nextPos记录下一个位置信息，后续根据nextPos和startPos是否相等判断一条SwapChain是否完成
            Integer nextPos = sinkPos;
            // nextPos和startPos相同则一条SwapChain完成
            while (!startPos.equals(nextPos)) {
                srcPos = nextPos;
                // 获取下一个元素的字段名
                fieldName = dstRowType.getFieldNames().get(srcPos);
                // 获取下一个元素应该放的位置
                sinkPos = sinkPosMap.get(fieldName);
                // nextPos信息更新
                nextPos = sinkPos;
                // swapChain增加位置
                sc.add(sinkPos);
                // 位置信息剔除
                posSet.remove(sinkPos);
            }
            res.add(sc);
        }
        return res;
    }

    public void add(Integer i) {
        this.posChain.add(i);
    }

    public String toString() {
        return this.posChain.stream().map(String::valueOf).collect(Collectors.joining("->"));
    }
}