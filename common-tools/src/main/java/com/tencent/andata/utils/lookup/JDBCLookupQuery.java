package com.tencent.andata.utils.lookup;

import org.apache.flink.util.Preconditions;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Enumeration;
import java.util.List;
import java.util.Properties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JDBC查询抽象类，提供基于JDBC的数据库查询功能
 * 支持连接管理、查询重试、异常处理等特性
 *
 * @param <IN>  输入参数类型
 * @param <OUT> 输出结果类型
 */
public abstract class JDBCLookupQuery<IN, OUT> implements LookupQueryable<IN, OUT>, Serializable {
    private static final Logger LOG = LoggerFactory.getLogger(JDBCLookupQuery.class);
    
    /*
     常量数据，暂时没配置化
     */
    /** 检测链接是否有效的超时时间（秒） */
    private static final int CONNECTION_CHECK_TIMEOUT_SECONDS = 5;
    /** 查询失败时的最大重试次数 */
    private static final int RETRY_COUNT = 3;
    
    /*
    链接属性
     */
    /** 数据库连接URL */
    protected String url;
    /** 数据库用户名 */
    protected String userName;
    /** 数据库密码 */
    protected String password;
    /** JDBC驱动类名 */
    protected String driverName;
    /** 已加载的JDBC驱动实例 */
    protected transient Driver loadedDriver;
    /** 数据库连接实例 */
    protected transient Connection connection;
    /** 预编译SQL语句 */
    protected transient PreparedStatement statement;

    /**
     * 默认构造函数
     */
    public JDBCLookupQuery() {
    }

    /**
     * 带参数的构造函数
     *
     * @param driverName JDBC驱动类名
     * @param url        数据库连接URL
     * @param userName   数据库用户名
     * @param password   数据库密码
     */
    public JDBCLookupQuery(String driverName, String url, String userName, String password) {
        this.driverName = driverName;
        this.url = url;
        this.userName = userName;
        this.password = password;
    }

    /**
     * 构建预编译SQL语句
     * 子类需要实现此方法以创建特定的PreparedStatement
     *
     * @param conn 数据库连接
     * @return 预编译的SQL语句
     * @throws SQLException 如果创建语句失败
     */
    public abstract PreparedStatement buildStatement(Connection conn) throws SQLException;

    /**
     * 执行单条记录查询
     * 子类需要实现此方法以执行具体的查询逻辑
     *
     * @param sts 预编译SQL语句
     * @param in  输入参数
     * @return 查询结果
     * @throws Exception 如果查询过程中发生错误
     */
    public abstract OUT doQuery(PreparedStatement sts, IN in) throws Exception;

    /**
     * 执行多条记录查询
     * 子类需要实现此方法以执行具体的批量查询逻辑
     *
     * @param sts 预编译SQL语句
     * @param in  输入参数
     * @param all 是否查询所有记录
     * @param cnt 查询记录数量限制（当all为false时有效）
     * @return 查询结果列表
     * @throws Exception 如果查询过程中发生错误
     */
    public abstract List<OUT> doQueryList(PreparedStatement sts, IN in, boolean all, int cnt) throws Exception;

    /**
     * 初始化数据库连接和预编译语句
     * 在首次使用前必须调用此方法
     *
     * @throws SQLException           如果数据库连接失败
     * @throws ClassNotFoundException 如果JDBC驱动类未找到
     */
    public void open() throws SQLException, ClassNotFoundException {
        Preconditions.checkNotNull(driverName);
        Preconditions.checkNotNull(url);
        Preconditions.checkNotNull(userName);
        Preconditions.checkNotNull(password);
        this.establishConnectionAndStatement();
    }

    /**
     * 关闭数据库连接和相关资源
     * 使用完毕后必须调用此方法释放资源
     *
     * @throws SQLException 如果关闭资源失败
     */
    public void close() throws SQLException {
        if (this.statement != null) {
            this.statement.close();
        }
        this.closeConnection();
    }

    /**
     * 查询单条记录
     * 实现重试机制，在连接失效时会自动重建连接
     *
     * @param in 输入参数
     * @return 查询结果
     * @throws RuntimeException 如果所有重试都失败
     */
    @Override
    public OUT query(IN in) throws RuntimeException {
        int retry = 0;
        // 允许重试
        for (; retry <= RETRY_COUNT; retry++) {
            try {
                // 清空statement参数
                this.statement.clearParameters();
                // 子类设置参数 & 查询 & 解析
                return this.doQuery(statement, in);
            } catch (Exception e) {
                LOG.error("[JDBCLookupQuery]JDBC execute query error, retry times = {}, error: {}", retry, e.getMessage());
                if (retry == RETRY_COUNT) {
                    throw new RuntimeException("[JDBCLookupQuery] Execution of JDBC statement failed.", e);
                }

                try {
                    // 链接失效则重建
                    if (!isConnectionValid()) {
                        this.statement.close();
                        this.closeConnection();
                        this.establishConnectionAndStatement();
                    }
                } catch (SQLException | ClassNotFoundException exception) {
                    LOG.error("[JDBCLookupQuery]JDBC connection is not valid, and reestablish connection failed: {}", exception.getMessage());
                    throw new RuntimeException("[JDBCLookupQuery]Reestablish JDBC connection failed", exception);
                }
            }
        }
        throw new RuntimeException("[JDBCLookupQuery]Get data failed");
    }

    /**
     * 查询所有记录
     * 实现重试机制，在连接失效时会自动重建连接
     *
     * @param in 输入参数
     * @return 查询结果列表
     * @throws RuntimeException 如果所有重试都失败
     */
    @Override
    public List<OUT> queryAll(IN in) {
        int retry = 0;
        // 允许重试
        for (; retry <= RETRY_COUNT; retry++) {
            try {
                // 清空statement参数
                this.statement.clearParameters();
                // 子类设置参数 & 查询 & 解析
                // -1 表示查询全部
                return this.doQueryList(statement, in, true, 0);
            } catch (Exception e) {
                if (retry == RETRY_COUNT) {
                    throw new RuntimeException("[JDBCLookupQuery] Execution of JDBC statement failed.", e);
                }

                try {
                    // 链接失效则重建
                    if (!isConnectionValid()) {
                        this.statement.close();
                        this.closeConnection();
                        this.establishConnectionAndStatement();
                    }
                } catch (SQLException | ClassNotFoundException exception) {
                    throw new RuntimeException("[JDBCLookupQuery]Reestablish JDBC connection failed", exception);
                }
            }
        }
        throw new RuntimeException("[JDBCLookupQuery]Get data failed");
    }

    /**
     * 查询指定数量的记录
     * 实现重试机制，在连接失效时会自动重建连接
     *
     * @param in  输入参数
     * @param cnt 查询记录数量限制
     * @return 查询结果列表
     * @throws RuntimeException 如果所有重试都失败
     */
    @Override
    public List<OUT> queryList(IN in, int cnt) {
        int retry = 0;
        // 允许重试
        for (; retry <= RETRY_COUNT; retry++) {
            try {
                // 清空statement参数
                this.statement.clearParameters();
                // 子类设置参数 & 查询 & 解析
                return this.doQueryList(statement, in, false, cnt);
            } catch (Exception e) {
                LOG.error("[JDBCLookupQuery]JDBC execute query list error, retry times = {}, error: {}", retry, e.getMessage());
                if (retry == RETRY_COUNT) {
                    LOG.error("[JDBCLookupQuery]exceed retries");
                    throw new RuntimeException("[JDBCLookupQuery] Execution of JDBC statement failed.", e);
                }

                try {
                    // 链接失效则重建
                    if (!isConnectionValid()) {
                        this.statement.close();
                        this.closeConnection();
                        this.establishConnectionAndStatement();
                    }
                } catch (SQLException | ClassNotFoundException exception) {
                    throw new RuntimeException("[JDBCLookupQuery]Reestablish JDBC connection failed", exception);
                }
            }
        }
        throw new RuntimeException("[JDBCLookupQuery]Get data failed");
    }

    /**
     * 加载JDBC驱动
     * 支持MySQL、PostgreSQL等常见数据库驱动
     *
     * @param driverName JDBC驱动类名
     * @return JDBC驱动实例
     * @throws SQLException           如果加载驱动失败
     * @throws ClassNotFoundException 如果驱动类未找到
     */
    private static Driver loadDriver(String driverName) throws SQLException, ClassNotFoundException {
        Preconditions.checkNotNull(driverName);
        Enumeration<Driver> drivers = DriverManager.getDrivers();
        while (drivers.hasMoreElements()) {
            Driver driver = drivers.nextElement();
            if (driver.getClass().getName().equals(driverName)) {
                return driver;
            }
        }
        // We could reach here for reasons:
        // * Class loader hell of DriverManager(see JDK-8146872).
        // * driver is not installed as a service provider.
        Class<?> clazz = Class.forName(driverName, true, Thread.currentThread().getContextClassLoader());
        try {
            return (Driver) clazz.newInstance();
        } catch (Exception ex) {
            throw new SQLException("[JDBCLookupQuery] Fail to create driver of class " + driverName, ex);
        }
    }

    /**
     * 获取已加载的JDBC驱动
     * 如果尚未加载，则先加载驱动
     *
     * @return JDBC驱动实例
     * @throws SQLException           如果加载驱动失败
     * @throws ClassNotFoundException 如果驱动类未找到
     */
    private Driver getLoadedDriver() throws SQLException, ClassNotFoundException {
        if (loadedDriver == null) {
            loadedDriver = loadDriver(this.driverName);
        }
        return loadedDriver;
    }

    /**
     * 建立数据库连接和预编译语句
     * 内部方法，用于初始化或重建连接
     *
     * @throws SQLException           如果连接失败
     * @throws ClassNotFoundException 如果驱动类未找到
     */
    private void establishConnectionAndStatement() throws SQLException, ClassNotFoundException {
        Connection dbConn = this.getOrEstablishConnection();
        statement = this.buildStatement(dbConn);
    }

    /**
     * 获取或建立数据库连接
     * 如果连接已存在且有效，则返回现有连接
     * 否则创建新的连接
     *
     * @return 数据库连接
     * @throws SQLException           如果连接失败
     * @throws ClassNotFoundException 如果驱动类未找到
     */
    private Connection getOrEstablishConnection() throws SQLException, ClassNotFoundException {
        if (connection != null) {
            return connection;
        }
        if (this.driverName == null) {
            connection = DriverManager.getConnection(this.url, this.userName, this.password);
        } else {
            Driver driver = getLoadedDriver();
            Properties info = new Properties() {{
                put("user", userName);
                put("password", password);
            }};
            connection = driver.connect(this.url, info);
            if (connection == null) {
                // Throw same exception as DriverManager.getConnection when no driver found to match
                // caller expectation.
                throw new SQLException("[JDBCLookupQuery] No suitable driver found for " + url, "08001");
            }
        }
        return connection;
    }

    /**
     * 检查数据库连接是否有效
     * 使用超时机制检测连接状态
     *
     * @return true如果连接有效，false如果连接无效或已关闭
     * @throws SQLException 如果检查连接状态时发生错误
     */
    private boolean isConnectionValid() throws SQLException {
        return connection != null && connection.isValid(CONNECTION_CHECK_TIMEOUT_SECONDS);
    }

    /**
     * 关闭数据库连接
     * 安全地关闭连接并释放资源
     */
    private void closeConnection() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                LOG.warn("[JDBCLookupQuery]JDBC connection:{} close failed: {}", this.url, e.getMessage());
            } finally {
                connection = null;
            }
        }
    }
}