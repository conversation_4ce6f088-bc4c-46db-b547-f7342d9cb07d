package com.tencent.andata.utils.factory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang.NotImplementedException;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.parquet.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Deprecated
public class BaseTableDDLFactory {

    protected String tableDDLTemplate = "CREATE TABLE %s\n %s WITH (\n" + "%s\n" + ")";
    protected HashMap<String, String> tableProperties;
    Logger logger = LoggerFactory.getLogger(BaseTableDDLFactory.class);

    /**
     * 设置额外的Property
     *
     * @param properties 配置
     * @return
     */
    public BaseTableDDLFactory setTableProperties(HashMap<String, String> properties) {
        Set<Map.Entry<String, String>> entries = properties.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            tableProperties.put(entry.getKey(), entry.getValue());
        }
        return this;
    }

    /**
     * 子类生成声明表结构的DDL
     *
     * @return
     */
    public String getTableSchemaDDL() throws TableNotExistException {
        throw new NotImplementedException("Sub class must implement this method!");
    }

    /**
     * 获取Table的DDL SQL
     *
     * @param flinkTableName 生成的Table名
     * @return
     */
    public String createTableDDL(String flinkTableName) throws TableNotExistException {
        // property拼接
        ArrayList<String> propertiesDeclareList = new ArrayList<>();
        for (Map.Entry<String, String> property : tableProperties.entrySet()) {
            propertiesDeclareList.add(
                    String.format(
                            "  '%s' = '%s'",
                            property.getKey(),
                            property.getValue()
                    )
            );
        }
        return String.format(
                tableDDLTemplate,
                flinkTableName.toLowerCase(Locale.ROOT),
                getTableSchemaDDL(),
                Strings.join(propertiesDeclareList, ",\n")
        );
    }
}