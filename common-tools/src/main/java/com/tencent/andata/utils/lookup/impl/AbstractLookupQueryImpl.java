package com.tencent.andata.utils.lookup.impl;

import com.tencent.andata.utils.lookup.exception.LimitedException;
import com.tencent.andata.utils.lookup.exception.QueryException;
import com.tencent.andata.utils.lookup.interfaces.Cacheable;
import com.tencent.andata.utils.lookup.interfaces.LookupQueryAble;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractLookupQueryImpl<IN, OUT> implements LookupQueryAble<IN, OUT> {

    private static final Logger LOG = LoggerFactory.getLogger(AbstractLookupQueryImpl.class);
    // 查询缓存
    private Cacheable<IN, OUT> cache;
    // 重试次数
    private int tryMaxCnt = 3;

    public void setCache(Cacheable<IN, OUT> cache) {
        this.cache = cache;
    }

    public void setTryMaxCnt(int tryMaxCnt) {
        this.tryMaxCnt = tryMaxCnt;
    }

    /**
     * 实际查询方法，子类具体实现
     *
     * @param in 参数
     * @return 查询结果
     */
    abstract protected OUT doQuery(IN in) throws Exception;

    /**
     * 重试过程中的回调方法
     */
    abstract protected void onRetryCallback();

    /**
     * TODO 判断是否限流，先抽出来，后续看怎么实现
     * 现在默认没有限流
     *
     * @return 是否限流
     */
    private boolean isLimited() {
        return false;
    }


    @Override
    public OUT query(IN in) throws QueryException {
        // 到达限流次数
        if (this.isLimited()) {
            throw new LimitedException("limit exceeded");
        }
        // 尝试次数
        int tryCnt = 0;
        // 查询报错
        Exception e = null;
        while (tryCnt < tryMaxCnt) {
            try {
                if (this.cache == null) {
                    return this.doQuery(in);
                }
                // 查询缓存
                OUT cacheValue = this.cache.getCache(in);
                if (cacheValue == null) {
                    // 没命中，查询外部
                    cacheValue = this.doQuery(in);
                    this.cache.setCache(in, cacheValue);
                }
                return cacheValue;

            } catch (Exception ex) {
                // 重试回调
                this.onRetryCallback();
                e = ex;
                tryCnt += 1;
                LOG.error("query time {} failed! err: {}", tryCnt, e.getMessage());
            }
        }
        // 最后查询失败抛出异常
        throw new QueryException(String.format("query failed! err: %s", e));
    }

    @Override
    public void open() throws Exception {

    }

    @Override
    public void close() throws Exception {

    }
}