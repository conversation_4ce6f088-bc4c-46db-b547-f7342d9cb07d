package com.tencent.andata.utils;


import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Try;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.Date;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Base64;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DbResultUtils implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 日志和配置
    private static final Logger LOG = LoggerFactory.getLogger(DbResultUtils.class);
    private static final RainbowUtils rainbow = RainbowAppConfig.getInstance();

    /**
     * 私有构造函数，防止外部实例化
     * 同时防止反射攻击
     */
    private DbResultUtils() {
        // 防止反射攻击
        if (DbResultUtilsHolder.INSTANCE != null) {
            throw new IllegalStateException("单例对象已存在，不允许重复创建");
        }
    }

    /**
     * 获取DbResultUtils单例对象
     * 线程安全的懒加载实现
     *
     * @return DbResultUtils单例实例
     */
    public static DbResultUtils getInstance() {
        return DbResultUtilsHolder.INSTANCE;
    }

    /**
     * 通用数据库查询器初始化工具方法
     * 支持任意类型的数据库查询器延迟初始化
     *
     * @param <T> 查询器类型
     * @param groupName 数据库配置组名
     * @param constructor 查询器构造函数
     * @param dbType 数据库类型
     * @param initializer 查询器初始化器（可选）
     * @return 初始化后的查询器实例
     */
    public static <T> T initializeQuery(String groupName, QueryConstructor<T> constructor, DatabaseEnum dbType, QueryInitializer<T> initializer) {
        return Try.of(() -> {
            DatabaseConf dbConf = new KVConfBuilder<>(DatabaseConf.class)
                    .setRainbowUtils(rainbow)
                    .setGroupName(groupName)
                    .build();

            T query = constructor.create(dbType, dbConf);

            // 使用通用初始化器
            if (initializer != null) {
                initializer.initialize(query);
            }

            return query;
        }).getOrElseThrow(e -> {
            LOG.error("Failed to initialize query for group: {}, error: {}", groupName, e.getMessage());
            return new RuntimeException("Query initialization failed for: " + groupName, e);
        });
    }

    /**
     * 通用数据库查询器初始化工具方法（简化版）
     * 适用于不需要特殊初始化逻辑的查询器
     *
     * @param <T> 查询器类型
     * @param groupName 数据库配置组名
     * @param constructor 查询器构造函数
     * @param dbType 数据库类型
     * @return 初始化后的查询器实例
     */
    public static <T> T initializeQuery(String groupName, QueryConstructor<T> constructor, DatabaseEnum dbType) {
        return initializeQuery(groupName, constructor, dbType, null);
    }

    /**
     * 根据数据类型将值添加到JsonNode
     *
     * @param jsonNode 目标JsonNode
     * @param fieldName 字段名
     * @param value 字段值
     * @param sqlType SQL类型（来自java.sql.Types）
     */
    public void addValueToJsonNode(ObjectNode jsonNode, String fieldName, Object value, int sqlType) {
        // 处理数值类型
        if (value instanceof Number) {
            if (value instanceof Integer || value instanceof Short || value instanceof Byte) {
                jsonNode.put(fieldName, ((Number) value).intValue());
            } else if (value instanceof Long) {
                jsonNode.put(fieldName, ((Number) value).longValue());
            } else if (value instanceof Float) {
                jsonNode.put(fieldName, ((Number) value).floatValue());
            } else if (value instanceof Double) {
                jsonNode.put(fieldName, ((Number) value).doubleValue());
            } else if (value instanceof BigDecimal) {
                jsonNode.put(fieldName, (BigDecimal) value);
            } else {
                // 其他数值类型
                jsonNode.put(fieldName, value.toString());
            }
            return;
        }

        // 处理字符串类型
        if (value instanceof String || value instanceof Character) {
            jsonNode.put(fieldName, value.toString());
            return;
        }

        // 处理布尔类型
        if (value instanceof Boolean) {
            jsonNode.put(fieldName, (Boolean) value);
            return;
        }

        // 处理日期时间类型
        if (value instanceof Date) {
            jsonNode.put(fieldName, value.toString());
            return;
        }
        if (value instanceof Time) {
            jsonNode.put(fieldName, value.toString());
            return;
        }
        if (value instanceof Timestamp) {
            jsonNode.put(fieldName, value.toString());
            return;
        }

        // 处理二进制类型
        if (value instanceof byte[]) {
            jsonNode.put(fieldName, Base64.getEncoder().encodeToString((byte[]) value));
            return;
        }

        // 处理BLOB类型
        if (value instanceof Blob) {
            Blob blob = (Blob) value;
            try (InputStream is = blob.getBinaryStream()) {
                byte[] bytes = new byte[(int) blob.length()];
                is.read(bytes);
                jsonNode.put(fieldName, Base64.getEncoder().encodeToString(bytes));
            } catch (Exception e) {
                jsonNode.put(fieldName, "Error reading BLOB data: " + e.getMessage());
            }
            return;
        }

        // 处理CLOB类型
        if (value instanceof Clob) {
            Clob clob = (Clob) value;
            try {
                jsonNode.put(fieldName, clob.getSubString(1, (int) clob.length()));
            } catch (Exception e) {
                jsonNode.put(fieldName, "Error reading CLOB data: " + e.getMessage());
            }
            return;
        }

        // 处理JSON类型
        if (sqlType == Types.OTHER && value.toString().startsWith("{")) {
            try {
                JsonNode jsonValue = objectMapper.readTree(value.toString());
                jsonNode.set(fieldName, jsonValue);
                return;
            } catch (Exception ignored) {
                // 如果解析失败，当作普通字符串处理
            }
        }

        // 其他类型，转为字符串
        jsonNode.put(fieldName, value.toString());
    }

    /**
     * 防止序列化破坏单例
     * 在反序列化时返回同一个实例
     *
     * @return 单例实例
     */
    private Object readResolve() {
        return getInstance();
    }

    /**
     * 公共方法：将ResultSet当前行转换为JsonNode
     * 将原来的私有方法改为公共方法，提供外部访问能力
     *
     * @param resultSet 结果集
     * @return 转换后的JsonNode
     * @throws SQLException SQL异常
     */
    public JsonNode convertResultSetRowToJsonNode(ResultSet resultSet) throws SQLException {
        return resultSetRowToJsonNode(resultSet);
    }

    /**
     * 将ResultSet当前行转换为JsonNode（内部实现）
     *
     * @param resultSet 结果集
     * @return 转换后的JsonNode
     * @throws SQLException SQL异常
     */
    private JsonNode resultSetRowToJsonNode(ResultSet resultSet) throws SQLException {
        ResultSetMetaData metaData = resultSet.getMetaData();
        ObjectNode jsonNode = objectMapper.createObjectNode();

        for (int i = 1; i <= metaData.getColumnCount(); i++) {
            String columnName = metaData.getColumnName(i);
            Object value = resultSet.getObject(i);
            int columnType = metaData.getColumnType(i);

            if (resultSet.wasNull() || value == null) {
                jsonNode.putNull(columnName);
                continue;
            }

            // 根据数据类型分组处理
            addValueToJsonNode(jsonNode, columnName, value, columnType);
        }

        return jsonNode;
    }

    /**
     * 查询器构造函数接口
     * 定义查询器的创建方式
     */
    @FunctionalInterface
    public interface QueryConstructor<T> {

        /**
         * 创建查询器实例
         *
         * @param dbType 数据库类型
         * @param config 数据库配置
         * @return 查询器实例
         * @throws Exception 创建过程中的异常
         */
        T create(DatabaseEnum dbType, DatabaseConf config) throws Exception;
    }

    /**
     * 查询器初始化器接口
     * 用于执行查询器创建后的初始化操作（如打开连接等）
     */
    @FunctionalInterface
    public interface QueryInitializer<T> {

        /**
         * 初始化查询器
         *
         * @param query 待初始化的查询器
         * @throws Exception 初始化过程中的异常
         */
        void initialize(T query) throws Exception;
    }

    /**
     * 静态内部类实现单例模式
     * 利用类加载机制保证线程安全，实现懒加载
     * 这种方式被称为 Initialization-on-demand holder idiom
     */
    private static class DbResultUtilsHolder {

        private static final DbResultUtils INSTANCE = new DbResultUtils();
    }
}