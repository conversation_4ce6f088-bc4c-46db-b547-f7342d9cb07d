package com.tencent.andata.utils.struct.mq.kafka;

import com.tencent.andata.utils.struct.mq.kafka.builder.RainbowKafkaConfBuilder;

import java.util.Properties;

public class KafkaConf {
    public String topics;
    public String brokers;
    public String consumerGroup;
    private Properties properties;

    public KafkaConf property(String key, String value) {
        if (this.properties == null) {
            this.properties = new Properties();
        }
        this.properties.put(key, value);
        return this;
    }

    public KafkaConf readCommitted() {
        this.property("isolation.level", "read_committed");
        return this;
    }

    public Properties properties() {
        this.property("bootstrap.servers", brokers);
        this.property("group.id", consumerGroup);
        return this.properties;
    }

    @Override
    public String toString() {
        return "KafkaConf{" 
            +                 "topics='" + topics + '\'' 
            +                 ", brokers='" + brokers + '\'' 
            +                 ", consumerGroup='" + consumerGroup + '\'' 
            +                 ", properties=" + properties 
            +                 '}';
    }

    public static void main(String[] args) throws Exception {
        final RainbowKafkaConfBuilder rainbowKafkaConfBuilder = new RainbowKafkaConfBuilder(
                "cprb.mask.kafka.ticketflow"
        );
        System.out.println(rainbowKafkaConfBuilder.build());
    }
}
