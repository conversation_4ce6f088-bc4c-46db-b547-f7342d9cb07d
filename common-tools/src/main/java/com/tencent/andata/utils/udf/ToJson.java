package com.tencent.andata.utils.udf;

import com.google.common.base.CaseFormat;
import java.io.IOException;
import java.io.StringWriter;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import org.apache.flink.table.data.DecimalData;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF;
import org.apache.hadoop.hive.serde2.objectinspector.ConstantObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ListObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.MapObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector.Category;
import org.apache.hadoop.hive.serde2.objectinspector.PrimitiveObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.PrimitiveObjectInspector.PrimitiveCategory;
import org.apache.hadoop.hive.serde2.objectinspector.StructField;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.BinaryObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.BooleanObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.ByteObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.DoubleObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.FloatObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.HiveCharObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.HiveDecimalObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.HiveVarcharObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.IntObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.LongObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.ShortObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.StringObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.TimestampObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.WritableConstantBooleanObjectInspector;
import org.codehaus.jackson.JsonFactory;
import org.codehaus.jackson.JsonGenerator;
import org.jetbrains.annotations.NotNull;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;


@Description(name = "to_json",
        value = "_FUNC_(struct, convert_to_camel_case) - Returns a JSON string from an arbitrary Hive structure."
)
public class ToJson extends GenericUDF {

    private InspectorHandle inspHandle;
    private Boolean convertFlag = Boolean.FALSE;
    private JsonFactory jsonFactory;

    @NotNull
    private static WritableConstantBooleanObjectInspector getWritableConstantBooleanObjectInspector(
            ObjectInspector[] args) throws UDFArgumentException {
        ObjectInspector flagInsp = args[1];
        if (flagInsp.getCategory() != Category.PRIMITIVE
                || ((PrimitiveObjectInspector) flagInsp).getPrimitiveCategory()
                != PrimitiveCategory.BOOLEAN
                || !(flagInsp instanceof ConstantObjectInspector)) {
            throw new UDFArgumentException(
                    " ToJson takes an object as an argument, and an optional to_camel_case flag");
        }
        return (WritableConstantBooleanObjectInspector) flagInsp;
    }

    private InspectorHandle generateInspectorHandle(ObjectInspector insp) throws UDFArgumentException {
        Category cat = insp.getCategory();
        if (cat == Category.STRUCT) {
            return new StructInspectorHandle((StructObjectInspector) insp);
        } else if (cat == Category.LIST) {
            return new ArrayInspectorHandle((ListObjectInspector) insp);
        } else if (cat == Category.MAP) {
            return new MapInspectorHandle((MapObjectInspector) insp);
        } else if (cat == Category.PRIMITIVE) {
            PrimitiveObjectInspector primInsp = (PrimitiveObjectInspector) insp;
            PrimitiveCategory primCat = primInsp.getPrimitiveCategory();
            if (primCat == PrimitiveCategory.VARCHAR) {
                return new VarcharObjectInspector((HiveVarcharObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.CHAR) {
                return new CharObjectInspector((HiveCharObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.INT) {
                return new IntInspectorHandle((IntObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.LONG) {
                return new LongInspectorHandle((LongObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.SHORT) {
                return new ShortInspectorHandle((ShortObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.TIMESTAMP) {
                return new TimestampInspectorHandle((TimestampObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.STRING) {
                return new StringInspectorHandle((StringObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.BOOLEAN) {
                return new BooleanInspectorHandle((BooleanObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.FLOAT) {
                return new FloatInspectorHandle((FloatObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.DOUBLE) {
                return new DoubleInspectorHandle((DoubleObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.BYTE) {
                return new ByteInspectorHandle((ByteObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.BINARY) {
                return new BinaryInspectorHandle((BinaryObjectInspector) primInsp);
            } else if (primCat == PrimitiveCategory.DECIMAL) {
                return new DecimalInspectorHandle((HiveDecimalObjectInspector) primInsp);
            }
        }
        /// Dunno ...
        throw new UDFArgumentException("Don't know how to handle object inspector " + insp);
    }

    @Override
    public Object evaluate(DeferredObject[] args) throws HiveException {
        try {
            StringWriter writer = new StringWriter();
            JsonGenerator gen = jsonFactory.createJsonGenerator(writer);
            inspHandle.generateJson(gen, args[0].get());
            gen.close();
            writer.close();
            return writer.toString();
        } catch (IOException io) {
            throw new HiveException(io);
        }
    }

    @Override
    public String getDisplayString(String[] args) {
        return "to_json(" + args[0] + ")";
    }

    @Override
    public ObjectInspector initialize(ObjectInspector[] args)
            throws UDFArgumentException {
        if (args.length != 1 && args.length != 2) {
            throw new UDFArgumentException(
                    " ToJson takes an object as an argument, and an optional to_camel_case flag");
        }
        ObjectInspector oi = args[0];
        inspHandle = generateInspectorHandle(oi);

        if (args.length == 2) {
            WritableConstantBooleanObjectInspector constInsp = getWritableConstantBooleanObjectInspector(args);
            convertFlag = constInsp.getWritableConstantValue().get();
        }

        jsonFactory = new JsonFactory();

        return PrimitiveObjectInspectorFactory.javaStringObjectInspector;
    }

    private interface InspectorHandle {

        void generateJson(JsonGenerator gen, Object obj) throws IOException;
    }

    private static class StringInspectorHandle implements InspectorHandle {

        private final StringObjectInspector strInspector;


        public StringInspectorHandle(StringObjectInspector insp) {
            strInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                String str = strInspector.getPrimitiveJavaObject(obj);
                gen.writeString(str);
            }
        }
    }

    private static class VarcharObjectInspector implements InspectorHandle {

        private final HiveVarcharObjectInspector varcharInspector;


        public VarcharObjectInspector(HiveVarcharObjectInspector insp) {
            varcharInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                String str = String.valueOf(varcharInspector.getPrimitiveJavaObject(obj));
                gen.writeString(str);
            }
        }
    }

    private static class CharObjectInspector implements InspectorHandle {

        private final HiveCharObjectInspector charInspector;


        public CharObjectInspector(HiveCharObjectInspector insp) {
            charInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                String str = String.valueOf(charInspector.getPrimitiveJavaObject(obj));
                gen.writeString(str);
            }
        }
    }

    private static class IntInspectorHandle implements InspectorHandle {

        private final IntObjectInspector intInspector;

        public IntInspectorHandle(IntObjectInspector insp) {
            intInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                int num = intInspector.get(obj);
                gen.writeNumber(num);
            }
        }
    }

    private static class DoubleInspectorHandle implements InspectorHandle {

        private final DoubleObjectInspector dblInspector;

        public DoubleInspectorHandle(DoubleObjectInspector insp) {
            dblInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                double num = dblInspector.get(obj);
                gen.writeNumber(num);
            }
        }
    }

    private static class LongInspectorHandle implements InspectorHandle {

        private final LongObjectInspector longInspector;

        public LongInspectorHandle(LongObjectInspector insp) {
            longInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                long num = longInspector.get(obj);
                gen.writeNumber(num);
            }
        }
    }

    private static class ShortInspectorHandle implements InspectorHandle {

        private final ShortObjectInspector shortInspector;

        public ShortInspectorHandle(ShortObjectInspector insp) {
            shortInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                short num = shortInspector.get(obj);
                gen.writeNumber(num);
            }
        }
    }

    private static class ByteInspectorHandle implements InspectorHandle {

        private final ByteObjectInspector byteInspector;

        public ByteInspectorHandle(ByteObjectInspector insp) {
            byteInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                byte num = byteInspector.get(obj);
                gen.writeNumber(num);
            }
        }
    }

    private static class FloatInspectorHandle implements InspectorHandle {

        private final FloatObjectInspector floatInspector;

        public FloatInspectorHandle(FloatObjectInspector insp) {
            floatInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                float num = floatInspector.get(obj);
                gen.writeNumber(num);
            }
        }
    }

    private static class BooleanInspectorHandle implements InspectorHandle {

        private final BooleanObjectInspector boolInspector;

        public BooleanInspectorHandle(BooleanObjectInspector insp) {
            boolInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                boolean tf = boolInspector.get(obj);
                gen.writeBoolean(tf);
            }
        }
    }

    private static class BinaryInspectorHandle implements InspectorHandle {

        private final BinaryObjectInspector binaryInspector;

        public BinaryInspectorHandle(BinaryObjectInspector insp) {

            binaryInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                byte[] bytes = binaryInspector.getPrimitiveJavaObject(obj);
                gen.writeBinary(bytes);
            }
        }
    }

    private static class TimestampInspectorHandle implements InspectorHandle {

        private final TimestampObjectInspector timestampInspector;
        private final DateTimeFormatter isoFormatter = ISODateTimeFormat.dateTimeNoMillis();

        public TimestampInspectorHandle(TimestampObjectInspector insp) {
            timestampInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                Timestamp timestamp = timestampInspector.getPrimitiveJavaObject(obj);
                String timeStr = isoFormatter.print(timestamp.getTime());
                gen.writeString(timeStr);
            }
        }
    }

    private static class DecimalInspectorHandle implements InspectorHandle {

        private final HiveDecimalObjectInspector decimalInspector;

        public DecimalInspectorHandle(HiveDecimalObjectInspector insp) {

            decimalInspector = insp;
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                if (decimalInspector.preferWritable()) {
                    gen.writeString(Objects.requireNonNull(DecimalData.fromBigDecimal(
                            decimalInspector.getPrimitiveWritableObject(obj).getHiveDecimal().bigDecimalValue(),
                            decimalInspector.precision(),
                            decimalInspector.scale())).toString());
                } else {
                    gen.writeString(Objects.requireNonNull(DecimalData.fromBigDecimal(
                            decimalInspector.getPrimitiveJavaObject(obj).bigDecimalValue(),
                            decimalInspector.precision(),
                            decimalInspector.scale())).toString());
                }
            }
        }
    }

    private class MapInspectorHandle implements InspectorHandle {

        private final MapObjectInspector mapInspector;
        private final StringObjectInspector keyObjectInspector;
        private final InspectorHandle valueInspector;


        public MapInspectorHandle(MapObjectInspector mInsp) throws UDFArgumentException {
            mapInspector = mInsp;
            try {
                keyObjectInspector = (StringObjectInspector) mInsp.getMapKeyObjectInspector();
            } catch (ClassCastException castExc) {
                throw new UDFArgumentException("Only Maps with strings as keys can be converted to valid JSON");
            }
            valueInspector = generateInspectorHandle(mInsp.getMapValueObjectInspector());
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                gen.writeStartObject();
                Map<?, ?> map = mapInspector.getMap(obj);
                for (Entry<?, ?> o : map.entrySet()) {
                    String keyJson = keyObjectInspector.getPrimitiveJavaObject(o.getKey());
                    if (convertFlag) {
                        gen.writeFieldName(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, keyJson));
                    } else {
                        gen.writeFieldName(keyJson);
                    }
                    valueInspector.generateJson(gen, o.getValue());
                }
                gen.writeEndObject();
            }
        }
    }

    private class StructInspectorHandle implements InspectorHandle {

        private final StructObjectInspector structInspector;
        private final List<String> fieldNames;
        private final List<InspectorHandle> fieldInspectorHandles;

        public StructInspectorHandle(StructObjectInspector insp) throws UDFArgumentException {
            structInspector = insp;
            List<? extends StructField> fieldList = insp.getAllStructFieldRefs();
            this.fieldNames = new ArrayList<>();
            this.fieldInspectorHandles = new ArrayList<>();
            for (StructField sf : fieldList) {
                fieldNames.add(sf.getFieldName());
                fieldInspectorHandles.add(generateInspectorHandle(sf.getFieldObjectInspector()));
            }
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            //// Interpret a struct as a map ...
            if (obj == null) {
                gen.writeNull();
            } else {
                gen.writeStartObject();
                List<Object> structObjs = structInspector.getStructFieldsDataAsList(obj);

                for (int i = 0; i < fieldNames.size(); ++i) {
                    String fieldName = fieldNames.get(i);
                    if (convertFlag) {
                        gen.writeFieldName(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, fieldName));
                    } else {
                        gen.writeFieldName(fieldName);
                    }
                    fieldInspectorHandles.get(i).generateJson(gen, structObjs.get(i));
                }
                gen.writeEndObject();
            }
        }
    }

    private class ArrayInspectorHandle implements InspectorHandle {

        private final ListObjectInspector arrayInspector;
        private final InspectorHandle valueInspector;


        public ArrayInspectorHandle(ListObjectInspector lInsp) throws UDFArgumentException {
            arrayInspector = lInsp;
            valueInspector = generateInspectorHandle(arrayInspector.getListElementObjectInspector());
        }

        @Override
        public void generateJson(JsonGenerator gen, Object obj) throws IOException {
            if (obj == null) {
                gen.writeNull();
            } else {
                gen.writeStartArray();
                List<?> list = arrayInspector.getList(obj);
                for (Object listObj : list) {
                    valueInspector.generateJson(gen, listObj);
                }
                gen.writeEndArray();
            }
        }
    }
}
