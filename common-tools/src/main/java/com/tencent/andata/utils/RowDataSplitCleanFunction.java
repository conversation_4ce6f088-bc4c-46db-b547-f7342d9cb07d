package com.tencent.andata.utils;

import com.tencent.andata.utils.cdc.conf.constant.CDCChangeLog;
import com.tencent.andata.utils.cdc.conf.fieldgetter.BaseFieldGetter;
import com.tencent.andata.utils.cdc.conf.sink.SwapChain;
import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.data.TimestampData;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.LogicalTypeRoot;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.RowKind;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import org.apache.iceberg.flink.FlinkSchemaUtil;

public class RowDataSplitCleanFunction {

    /**
     * 数据清洗Map
     * @param rowData
     * @param fieldGetterList
     * @return
     * @throws Exception
     */
    public static RowData map(RowData rowData, ArrayList<Tuple2<RowData.FieldGetter, LogicalType>> fieldGetterList)
            throws Exception {
        boolean isInstanceOfGenericRowData = rowData instanceof GenericRowData;
        GenericRowData retRow = isInstanceOfGenericRowData
                ? (GenericRowData) rowData :
                new GenericRowData(rowData.getArity());

        for (int i = 0; i < fieldGetterList.size(); ++i) {
            RowData.FieldGetter fieldGetter = fieldGetterList.get(i).getField(0);
            Object fieldData;
            LogicalTypeRoot typeRoot = ((LogicalType) fieldGetterList.get(i).getField(1)).getTypeRoot();

            if (typeRoot != LogicalTypeRoot.VARCHAR) {
                // 对于非String类数据，直接根据是否复用判断是否需要用Getter捞数据出来
                if (!isInstanceOfGenericRowData) {
                    fieldData = fieldGetter.getFieldOrNull(rowData);
                    retRow.setField(i, fieldData);
                }
            } else {
                // 对于String类型，不管怎样都得读取数据来做处理
                fieldData = fieldGetter.getFieldOrNull(rowData);
                // 20230707 Bug，具体原因见testMessageRowDataDBusSchemaChange这个UT。
                // TODO：Map算子UT待补全
                try {
                    fieldData = dataClean(fieldData);
                } catch (IndexOutOfBoundsException e) {
                    fieldData = null;
                }
                retRow.setField(i, fieldData);
            }
        }
        retRow.setRowKind(rowData.getRowKind());
        return retRow;
    }

    /**
     * 数据清洗Map
     * @param rowData
     * @param sourceFieldGetter
     * @param sinkFieldGetter
     * @param namePosMap
     * @return
     * @throws Exception
     */
    public static RowData map(RowData rowData, BaseFieldGetter sourceFieldGetter,
                              BaseFieldGetter sinkFieldGetter, Map<String, Integer> namePosMap)
            throws Exception {
        int sinkSize = sinkFieldGetter.fieldGetterList.size();
        GenericRowData retRow = new GenericRowData(sinkSize);

        for (int i = 0; i < sinkSize; ++i) {
            Tuple3<String, RowData.FieldGetter, LogicalType> srcGetter = sourceFieldGetter.fieldGetterList.get(i);
            Tuple3<String, RowData.FieldGetter, LogicalType> sinkGetter = sinkFieldGetter.fieldGetterList.get(i);
            if (srcGetter.f0.equals(sinkGetter.f0)) {
                // 不需要交换字段顺序
                Object fieldData = srcGetter.f1.getFieldOrNull(rowData);
                retRow.setField(i, fieldData);
            } else {
                // 应该在此位置的数据在源rowdata中的位置
                Tuple3<String, RowData.FieldGetter, LogicalType> destFieldGetter = sourceFieldGetter.fieldGetterList
                        .get(namePosMap.get(sinkGetter.f0));
                Object fieldData = destFieldGetter.f1.getFieldOrNull(rowData);
                retRow.setField(i, fieldData);
            }
        }
        retRow.setRowKind(rowData.getRowKind());
        return retRow;
    }

    @Deprecated
    private static RowData swapMap(RowData rowData, BaseFieldGetter sourceFieldGetter, List<SwapChain> swapChains) {
        GenericRowData retRow = (GenericRowData) rowData;
        for (SwapChain swapChain : swapChains) {
            Object firstData = sourceFieldGetter
                    .fieldGetterList
                    .get(swapChain.posChain.get(0))
                    .f1.getFieldOrNull(rowData);
            Object fieldOrNull;
            int posSize = swapChain.posChain.size();
            for (int j = 1; j < posSize - 1; ++j) {
                int pos1 = swapChain.posChain.get(j - 1);
                int pos2 = swapChain.posChain.get(j);
                fieldOrNull = sourceFieldGetter.fieldGetterList.get(pos2).f1.getFieldOrNull(rowData);
                retRow.setField(pos1, fieldOrNull);
            }
            retRow.setField(swapChain.posChain.get(posSize - 2), firstData);
        }
        return retRow;
    }

    /**
     * 增加ChangeLog相关字段
     * @param rowData
     * @param sourceFieldGetter
     * @param sinkFieldGetter
     * @param namePosMap
     * @return
     */
    public static RowData changeLogMap(RowData rowData, BaseFieldGetter sourceFieldGetter,
                                       BaseFieldGetter sinkFieldGetter, Map<String, Integer> namePosMap) {
        int size = sinkFieldGetter.fieldGetterList.size();
        MessageRowData retRow = new MessageRowData(size);

        retRow.setField(CDCChangeLog.OPERATION_POS, StringData.fromString(rowData.getRowKind().shortString()));
        retRow.setField(CDCChangeLog.OPERATION_TIME_POS, TimestampData.fromEpochMillis(System.currentTimeMillis()));

        for (int i = CDCChangeLog.FIELD_START_POS; i < size; ++i) {
            Tuple3<String, RowData.FieldGetter, LogicalType> sinkGetter = sinkFieldGetter.fieldGetterList.get(i);
            // 应该在此位置的数据在源rowdata中的位置
            Tuple3<String, RowData.FieldGetter, LogicalType> destFieldGetter = sourceFieldGetter.fieldGetterList
                    .get(namePosMap.get(sinkGetter.f0));
            Object fieldData = destFieldGetter.f1.getFieldOrNull(rowData);
            retRow.setField(i, fieldData);
        }
        retRow.setRowKind(RowKind.INSERT);
        if (rowData instanceof MessageRowData) {
            retRow.setAttr(retRow.getAttr());
        }
        return retRow;
    }

    private static Object dataClean(Object data) {
        if (data == null) {
            return null;
        }
        return StringData.fromString(
                Pattern.compile("[\\u0000-\\u001f]|\\p{C}")
                        .matcher(data.toString())
                        .replaceAll(""));
    }

}