package com.tencent.andata.utils.rowdata;

import static com.tencent.andata.utils.rowdata.RowDataConverter.copyGenericRowDataFromRowData;

import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;
import org.apache.flink.annotation.PublicEvolving;
import org.apache.flink.table.data.ArrayData;
import org.apache.flink.table.data.DecimalData;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.MapData;
import org.apache.flink.table.data.RawValueData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.data.TimestampData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Preconditions;
import org.apache.flink.util.StringUtils;

@PublicEvolving
public final class MessageRowData implements RowData {

    private final Object[] fields;
    private RowKind kind;
    @Getter
    private MessageRowDataAttr attr;

    public MessageRowData(RowKind kind, int arity) {
        this.fields = new Object[arity];
        this.kind = kind;
        this.attr = new MessageRowDataAttr();
    }

    public MessageRowData(int arity) {
        this.fields = new Object[arity];
        this.kind = RowKind.INSERT;
        this.attr = new MessageRowDataAttr();

    }

    public static MessageRowData convertFromGenericRowData(GenericRowData rowData, MessageRowDataAttr attr) {
        MessageRowData messageRowData = new MessageRowData(rowData.getRowKind(), rowData.getArity());
        for (int i = 0; i < rowData.getArity(); i++) {
            messageRowData.setField(i, rowData.getField(i));
        }
        messageRowData.setAttr(attr);
        return messageRowData;
    }

    public static MessageRowData convertFromRowData(RowData rowData, MessageRowDataAttr attr, RowType rowType) {
        return MessageRowData.convertFromGenericRowData(
                copyGenericRowDataFromRowData(rowData, rowType.getFields()),
                attr
        );
    }

    public static MessageRowData of(Object... values) {
        MessageRowData row = new MessageRowData(values.length);

        for (int i = 0; i < values.length; ++i) {
            row.setField(i, values[i]);
        }

        return row;
    }

    public static MessageRowData ofKind(RowKind kind, Object... values) {
        MessageRowData row = new MessageRowData(kind, values.length);

        for (int i = 0; i < values.length; ++i) {
            row.setField(i, values[i]);
        }

        return row;
    }

    public void setAttr(MessageRowDataAttr attr) {
        this.attr = attr;
    }

    public void setField(int pos, Object value) {
        this.fields[pos] = value;
    }

    public Object getField(int pos) {
        return this.fields[pos];
    }

    public int getArity() {
        return this.fields.length;
    }

    public RowKind getRowKind() {
        return this.kind;
    }

    public void setRowKind(RowKind kind) {
        Preconditions.checkNotNull(kind);
        this.kind = kind;
    }

    public boolean isNullAt(int pos) {
        return this.fields[pos] == null;
    }

    public boolean getBoolean(int pos) {
        return (Boolean) this.fields[pos];
    }

    public byte getByte(int pos) {
        return (Byte) this.fields[pos];
    }

    public short getShort(int pos) {
        return (Short) this.fields[pos];
    }

    public int getInt(int pos) {
        return (Integer) this.fields[pos];
    }

    public long getLong(int pos) {
        return (Long) this.fields[pos];
    }

    public float getFloat(int pos) {
        return (Float) this.fields[pos];
    }

    public double getDouble(int pos) {
        return (Double) this.fields[pos];
    }

    public StringData getString(int pos) {
        return (StringData) this.fields[pos];
    }

    public DecimalData getDecimal(int pos, int precision, int scale) {
        return (DecimalData) this.fields[pos];
    }

    public TimestampData getTimestamp(int pos, int precision) {
        return (TimestampData) this.fields[pos];
    }

    public <T> RawValueData<T> getRawValue(int pos) {
        return (RawValueData) this.fields[pos];
    }

    public byte[] getBinary(int pos) {
        return (byte[]) ((byte[]) this.fields[pos]);
    }

    public ArrayData getArray(int pos) {
        return (ArrayData) this.fields[pos];
    }

    public MapData getMap(int pos) {
        return (MapData) this.fields[pos];
    }

    public RowData getRow(int pos, int numFields) {
        return (RowData) this.fields[pos];
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (!(o instanceof MessageRowData)) {
            return false;
        } else {
            MessageRowData that = (MessageRowData) o;
            return this.kind == that.kind && Arrays.deepEquals(this.fields, that.fields);
        }
    }

    public int hashCode() {
        int result = Objects.hash(new Object[]{this.kind});
        result = 31 * result + Arrays.deepHashCode(this.fields);
        return result;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(this.kind.shortString()).append("(");

        for (int i = 0; i < this.fields.length; ++i) {
            if (i != 0) {
                sb.append(",");
            }

            sb.append(StringUtils.arrayAwareToString(this.fields[i]));
        }

        sb.append(")");
        return String.format("%s, %s", sb.toString(), attr.toString());
    }

}
