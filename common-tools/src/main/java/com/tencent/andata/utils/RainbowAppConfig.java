package com.tencent.andata.utils;

import java.io.IOException;
import java.util.Properties;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * RainbowUtils实例的线程安全单例工厂。
 * 此类实现了按需初始化持有者模式，以实现最佳性能和线程安全。
 * 支持默认配置（来自env.properties）和自定义配置初始化。
 *
 * <AUTHOR>
 * @version 2.0
 */
public final class RainbowAppConfig {

    private static final Logger LOGGER = Logger.getLogger(RainbowAppConfig.class.getName());

    // 自定义配置初始化的锁
    private static final ReentrantReadWriteLock LOCK = new ReentrantReadWriteLock();

    // 非默认配置的自定义实例
    private static volatile RainbowUtils customInstance;
    private static volatile Properties customConfig;

    /**
     * 私有构造函数防止实例化。
     */
    private RainbowAppConfig() {
        throw new UnsupportedOperationException("RainbowAppConfig是一个工具类，不能被实例化");
    }

    /**
     * 用于默认实例初始化的静态内部类。
     * 此类仅在首次访问时加载，确保线程安全的延迟初始化。
     */
    private static final class DefaultInstanceHolder {

        private static final Properties DEFAULT_CONFIG;
        private static final RainbowUtils DEFAULT_INSTANCE;

        static {
            try {
                DEFAULT_CONFIG = loadDefaultConfig();
                DEFAULT_INSTANCE = new RainbowUtils(DEFAULT_CONFIG);
                LOGGER.info("默认RainbowUtils实例初始化成功");
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "初始化默认RainbowUtils实例失败", e);
                throw new ExceptionInInitializerError("加载默认配置失败: " + e.getMessage());
            }
        }

        /**
         * 从env.properties文件加载默认配置。
         *
         * @return 包含默认配置的Properties对象
         */
        private static Properties loadDefaultConfig() throws IOException {
            Properties config = PropertyUtils.loadProperties("env.properties");
            if (config == null) {
                throw new IllegalStateException("加载env.properties文件失败");
            }

            // 验证必需属性
            validateRequiredProperties(config);

            return config;
        }

        /**
         * 验证所有必需的Rainbow配置属性是否存在。
         *
         * @param config 要验证的属性
         * @throws IllegalStateException 如果缺少必需属性
         */
        private static void validateRequiredProperties(Properties config) {
            String[] requiredKeys = {
                    "RAINBOW_APP_ID",
                    "RAINBOW_ENV_NAME",
                    "RAINBOW_USER_ID",
                    "RAINBOW_SECRET_KEY"
            };

            for (String key : requiredKeys) {
                if (config.getProperty(key) == null || config.getProperty(key).trim().isEmpty()) {
                    throw new IllegalStateException("配置中缺少必需属性'" + key + "'或为空");
                }
            }
        }
    }

    /**
     * 获取用env.properties初始化的默认RainbowUtils实例。
     * 此方法使用按需初始化持有者模式，该模式是线程安全的，
     * 并且在没有同步开销的情况下提供最佳性能。
     *
     * @return 具有默认配置的RainbowUtils实例
     * @throws ExceptionInInitializerError 如果无法加载默认配置
     */
    public static RainbowUtils getInstance() {
        return DefaultInstanceHolder.DEFAULT_INSTANCE;
    }

    /**
     * 获取具有自定义配置的RainbowUtils实例。
     * 此方法在处理自定义配置时实现了双重检查锁定以确保线程安全。
     * 如果提供的属性为null或空，则返回默认实例。
     *
     * @param properties 自定义配置属性（可以为null表示默认配置）
     * @return 具有指定配置的RainbowUtils实例
     * @throws IllegalArgumentException 如果属性包含无效值
     */
    public static RainbowUtils getInstance(Properties properties) {
        // 如果没有提供自定义属性，返回默认实例
        if (properties == null || properties.isEmpty()) {
            LOGGER.info("未提供自定义属性，返回默认实例");
            return getInstance();
        }

        // 检查我们是否已经有具有相同配置的自定义实例
        LOCK.readLock().lock();
        try {
            if (customInstance != null && propertiesEqual(customConfig, properties)) {
                return customInstance;
            }
        } finally {
            LOCK.readLock().unlock();
        }

        // 需要创建新的自定义实例
        LOCK.writeLock().lock();
        try {
            // 双重检查模式
            if (customInstance != null && propertiesEqual(customConfig, properties)) {
                return customInstance;
            }

            // 创建合并配置（默认+自定义）
            Properties mergedConfig = createMergedConfig(properties);

            // 验证合并后的配置
            DefaultInstanceHolder.validateRequiredProperties(mergedConfig);

            // 创建新实例
            customInstance = new RainbowUtils(mergedConfig);
            customConfig = new Properties();
            customConfig.putAll(properties); // 仅存储自定义部分用于比较

            LOGGER.info("自定义RainbowUtils实例创建成功");
            return customInstance;

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "创建自定义RainbowUtils实例失败", e);
            throw new IllegalArgumentException("使用自定义配置创建RainbowUtils失败: " + e.getMessage(), e);
        } finally {
            LOCK.writeLock().unlock();
        }
    }

    /**
     * 通过组合默认和自定义属性来创建合并配置。
     * 自定义属性覆盖默认属性。
     *
     * @param customProperties 要合并的自定义属性
     * @return 合并后的属性
     */
    private static Properties createMergedConfig(Properties customProperties) {
        Properties merged = new Properties();

        // 从默认配置开始
        merged.putAll(DefaultInstanceHolder.DEFAULT_CONFIG);

        // 用自定义属性覆盖
        merged.putAll(customProperties);

        return merged;
    }

    /**
     * 比较两个Properties对象是否相等。
     *
     * @param props1 第一个属性对象
     * @param props2 第二个属性对象
     * @return 如果属性相等返回true，否则返回false
     */
    private static boolean propertiesEqual(Properties props1, Properties props2) {
        if (props1 == props2) {return true;}
        if (props1 == null || props2 == null) {return false;}
        return props1.equals(props2);
    }

    /**
     * 获取默认配置属性的防御性副本。
     *
     * @return 默认配置属性的副本
     */
    public static Properties getDefaultConfig() {
        Properties copy = new Properties();
        copy.putAll(DefaultInstanceHolder.DEFAULT_CONFIG);
        return copy;
    }

    /**
     * 重置自定义实例。此方法主要用于测试目的。
     *
     * @deprecated 此方法应仅在测试环境中使用
     */
    @Deprecated
    public static void resetCustomInstance() {
        LOCK.writeLock().lock();
        try {
            customInstance = null;
            customConfig = null;
            LOGGER.warning("自定义RainbowUtils实例已重置");
        } finally {
            LOCK.writeLock().unlock();
        }
    }

    /**
     * 检查当前是否有自定义实例处于活动状态。
     *
     * @return 如果存在自定义实例返回true，否则返回false
     */
    public static boolean hasCustomInstance() {
        LOCK.readLock().lock();
        try {
            return customInstance != null;
        } finally {
            LOCK.readLock().unlock();
        }
    }
}