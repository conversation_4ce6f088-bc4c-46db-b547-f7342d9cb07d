package com.tencent.andata.utils.udf;

import org.apache.flink.table.functions.ScalarFunction;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringDataCleanTransform extends ScalarFunction {

    public StringDataCleanTransform() {
    }

    /**
     * 去除字符串数据中的换行符等
     *
     * @param data 原数据
     * @return
     */
    public String eval(String data) {
        Pattern reg = Pattern.compile("[\u0000-\u001f]|\\r|\\n");
        Matcher m = reg.matcher(data);
        return m.replaceAll(" ");
    }
}
