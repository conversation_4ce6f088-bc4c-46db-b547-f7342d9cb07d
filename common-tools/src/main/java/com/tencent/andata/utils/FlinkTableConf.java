package com.tencent.andata.utils;

import java.util.HashMap;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.util.Preconditions;

public class FlinkTableConf {

    private StreamTableEnvironment env;
    private HashMap<String, String> conf = new HashMap<>();

    public FlinkTableConf setEnv(StreamTableEnvironment env) {
        this.env = env;
        return this;
    }

    public FlinkTableConf setConf(String key, String value) {
        this.conf.put(key, value);
        return this;
    }

    public Configuration build() {
        Preconditions.checkNotNull(env);
        Configuration configuration = env.getConfig().getConfiguration();
        addDefaultConf(configuration);
        return configuration;
    }

    private void addDefaultConf(Configuration configuration) {
        //开启微批模式
        configuration.setString("table.exec.mini-batch.size", "5000");
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "3 s");
        // 状态保留3天
        // configuration.setString("table.exec.state.ttl", "259200000");
        configuration.setString("execution.runtime-mode", "streaming");
        configuration.setString("execution.checkpointing.interval", "30s");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        configuration.setString("table.exec.sink.upsert-materialize", "AUTO");
        configuration.setString("table.dynamic-table-options.enabled", "true");
        configuration.setString("table.exec.legacy-cast-behaviour", "enabled");

        // 优化join性能
        configuration.setString("table.optimizer.reuse-source-enabled", "true");
        configuration.setString("table.optimizer.join-reorder-enabled", "true");
        configuration.setString("table.optimizer.reuse-sub-plan-enabled", "true");
        configuration.setString("table.optimizer.multiple-input-enabled", "true");
        configuration.setString("table.exec.disabled-operators", "NestedLoopJoin");
        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");
        configuration.setString("table.exec.simplify-operator-name-enabled", "true");
        // 关闭iceberg source的自动推断并行度
        configuration.setString("table.exec.iceberg.infer-source-parallelism", "false");

        configuration.setString("table.optimizer.join.broadcast-threshold", "268435456");
        configuration.setString("table.optimizer.source.aggregate-pushdown-enabled", "true");
        configuration.setString("table.optimizer.source.predicate-pushdown-enabled", "true");

    }
}
