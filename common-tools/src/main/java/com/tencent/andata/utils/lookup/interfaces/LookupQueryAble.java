package com.tencent.andata.utils.lookup.interfaces;

import com.tencent.andata.utils.lookup.exception.QueryException;

import java.io.Serializable;

/**
 * 生命周期：new -> open -> query -> close
 *
 * @param <IN>
 * @param <OUT>
 */
public interface LookupQueryAble<IN, OUT> extends Serializable {

    public void open() throws Exception;

    public void close() throws Exception;


    public OUT query(IN in) throws QueryException;
}
