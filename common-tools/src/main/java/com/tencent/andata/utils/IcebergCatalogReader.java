package com.tencent.andata.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.ObjectPath;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.types.TypeInfoDataTypeConverter;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeTransformations;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.types.logical.TimestampType;
import org.apache.flink.table.types.utils.DataTypeUtils;
import org.apache.hadoop.hive.conf.HiveConf;
import org.apache.iceberg.Table;
import org.apache.iceberg.catalog.Catalog;
import org.apache.iceberg.catalog.TableIdentifier;
import org.apache.iceberg.flink.CatalogLoader;
import org.apache.iceberg.flink.FlinkSchemaUtil;
import org.apache.iceberg.flink.TableLoader;
import org.apache.iceberg.relocated.com.google.common.collect.Maps;
import org.apache.iceberg.types.Type;
import org.apache.iceberg.types.Types;

import static org.apache.flink.table.api.DataTypes.STRING;
import static org.apache.flink.table.api.DataTypes.FIELD;
import static org.apache.flink.table.api.DataTypes.ROW;
import static org.apache.flink.table.api.DataTypes.INT;
import static org.apache.flink.table.api.DataTypes.BIGINT;
import static org.apache.flink.table.api.DataTypes.TIMESTAMP_WITH_LOCAL_TIME_ZONE;
import static org.apache.flink.table.api.DataTypes.TIMESTAMP;
import static org.apache.flink.table.api.DataTypes.BOOLEAN;
import static org.apache.flink.table.api.DataTypes.DOUBLE;
import static org.apache.flink.table.api.DataTypes.BYTES;
import static org.apache.flink.table.api.DataTypes.DECIMAL;

public class IcebergCatalogReader {

    private final CatalogLoader catalogLoader;
    private final Catalog catalog;
    private static IcebergCatalogReader reader;

    private static final ReentrantReadWriteLock singletonWLock = new ReentrantReadWriteLock();

    public IcebergCatalogReader() {
        HiveConf hiveConf = new HiveConf();
        hiveConf.addResource("hdfs-site.xml");
        hiveConf.set(
                HiveConf.ConfVars.METASTOREURIS.varname, "thrift://ss-qe-nginx-tauth.tencent-distribute.com:8108");
        hiveConf.set(HiveConf.ConfVars.METASTORE_EXECUTE_SET_UGI.varname, "false");
        HashMap<String, String> hashMap = Maps.newHashMap();
        // 防止代码误操作导致的数据消失
        hashMap.put("table.drop.delete-base-path-directly.enabled", "false");
        catalogLoader = CatalogLoader.hive("my_catalog", hiveConf, hashMap);
        catalog = catalogLoader.loadCatalog();
    }

    public static DataType getDataType(Table table) {
        List<DataTypes.Field> fields = new ArrayList<>();
        for (Types.NestedField field : table.schema().asStruct().fields()) {
            fields.add(FIELD(field.name(), convertTypeIDToDataType(field.type())));
        }
        return ROW(fields.toArray(new DataTypes.Field[]{}));
    }

    public static DataType getDataType(Table table, boolean timeZone) {
        List<DataTypes.Field> fields = new ArrayList<>();
        for (Types.NestedField field : table.schema().asStruct().fields()) {
            fields.add(FIELD(field.name(), convertTypeIDToDataType(field.type(), timeZone)));
        }
        return ROW(fields.toArray(new DataTypes.Field[]{}));
    }

    private static TypeInformation<RowData> convertTableToTypeInfo(Table table) {
        return createTypeInfo(IcebergCatalogReader.getDataType(table));
    }

    private static DataType convertTypeIDToDataType(Type type) {
       return convertTypeIDToDataType(type, false);
    }

    private static DataType convertTypeIDToDataType(Type type, boolean timeZone) {
        Type.TypeID typeID = type.typeId();
        // 后续写个Converter
        switch (typeID) {
            case STRING:
                return STRING();
            case INTEGER:
                return INT();
            case LONG:
                return BIGINT();
            case TIMESTAMP:
                if (timeZone) {
                    return TIMESTAMP_WITH_LOCAL_TIME_ZONE();
                }
                return TIMESTAMP();
            case BOOLEAN:
                return BOOLEAN();
            case DOUBLE:
                return DOUBLE();
            case BINARY:
                return BYTES();
            case DECIMAL:
                // TODO: 这里只对 BIGINT UNSIGNED 做了处理，如果天然就是DECIMAL/NUMERIC，会有问题
                return DECIMAL(20, 0);
            default:
                throw new IllegalArgumentException(
                        String.format("%s is not listed in switch case", typeID.name()));
        }
    }

    @SuppressWarnings("unchecked")
    public static TypeInformation<RowData> createTypeInfo(DataType producedDataType) {
        DataType internalDataType =
                DataTypeUtils.transform(producedDataType, TypeTransformations.TO_INTERNAL_CLASS);
        return (TypeInformation<RowData>)
                TypeInfoDataTypeConverter.fromDataTypeToTypeInfo(internalDataType);
    }

    public TableLoader getTableLoaderInstance(String database, String tableName) {
        return getTableLoaderInstance(TableIdentifier.of(database, tableName));
    }

    public TableLoader getTableLoaderInstance(TableIdentifier tableIdentifier) {
        TableLoader tableLoader = TableLoader.fromCatalog(catalogLoader, tableIdentifier);
        tableLoader.open();
        return tableLoader;

    }


    public Table getTableInstance(String database, String tableName) throws TableNotExistException {
        return getTableInstance(TableIdentifier.of(database, tableName));
    }

    public Table getTableInstance(TableIdentifier tableIdentifier) throws TableNotExistException {
        if (catalog.tableExists(tableIdentifier)) {
            return catalog.loadTable(tableIdentifier);
        }
        throw new TableNotExistException("ICEBERG",
                new ObjectPath(tableIdentifier.namespace().toString(),
                        tableIdentifier.name()));
    }

    public RowType getTableRowType(String database, String tableName) throws TableNotExistException {
        return FlinkSchemaUtil.convert(getTableInstance(database, tableName).schema());
    }

    public RowTypeInfo getTableRowTypeInfo(String database, String tableName) throws TableNotExistException {
        RowType rowType = FlinkSchemaUtil.convert(getTableInstance(database, tableName).schema());
        int fieldCount = rowType.getFieldCount();
        TypeInformation[] types = new TypeInformation[fieldCount];
        String[] fieldNames = new String[fieldCount];
        List<String> fields = rowType.getFieldNames();
        for (int i = 0; i < fieldCount; i++) {
            LogicalType typeAt = rowType.getTypeAt(i);
            Class<?> type = typeAt.getDefaultConversion();
            types[i] = TypeInformation.of(type);
            fieldNames[i] = fields.get(i);
        }
        return new RowTypeInfo(types, fieldNames);
    }

    public static RowType getTableRowType(Table table) {
        return FlinkSchemaUtil.convert(table.schema());
    }

    public Catalog getCatalog() {
        return catalog == null ? catalogLoader.loadCatalog() : catalog;
    }

    public TypeInformation<RowData> getTypeInformation(Table table) {
        return convertTableToTypeInfo(table);
    }

    // Singularity
    static public IcebergCatalogReader getInstance() {
        singletonWLock.readLock().lock();

        if (IcebergCatalogReader.reader == null) {
            // 锁升级
            singletonWLock.readLock().unlock();
            singletonWLock.writeLock().lock();

            IcebergCatalogReader.reader = new IcebergCatalogReader();

            singletonWLock.writeLock().unlock();
        } else {
            singletonWLock.readLock().unlock();
        }

        return IcebergCatalogReader.reader;
    }
}