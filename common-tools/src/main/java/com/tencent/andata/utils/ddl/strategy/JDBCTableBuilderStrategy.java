package com.tencent.andata.utils.ddl.strategy;

import com.tencent.andata.utils.connector.jdbc.catalog.JdbcCatalogUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.util.HashMap;
import org.apache.flink.table.catalog.ObjectPath;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;

public class JDBCTableBuilderStrategy extends BaseTableBuilderStrategy {

    private final String dbTableName;
    private final DatabaseConf databaseConf;
    private final DatabaseEnum databaseEnum;

    /**
     * JDBC表生成策略
     */
    public JDBCTableBuilderStrategy(String dbTableName, DatabaseEnum databaseEnum, DatabaseConf databaseConf)
            throws Exception {
        this.databaseConf = databaseConf;
        this.dbTableName = dbTableName;
        this.databaseEnum = databaseEnum;
        // 设置Property
        this.tableProperties = new HashMap<String, String>() {{
            put("connector", "jdbc");
            put("username", databaseConf.userName);
            put("password", databaseConf.password);
            put("table-name", dbTableName);
        }};

        if (databaseEnum == DatabaseEnum.PGSQL) {
            this.tableProperties.put("url", String.format(
                    "***************************************************************************"
                            + "&serverTimezone=Asia/Shanghai",
                    databaseConf.dbHost,
                    databaseConf.dbPort,
                    databaseConf.dbName
            ));
        } else if (databaseEnum == DatabaseEnum.MYSQL) {
            this.tableProperties.put("url", String.format(
                    "jdbc:mysql://%s:%d/%s",
                    databaseConf.dbHost,
                    databaseConf.dbPort,
                    databaseConf.dbName
            ));
        } else {
            throw new Exception("Only support PGSQL and MYSQL!");
        }
    }

    @Override
    public String getSchemaString() throws TableNotExistException {
        return JdbcCatalogUtils.createCatalog(
                        "cdc_catalog",
                        databaseConf.dbName,
                        databaseConf.userName,
                        databaseConf.password,
                        String.format("jdbc:%s://%s:%d/",
                                databaseEnum.toString(),
                                databaseConf.dbHost,
                                databaseConf.dbPort),
                        databaseEnum
                )
                .getTable(new ObjectPath(databaseConf.dbName, dbTableName))
                .getUnresolvedSchema().toString();
    }

    public JDBCTableBuilderStrategy driver(String value) {
        this.tableProperties.put("driver", value);
        return this;
    }

    public JDBCTableBuilderStrategy sinkParallelism(String value) {
        this.tableProperties.put("sink.parallelism", value);
        return this;
    }

    public JDBCTableBuilderStrategy lookupCacheTtl(String value) {
        this.tableProperties.put("lookup.cache.ttl", value);
        return this;
    }

    public JDBCTableBuilderStrategy sinkBufferFlushMaxRows(String value) {
        this.tableProperties.put("sink.buffer-flush.max-rows", value);
        return this;
    }

    public JDBCTableBuilderStrategy sinkBufferFlushInterval(String value) {
        this.tableProperties.put("sink.buffer-flush.interval", value);
        return this;
    }
}
