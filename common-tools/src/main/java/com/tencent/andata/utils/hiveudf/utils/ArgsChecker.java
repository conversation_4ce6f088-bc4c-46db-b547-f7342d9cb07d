package com.tencent.andata.utils.hiveudf.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;

public class ArgsChecker {
    String funcName;
    List<Class<?>> argsSchemaList;

    public static class Builder {
        final ArrayList<Class<?>> builderArgsSchemaList;
        String funcName = "Unnamed";

        Builder() {
            builderArgsSchemaList = new ArrayList<>();
        }

        public Builder addColumnClass(Class<?> oi) {
            builderArgsSchemaList.add(oi);
            return this;
        }

        public Builder setFuncName(String name) {
            this.funcName = name;
            return this;
        }

        public ArgsChecker build() {
            return new ArgsChecker(builderArgsSchemaList);
        }
    }

    ArgsChecker(ArrayList<Class<?>> argSchemaList) {
        this.argsSchemaList = argSchemaList;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    /**
     * checkArgsNumbersAndType
     * @param arguments SQL解析出来的参数类型
     * @throws UDFArgumentLengthException 参数长度不对
     * @throws UDFArgumentTypeException 参数类型不对
     */
    public void checkArgsNumbersAndType(ObjectInspector[] arguments) throws
            UDFArgumentLengthException,
            UDFArgumentTypeException {
        boolean isArgumentLengthQualified = arguments.length == argsSchemaList.size();
        if (!isArgumentLengthQualified) {
            throw new UDFArgumentLengthException(String.format(
                    "%s takes %d argument: %s,but receive %s",
                    funcName,
                    argsSchemaList.size(),
                    argsSchemaList,
                    Arrays.toString(arguments)));
        }
        for (int i = 0; i < argsSchemaList.size(); i++) {
            Class<?> colType = argsSchemaList.get(i);
            Class<?> argType = arguments[i].getClass();
            if (!colType.isAssignableFrom(argType)) {
                throw new UDFArgumentTypeException(
                        i,
                        String.format("%s %d arg should be %s, but receive %s",
                                funcName,
                                i,
                                colType,
                                argType
                        )
                );
            }
        }
    }
}