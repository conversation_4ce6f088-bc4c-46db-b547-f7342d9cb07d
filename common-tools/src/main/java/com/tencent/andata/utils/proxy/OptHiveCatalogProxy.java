package com.tencent.andata.utils.proxy;

import com.tencent.andata.utils.conf.IcebergTableConfEnum;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import org.apache.hadoop.hive.conf.HiveConf;
import org.apache.iceberg.BaseTable;
import org.apache.iceberg.CatalogUtil;
import org.apache.iceberg.PartitionSpec;
import org.apache.iceberg.Schema;
import org.apache.iceberg.Table;
import org.apache.iceberg.TableMetadata;
import org.apache.iceberg.TableOperations;
import org.apache.iceberg.catalog.TableIdentifier;
import org.apache.iceberg.hive.optimized.OptimizedHiveCatalog;
import org.apache.iceberg.relocated.com.google.common.collect.ImmutableMap;

// TODO:与离线Spark应用共用90%代码。后面抽出公共包
public class OptHiveCatalogProxy {

    OptimizedHiveCatalog hiveCatalog;

    public OptHiveCatalogProxy(OptimizedHiveCatalog hiveCatalog) {
        this.hiveCatalog = hiveCatalog;
    }

    public static OptHiveCatalogProxy buildFromOptHiveCatalog(OptimizedHiveCatalog catalog) {
        return new OptHiveCatalogProxy(catalog);
    }

    public static OptHiveCatalogProxy getDefaultInstance() {
        return new OptHiveCatalogProxy(getDefaultOptHiveCatalog());
    }

    static OptimizedHiveCatalog getDefaultOptHiveCatalog() {
        HiveConf hcf = new HiveConf();
        hcf.addResource("hdfs-site.xml");
        hcf.set(HiveConf.ConfVars.METASTOREURIS.varname, "thrift://ss-qe-nginx-tauth.tencent-distribute.com:8106");
        hcf.set(HiveConf.ConfVars.METASTORE_EXECUTE_SET_UGI.varname, "false");
        return (OptimizedHiveCatalog) CatalogUtil.loadCatalog(
                OptimizedHiveCatalog.class.getName(),
                "hive_opt", ImmutableMap.of(),
                hcf
        );
    }

    static TableIdentifier trimSparkCatalog(TableIdentifier identifier) {
        String[] nameList = identifier.toString().split("\\.");
        int nameLength = nameList.length;
        if (nameList.length > 2) {
            identifier = TableIdentifier.of(nameList[nameLength - 2], nameList[nameLength - 1]);
        }
        return identifier;
    }

    public boolean tableExists(TableIdentifier tbName) {
        return hiveCatalog.tableExists(tbName);
    }

    IcebergTableReplicator getTableReplicator(TableIdentifier srcTableIdentifier, TableIdentifier dstTableIdentifier) {
        // TODO: 后面添加缓存来解决同一张表被多次访问导致的性能问题
        Table srcTable = hiveCatalog.loadTable(srcTableIdentifier);
        return new IcebergTableReplicator(srcTable, dstTableIdentifier);
    }

    public void replicaTableIfNotExists(TableIdentifier src, TableIdentifier dst) {
        // 这段代码主要是为了将Spark Catalog & Hive表统一。如果用Spark 里的 iceberg.db.table访问，会报错。
        // 这里对超长的做了截取，只取分隔符之后的两个

        src = trimSparkCatalog(src);
        dst = trimSparkCatalog(dst);

        if (!this.tableExists(dst)) {
            this.getTableReplicator(src, dst).create();
        }
    }

    /**
     * 给定Iceberg表Schema，根据Schema创建对应的表
     * HINTS: Flink Schema转Ice Schema方法如下：
     * org.apache.iceberg.Schema iceSchema = TableUtils.convertRowTypeToIcebergSchema(
     * TableUtils.convertFlinkSchemaToRowType(schema, false)
     * );
     *
     * @param tableIdentifier Table名
     * @param schema 对应的Ice Schema
     * @param pkList 主键List
     * @param partitionSpec 分区信息
     */
    public void createIceTableIfNotExistsUsingIceSchema(TableIdentifier tableIdentifier,
            Schema schema,
            List<String> pkList,
            PartitionSpec partitionSpec) {
        if (!this.hiveCatalog.tableExists(tableIdentifier)) {
            this.hiveCatalog
                    .createTable(
                            tableIdentifier,
                            schema,
                            partitionSpec == null ? PartitionSpec.unpartitioned() : partitionSpec,
                            IcebergTableConfEnum.getDefaultIcebergTableConfMap(pkList)
                    );
        } else {
            throw new RuntimeException(String.format("Table %s exists, cannot create table", tableIdentifier));
        }
    }

    public class IcebergTableReplicator {

        @Getter
        Schema schema;
        TableIdentifier tbName;
        PartitionSpec partitionSpec;

        Map<String, String> properties;

        /**
         * Iceberg表复制器，给定一个Iceberg表与一个TableIdentifier，根据Iceberg表结构创建表
         *
         * @param table 需要复制的表
         * @param tbName 生成的表名称
         */
        IcebergTableReplicator(Table table, TableIdentifier tbName) {
            this.tbName = tbName;
            this.schema = table.schema();
            this.partitionSpec = table.spec();
            this.properties = table.properties();
        }

        public PartitionSpec.Builder getPartitionSpecBuilder() {
            return PartitionSpec.builderFor(schema);
        }

        public IcebergTableReplicator setPartitionSpec(PartitionSpec spec) {
            this.partitionSpec = spec;
            return this;
        }

        public IcebergTableReplicator setTableProperty(String name, String value) {
            properties.put(name, value);
            return this;
        }

        public Table create() {
            BaseTable table = (BaseTable) hiveCatalog.createTable(tbName, schema, partitionSpec, properties);
            TableOperations operations = table.operations();
            TableMetadata metadata = operations.current();
            if (metadata.formatVersion() < 2) {
                operations.commit(metadata, metadata.upgradeToFormatVersion(2));
            }
            return table;
        }
    }
}
