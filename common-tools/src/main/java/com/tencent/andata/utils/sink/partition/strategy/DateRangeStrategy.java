package com.tencent.andata.utils.sink.partition.strategy;

import com.google.common.base.Preconditions;
import com.tencent.andata.utils.CommonUtils;
import com.tencent.andata.utils.Constant;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * 时间分区策略
 */
public class DateRangeStrategy extends RangePartitionStrategy {
    public enum DateType {
        DAY {
            @Override
            public String toString() {
                return "day";
            }
        },
        MONTH {
            @Override
            public String toString() {
                return "month";
            }
        },
        YEAR {
            @Override
            public String toString() {
                return "year";
            }
        }
    }

    public DateType dateType;
    public int stepSize;
    public Date startDate;

    public DateRangeStrategy(String strategyString) throws ParseException {
        super(strategyString);
    }

    @Override
    public void init(String strategyString) throws ParseException {
        List<String> split = Arrays.stream(
                strategyString.split(
                        Constant.PARTITION_STRATEGY_PARSE_DELIMITER
                )
        ).collect(Collectors.toList());
        this.name = CommonUtils.listGetOrDefault(0, null, split);
        // 日期分区类型
        String dateTypeStr = CommonUtils.listGetOrDefault(1, null, split);
        this.dateType = DateType.valueOf(dateTypeStr.toUpperCase(Locale.ROOT));
        // 步长
        String stepSizeStr = CommonUtils.listGetOrDefault(2, null, split);
        if (stepSizeStr != null) {
            this.stepSize = Integer.parseInt(stepSizeStr);
        }
        // 分区开始时间
        String startDatetime = CommonUtils.listGetOrDefault(3, null, split);
        this.startDate = startDatetime != null ? new SimpleDateFormat("yyyy-MM-dd").parse(startDatetime) : null;
        // name和type为非空属性
        Preconditions.checkNotNull(this.name);
        Preconditions.checkNotNull(this.dateType);
    }

    @Override
    public String toString() {
        return "DateRangeStrategy{"  
                 + "name='" + name + '\''  
                 + ", dateType=" + dateType  
                 + ", stepSize=" + stepSize  
                 + ", startDate=" + startDate  
                 + '}';
    }
}