package com.tencent.andata.utils;

import java.util.List;

public class CommonUtils {
    /**
     * 获取列表指定索引的元素，没有则返回默认值
     *
     * @param index        指定索引
     * @param defaultValue 默认值
     * @param list         列表
     * @param <E>          列表值类型
     * @return
     */
    public static <E> E listGetOrDefault(int index, E defaultValue, List<E> list) {
        if (index < 0) {
            throw new IllegalArgumentException("index is less than 0: " + index);
        }
        return index <= list.size() - 1 ? list.get(index) : defaultValue;
    }
}
