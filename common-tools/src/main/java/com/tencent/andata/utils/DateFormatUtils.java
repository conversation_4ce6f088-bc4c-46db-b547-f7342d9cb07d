package com.tencent.andata.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateFormatUtils {

    public static final SimpleDateFormat STANDARD_FORMAT =
            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.ssssss'Z'");

    public static String cstTimestampToUTCString(long timestamp) {
        // 使用UTC入库
        timestamp = timestamp - 28800000;
        SimpleDateFormat localTimeZoneFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.ssssss'Z'");
        return localTimeZoneFormat.format(timestamp);
    }

    public static String timestampToString(long timestamp) {
        // 使用标准格式转换
        return STANDARD_FORMAT.format(timestamp);
    }

    public static String timestampToString(long timestamp, String formatter) {
        // 使用指定格式转换
        SimpleDateFormat format = new SimpleDateFormat(formatter);
        return format.format(timestamp);
    }

    public static long timeDelta(String startTime, String endTime) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date end = formatter.parse(endTime.replaceAll(".\\d{6}", ""));
        Date start = formatter.parse(startTime.replaceAll(".\\d{6}", ""));

        return (end.getTime() - start.getTime()) / 1000;
    }
}
