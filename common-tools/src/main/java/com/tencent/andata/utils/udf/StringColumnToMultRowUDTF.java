package com.tencent.andata.utils.udf;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonMappingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * 一行拆分多行
 */
@FunctionHint(
        output = @DataTypeHint("String"))
public class StringColumnToMultRowUDTF extends TableFunction<Row> {

    /**
     * 具体执行方法
     * @param itemValues
     */
    public void eval(String itemValues) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            ArrayNode nodes = mapper.readValue(itemValues, ArrayNode.class);
            List<JsonNode> datasets = StreamSupport
                    .stream(nodes.spliterator(), true)
                    .collect(Collectors.toList());
            for (JsonNode tableNode : datasets) {
                Map<String, String> fieldMap = new HashMap();
                Iterator<String> fieldNames = tableNode.fieldNames();
                while (fieldNames.hasNext()) {
                    String fieldName = fieldNames.next();
                    final JsonNode obj = tableNode.get(fieldName);
                    if (obj.isArray() || obj.isObject()) {
                        fieldMap.put(fieldName, obj.toString());
                    } else {
                        fieldMap.put(fieldName, obj.asText());
                    }
                }
                ObjectMapper json = new ObjectMapper();
                System.out.println(json.writeValueAsString(fieldMap));
                Row row = new Row(1);
                row.setField(0, json.writeValueAsString(fieldMap));
                collect(row);
            }
        } catch (JsonMappingException e) {
            e.printStackTrace();
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    @Override
    public TypeInformation<Row> getResultType() {
        return new RowTypeInfo(Types.STRING);
    }
}
