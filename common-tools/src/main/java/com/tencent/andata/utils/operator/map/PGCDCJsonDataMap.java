package com.tencent.andata.utils.operator.map;

import static org.apache.flink.table.types.logical.LogicalTypeRoot.TIMESTAMP_WITHOUT_TIME_ZONE;

import com.tencent.andata.utils.DateFormatUtils;
import com.tencent.andata.utils.JSONUtils;
import java.nio.charset.StandardCharsets;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.formats.json.JsonRowDataDeserializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.RowKind;

public class PGCDCJsonDataMap implements MapFunction<String, RowData> {

    private final JsonRowDataDeserializationSchema jsonRowDataDeserializationSchema;
    private final JSONUtils jsonUtils;
    private final RowType physicalRowType;

    public PGCDCJsonDataMap(JsonRowDataDeserializationSchema jsonRowDataDeserializationSchema,
            RowType physicalRowType) {
        this.jsonRowDataDeserializationSchema = jsonRowDataDeserializationSchema;
        this.jsonUtils = new JSONUtils();
        this.physicalRowType = physicalRowType;
    }

    @Override
    public RowData map(String s) throws Exception {
        ObjectNode cdcSchemaRecord = jsonUtils.getJSONObjectNodeByString(s);
        String op = cdcSchemaRecord.get("op").asText();
        ObjectNode after = (ObjectNode) cdcSchemaRecord.get("after");
        for (RowType.RowField field : this.physicalRowType.getFields()) {
            // 这里PG Schema字段如果是没有时区的时间格式的话，存的是long纳秒时间戳，需要做特殊转换才行
            if (TIMESTAMP_WITHOUT_TIME_ZONE.equals(field.getType().getTypeRoot())) {
                String fieldName = field.getName();
                long timestamp = after.get(fieldName).longValue();
                String timeString = DateFormatUtils.timestampToString(
                        (timestamp / 1000) - 57600000,
                        "yyyy-MM-dd'T'HH:mm:ss.ssssss'Z'"
                );
                after.put(fieldName, timeString);
            }
        }
        RowData res = this.jsonRowDataDeserializationSchema.deserialize(
                after.toString().getBytes(StandardCharsets.UTF_8)
        );
        switch (op) {
            case "r":
            case "c":
                res.setRowKind(RowKind.INSERT);
                break;
            case "d":
                res.setRowKind(RowKind.DELETE);
                break;
            case "u":
                res.setRowKind(RowKind.UPDATE_AFTER);
                break;
        }
        return res;
    }
}
