package com.tencent.andata.utils.factory;

import com.tencent.andata.utils.connector.jdbc.catalog.JdbcCatalogUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.util.HashMap;
import org.apache.flink.table.catalog.ObjectPath;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;

@Deprecated
public class CDCTableDDLFactory extends BaseTableDDLFactory {

    protected String dbTableName;
    protected DatabaseConf databaseConf;
    protected DatabaseEnum databaseEnum;

    /**
     * DDL
     * @param dbTableName
     * @param databaseEnum
     * @param databaseConf
     * @throws Exception
     */
    public CDCTableDDLFactory(String dbTableName, DatabaseEnum databaseEnum, DatabaseConf databaseConf)
            throws Exception {
        this.dbTableName = dbTableName;
        this.databaseConf = databaseConf;
        this.databaseEnum = databaseEnum;
        if (databaseEnum == DatabaseEnum.PGSQL) {
            throw new Exception("PGSQL CDC Connector not support!");
        }
        // 表property
        this.tableProperties = new HashMap<String, String>() {{
            put("connector", String.format("%s-cdc", databaseEnum.toString()));
            put("hostname", databaseConf.dbHost);
            put("port", databaseConf.dbPort.toString());
            put("username", databaseConf.userName);
            put("password", databaseConf.password);
            put("database-name", databaseConf.dbName);
            put("table-name", dbTableName);
        }};
    }

    /**
     * 返回DDL
     *
     * @return
     * @throws TableNotExistException
     */
    public String getTableSchemaDDL() throws TableNotExistException {
        return JdbcCatalogUtils.createCatalog(
                        "cdc_catalog",
                        databaseConf.dbName,
                        databaseConf.userName,
                        databaseConf.password,
                        String.format("jdbc:mysql://%s:%d/", databaseConf.dbHost, databaseConf.dbPort),
                        DatabaseEnum.MYSQL
                )
                .getTable(new ObjectPath(databaseConf.dbName, dbTableName))
                .getUnresolvedSchema().toString();
    }
}