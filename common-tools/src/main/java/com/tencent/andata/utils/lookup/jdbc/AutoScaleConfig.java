package com.tencent.andata.utils.lookup.jdbc;

import java.io.Serializable;

/**
 * Hikari连接池自动扩缩容配置类
 * 
 * 提供可配置的自动扩缩容参数，替代硬编码配置
 * 
 * <AUTHOR>
 */
public class AutoScaleConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // 扩缩容步长：每次调整的连接数
    private int scaleStep = 2;
    
    // 检查周期：自动扩缩容检查间隔（毫秒）
    private long periodMs = 2_000L;
    
    // 连接池最小边界：连接池最小连接数限制
    private int minPoolSizeBound = 5;
    
    // 连接池最大边界：连接池最大连接数限制
    private int maxPoolSizeBound = 20;
    
    // 冷却时间：两次扩缩容操作之间的最小间隔（毫秒）
    private long cooldownMs = 30_000L;
    
    // 等待线程阈值：触发扩容的等待连接线程数
    private int awaitingThreshold = 3;
    
    // 稳定轮数要求：缩容前需要连续满足条件的轮数
    private int lowStableRoundsNeeded = 3;
    
    // 低利用率阈值：触发缩容的连接池利用率上限
    private double lowUtilization = 0.25d;
    
    // 高利用率阈值：触发扩容的连接池利用率下限
    private double highUtilization = 0.75d;
    
    // 自动扩缩容开关
    private boolean autoScaleEnabled = true;
    
    /**
     * 默认构造函数
     */
    public AutoScaleConfig() {
    }
    
    /**
     * 全参数构造函数
     */
    public AutoScaleConfig(int scaleStep, long periodMs, int minPoolSizeBound, int maxPoolSizeBound,
                          long cooldownMs, int awaitingThreshold, int lowStableRoundsNeeded,
                          double lowUtilization, double highUtilization, boolean autoScaleEnabled) {
        this.scaleStep = scaleStep;
        this.periodMs = periodMs;
        this.minPoolSizeBound = minPoolSizeBound;
        this.maxPoolSizeBound = maxPoolSizeBound;
        this.cooldownMs = cooldownMs;
        this.awaitingThreshold = awaitingThreshold;
        this.lowStableRoundsNeeded = lowStableRoundsNeeded;
        this.lowUtilization = lowUtilization;
        this.highUtilization = highUtilization;
        this.autoScaleEnabled = autoScaleEnabled;
        validate();
    }
    
    /**
     * 验证配置参数的合理性
     * 
     * @throws IllegalArgumentException 如果配置参数不合理
     */
    public void validate() {
        if (scaleStep <= 0) {
            throw new IllegalArgumentException("Scale step must be positive, got: " + scaleStep);
        }
        
        if (periodMs <= 0) {
            throw new IllegalArgumentException("Period must be positive, got: " + periodMs);
        }
        
        if (minPoolSizeBound <= 0) {
            throw new IllegalArgumentException("Min pool size bound must be positive, got: " + minPoolSizeBound);
        }
        
        if (maxPoolSizeBound <= minPoolSizeBound) {
            throw new IllegalArgumentException(
                String.format("Max pool size bound (%d) must be greater than min pool size bound (%d)", 
                             maxPoolSizeBound, minPoolSizeBound));
        }
        
        if (cooldownMs < 0) {
            throw new IllegalArgumentException("Cooldown must be non-negative, got: " + cooldownMs);
        }
        
        if (awaitingThreshold < 0) {
            throw new IllegalArgumentException("Awaiting threshold must be non-negative, got: " + awaitingThreshold);
        }
        
        if (lowStableRoundsNeeded <= 0) {
            throw new IllegalArgumentException("Low stable rounds needed must be positive, got: " + lowStableRoundsNeeded);
        }
        
        if (lowUtilization < 0.0 || lowUtilization > 1.0) {
            throw new IllegalArgumentException("Low utilization must be between 0.0 and 1.0, got: " + lowUtilization);
        }
        
        if (highUtilization < 0.0 || highUtilization > 1.0) {
            throw new IllegalArgumentException("High utilization must be between 0.0 and 1.0, got: " + highUtilization);
        }
        
        if (lowUtilization >= highUtilization) {
            throw new IllegalArgumentException(
                String.format("Low utilization (%.2f) must be less than high utilization (%.2f)", 
                             lowUtilization, highUtilization));
        }
    }
    
    /**
     * 创建默认配置
     */
    public static AutoScaleConfig defaultConfig() {
        return new AutoScaleConfig();
    }
    
    /**
     * 创建高频场景配置（更短的检查周期和冷却时间）
     */
    public static AutoScaleConfig highFrequencyConfig() {
        return new AutoScaleConfig(2, 1_000L, 5, 30, 15_000L, 2, 2, 0.2d, 0.8d, true);
    }
    
    /**
     * 创建大型应用配置（更大的连接池边界）
     */
    public static AutoScaleConfig largeApplicationConfig() {
        return new AutoScaleConfig(5, 3_000L, 10, 100, 60_000L, 5, 3, 0.3d, 0.7d, true);
    }
    
    // Getter和Setter方法
    public int getScaleStep() {
        return scaleStep;
    }
    
    public void setScaleStep(int scaleStep) {
        this.scaleStep = scaleStep;
    }
    
    public long getPeriodMs() {
        return periodMs;
    }
    
    public void setPeriodMs(long periodMs) {
        this.periodMs = periodMs;
    }
    
    public int getMinPoolSizeBound() {
        return minPoolSizeBound;
    }
    
    public void setMinPoolSizeBound(int minPoolSizeBound) {
        this.minPoolSizeBound = minPoolSizeBound;
    }
    
    public int getMaxPoolSizeBound() {
        return maxPoolSizeBound;
    }
    
    public void setMaxPoolSizeBound(int maxPoolSizeBound) {
        this.maxPoolSizeBound = maxPoolSizeBound;
    }
    
    public long getCooldownMs() {
        return cooldownMs;
    }
    
    public void setCooldownMs(long cooldownMs) {
        this.cooldownMs = cooldownMs;
    }
    
    public int getAwaitingThreshold() {
        return awaitingThreshold;
    }
    
    public void setAwaitingThreshold(int awaitingThreshold) {
        this.awaitingThreshold = awaitingThreshold;
    }
    
    public int getLowStableRoundsNeeded() {
        return lowStableRoundsNeeded;
    }
    
    public void setLowStableRoundsNeeded(int lowStableRoundsNeeded) {
        this.lowStableRoundsNeeded = lowStableRoundsNeeded;
    }
    
    public double getLowUtilization() {
        return lowUtilization;
    }
    
    public void setLowUtilization(double lowUtilization) {
        this.lowUtilization = lowUtilization;
    }
    
    public double getHighUtilization() {
        return highUtilization;
    }
    
    public void setHighUtilization(double highUtilization) {
        this.highUtilization = highUtilization;
    }
    
    public boolean isAutoScaleEnabled() {
        return autoScaleEnabled;
    }
    
    public void setAutoScaleEnabled(boolean autoScaleEnabled) {
        this.autoScaleEnabled = autoScaleEnabled;
    }
    
    @Override
    public String toString() {
        return "AutoScaleConfig{" +
                "scaleStep=" + scaleStep +
                ", periodMs=" + periodMs +
                ", minPoolSizeBound=" + minPoolSizeBound +
                ", maxPoolSizeBound=" + maxPoolSizeBound +
                ", cooldownMs=" + cooldownMs +
                ", awaitingThreshold=" + awaitingThreshold +
                ", lowStableRoundsNeeded=" + lowStableRoundsNeeded +
                ", lowUtilization=" + lowUtilization +
                ", highUtilization=" + highUtilization +
                ", autoScaleEnabled=" + autoScaleEnabled +
                '}';
    }
}
