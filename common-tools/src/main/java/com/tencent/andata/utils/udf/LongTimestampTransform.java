package com.tencent.andata.utils.udf;

import com.tencent.andata.utils.DateFormatUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.sql.Timestamp;

public class LongTimestampTransform {
    public static class LongToString extends ScalarFunction {
        public String eval(Long timestamp) {
            return DateFormatUtils.timestampToString(timestamp);
        }

        public String eval(Long timestamp, String tz) {
            if ("UTC".equalsIgnoreCase(tz)) {
                return DateFormatUtils.cstTimestampToUTCString(timestamp);
            } else if ("CST".equalsIgnoreCase(tz)) {
                return DateFormatUtils.timestampToString(timestamp + 28800000);
            }
            return eval(timestamp);
        }
    }

    public static class LongToSqlTimestamp extends ScalarFunction {
        public Timestamp eval(Long timestamp) {
            if (timestamp == null) {
                return null;
            }
            return new Timestamp(timestamp);
        }

        public Timestamp eval(Long timestamp, String tz) {
            if (timestamp == null) {
                return null;
            }
            if ("UTC".equalsIgnoreCase(tz)) {
                return new Timestamp(timestamp - 28800000);
            } else if ("CST".equalsIgnoreCase(tz)) {
                return new Timestamp(timestamp + 28800000);
            }
            return eval(timestamp);
        }
    }
}