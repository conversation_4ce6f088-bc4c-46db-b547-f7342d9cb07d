package com.tencent.andata.utils;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonParser;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationContext;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.MapperFeature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectWriter;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.SerializationFeature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.TextNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.type.CollectionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class JSONUtils implements Serializable {

    private static final Logger logger = LoggerFactory.getLogger(JSONUtils.class);

    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(DeserializationFeature.ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT, true)
            .configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true)
            .configure(MapperFeature.REQUIRE_SETTERS_FOR_GETTERS, true)
            .setTimeZone(TimeZone.getDefault());

    public JSONUtils() {
    }

    /**
     * 新建一个可变ArrayNode对象
     */
    public static ArrayNode createArrayNode() {
        return objectMapper.createArrayNode();
    }

    public static String toJsonString(Object object, SerializationFeature feature) {
        try {
            ObjectWriter writer = objectMapper.writer(feature);
            return writer.writeValueAsString(object);
        } catch (Exception e) {
            logger.error("object to json exception!", e);
        }
        return null;
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            logger.error("parse object exception!", e);
        }
        return null;
    }

    public static <T> T parseObject(byte[] src, Class<T> clazz) {
        if (src == null) {
            return null;
        }
        String json = new String(src, UTF_8);
        return parseObject(json, clazz);
    }

    public static <T> List<T> toList(String json, Class<T> clazz) {
        if (StringUtils.isEmpty(json)) {
            return Collections.emptyList();
        }
        try {
            CollectionType listType = objectMapper.getTypeFactory().constructCollectionType(ArrayList.class, clazz);
            return objectMapper.readValue(json, listType);
        } catch (Exception e) {
            logger.error("parse list exception!", e);
        }
        return Collections.emptyList();
    }

    public static Map<String, String> toMap(String json) {
        return parseObject(json, new TypeReference<Map<String, String>>() {
        });
    }

    public static <K, V> Map<K, V> toMap(String json, Class<K> classK, Class<V> classV) {
        return parseObject(json, new TypeReference<Map<K, V>>() {
        });
    }

    public static <T> T parseObject(String json, TypeReference<T> type) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        try {
            return objectMapper.readValue(json, type);
        } catch (Exception e) {
            logger.error("json to map exception!", e);
        }
        return null;
    }

    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            throw new RuntimeException("Object json deserialization exception.", e);
        }
    }

    public static <T> byte[] toJsonByteArray(T obj) {
        if (obj == null) {
            return null;
        }
        String json = "";
        try {
            json = toJsonString(obj);
        } catch (Exception e) {
            logger.error("json serialize exception.", e);
        }
        return json.getBytes(UTF_8);
    }

    public static ObjectNode parseObject(String text) {
        try {
            if (text.isEmpty()) {
                return parseObject(text, ObjectNode.class);
            } else {
                return (ObjectNode) objectMapper.readTree(text);
            }
        } catch (Exception e) {
            throw new RuntimeException("String json deserialization exception.", e);
        }
    }

    public static ArrayNode parseArray(String text) {
        try {
            return (ArrayNode) objectMapper.readTree(text);
        } catch (Exception e) {
            throw new RuntimeException("Json deserialization exception.", e);
        }
    }

    /**
     * 新建一个可变JSON对象
     */
    public ObjectNode createObjectNode() {
        return objectMapper.createObjectNode();
    }

    /**
     * 将JSON字符串解析成ObjectNode列表
     */
    public ArrayList<ObjectNode> getJSONObjectArrayByString(String value) throws JsonProcessingException {
        ArrayList<ObjectNode> result = new ArrayList<>();
        JsonNode jsonNode = objectMapper.readValue(value, JsonNode.class);
        if (jsonNode.isArray()) {
            for (Iterator<JsonNode> it = jsonNode.elements(); it.hasNext(); ) {
                JsonNode node = it.next();
                result.add((ObjectNode) node);
            }
        } else {
            // 如果字符串是单个JSON，则外面包一层列表
            result.add((ObjectNode) jsonNode);
        }
        return result;
    }

    /**
     * 将JSON字符串解析成ObjectNode单个对象
     */
    public ObjectNode getJSONObjectNodeByString(String value) throws JsonProcessingException {
        return (ObjectNode) objectMapper.readValue(value, JsonNode.class);
    }

    /**
     * 将字符串转成JsonNode
     *
     * @param value value
     * @return json node
     * @throws JsonProcessingException ex
     */
    public static JsonNode getJsonNodeByString(String value) throws JsonProcessingException {
        return objectMapper.readTree(value);
    }

    /**
     * 获取指定field 的json node
     *
     * @param value json string
     * @param field field name
     * @return field json node
     * @throws JsonProcessingException ex
     */
    public static JsonNode getJsonNodeByString(String value, String field) throws JsonProcessingException {
        final JsonNode jsonNode = objectMapper.readTree(value);
        return jsonNode.get(field);
    }

    /**
     * 将嵌套JSON展平
     */
    public ObjectNode flatten(JsonNode value) {
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode result = objectMapper.createObjectNode();
        // 待处理的JSON对象列表
        LinkedList<JsonNode> nodeList = new LinkedList<>();
        nodeList.add(value);
        while (!nodeList.isEmpty()) {
            // 取出第一个
            JsonNode node = nodeList.pop();
            Iterator<Map.Entry<String, JsonNode>> it = node.fields();
            // 遍历JSON对象的所有字段
            while (it.hasNext()) {
                Map.Entry<String, JsonNode> entry = it.next();
                JsonNode fieldValue = entry.getValue();
                if (fieldValue.isObject()) {
                    // 如果该字段的值是JSON对象，则添加到队列中递归处理
                    nodeList.add(fieldValue);
                } else {
                    result.set(entry.getKey().toLowerCase(), fieldValue);
                }
            }
        }
        return result;
    }

    public static class JsonDataDeserializer extends JsonDeserializer<String> {

        @Override
        public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            JsonNode node = p.getCodec().readTree(p);
            if (node instanceof TextNode) {
                return node.asText();
            } else {
                return node.toString();
            }
        }
    }
}
