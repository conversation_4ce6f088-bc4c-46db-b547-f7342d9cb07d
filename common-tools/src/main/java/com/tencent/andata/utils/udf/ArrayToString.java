package com.tencent.andata.utils.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Objects;


public class ArrayToString extends ScalarFunction {

    public String eval(String split, String[] delimiter) {
        if (Objects.isNull(delimiter) || delimiter.length == 0) {
            return "";
        }
        return StringUtils.join(delimiter, split);
    }

    public String eval(String split, Integer[] delimiter) {
        if (Objects.isNull(delimiter) || delimiter.length == 0) {
            return "";
        }
        return StringUtils.join(delimiter, split);
    }



}