package com.tencent.andata.utils;

import com.google.common.base.Preconditions;
import com.tencent.andata.utils.meta.IcebergTableMeta;
import com.tencent.andata.utils.speedlayer.LocalCacheSpeedLayer;
import com.tencent.andata.utils.speedlayer.SpeedLayer;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.iceberg.Table;
import org.apache.iceberg.catalog.TableIdentifier;
import org.apache.iceberg.exceptions.NoSuchTableException;
import org.apache.iceberg.flink.TableLoader;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ThreadPoolExecutor;


public class ConcurrentIcebergTableLoader {
    private int CORE_POOL_SIZE = 5;
    private int MAXIMUM_POOL_SIZE = 20;
    private long KEEP_ALIVE_TIME = 0L;
    private int TIME_OUT_MINUTES = 4;

    private final String databaseName;
    private final HashMap<String, String> keyTableNameMap;
    private static final IcebergCatalogReader icebergCatalogReader = new IcebergCatalogReader();
    private final SpeedLayer speedLayer;

    /**
     * Iceberg 表加载器，并发加载提升编译速度
     *
     * @param databaseName iceberg库名
     */
    ConcurrentIcebergTableLoader(String databaseName, SpeedLayer speedLayer) {
        this.databaseName = databaseName;
        this.keyTableNameMap = new HashMap<>();
        this.speedLayer = speedLayer;
    }

    @Deprecated
    public static ConcurrentIcebergTableLoader fromHiveDB(String databaseName) {
        return new Builder()
                .setDatabaseName(databaseName)
                .setSpeedLayer(
                        LocalCacheSpeedLayer.builder().build()
                )
                .build();
    }

    public static class Builder {
        private String databaseName;
        private SpeedLayer speedLayer;

        public Builder setDatabaseName(String databaseName) {
            this.databaseName = databaseName;
            return this;
        }

        public Builder setSpeedLayer(SpeedLayer speedLayer) {
            this.speedLayer = speedLayer;
            return this;
        }

        public ConcurrentIcebergTableLoader build() {
            Preconditions.checkNotNull(databaseName);
            return new ConcurrentIcebergTableLoader(databaseName, speedLayer);
        }
    }

    /**
     * 添加单个表
     *
     * @param key       表句柄，用于标识一个表
     * @param tableName Iceberg表名
     * @return
     */
    public ConcurrentIcebergTableLoader addTable(String key, String tableName) {
        this.keyTableNameMap.put(key, tableName);
        return this;
    }

    public static class Result {
        ArrayList<Tuple2<String, Future<Tuple2<Table, TableLoader>>>> futureArray;

        protected Result(ArrayList<Tuple2<String, Future<Tuple2<Table, TableLoader>>>> futureArray) {
            this.futureArray = futureArray;
        }

        public ArrayList<Tuple3<String, Table, TableLoader>> toTuple3List() {
            ArrayList<Tuple3<String, Table, TableLoader>> ret = new ArrayList<>();
            futureArray.forEach(task -> {
                Tuple2<Table, TableLoader> tableTuple = null;
                try {
                    tableTuple = task.f1.get();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                if (tableTuple != null) {
                    ret.add(new Tuple3<>(task.f0, tableTuple.f0, tableTuple.f1));
                }
            });
            return ret;
        }
    }

    /**
     * 线程池并发读取
     *
     * @return
     * @throws InterruptedException
     */
    public Result load() throws InterruptedException {
        ArrayList<Tuple2<String, Future<Tuple2<Table, TableLoader>>>> loadTaskList = new ArrayList<>();
        ThreadPoolExecutor threadPoolExecutor =
                new ThreadPoolExecutor(CORE_POOL_SIZE, MAXIMUM_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.MILLISECONDS,
                        new LinkedBlockingQueue<>());
        this.keyTableNameMap.forEach(
                (key, tableName) -> {
                    loadTaskList.add(
                            new Tuple2<>(
                                    key,
                                    threadPoolExecutor.submit(
                                            new IcebergLoadTaskGenerator(
                                                    TableIdentifier.of(databaseName, tableName),
                                                    icebergCatalogReader,
                                                    speedLayer
                                            )
                                    )
                            )
                    );
                });
        threadPoolExecutor.shutdown();
        Result result = new Result(loadTaskList);
        if (!threadPoolExecutor.awaitTermination(TIME_OUT_MINUTES, TimeUnit.MINUTES)) {
            throw new RuntimeException("Table Loader init Exception");
        }
        return result;
    }

    public ConcurrentIcebergTableLoader setCordPoolSize(int CORE_POOL_SIZE) {
        this.CORE_POOL_SIZE = CORE_POOL_SIZE;
        return this;
    }

    public ConcurrentIcebergTableLoader setMaximumPoolSize(int MAXIMUM_POOL_SIZE) {
        this.MAXIMUM_POOL_SIZE = MAXIMUM_POOL_SIZE;
        return this;
    }

    public ConcurrentIcebergTableLoader setKeepAliveTime(long KEEP_ALIVE_TIME) {
        this.KEEP_ALIVE_TIME = KEEP_ALIVE_TIME;
        return this;
    }

    public ConcurrentIcebergTableLoader setTimeOutMinutes(int TIME_OUT_MINUTES) {
        this.TIME_OUT_MINUTES = TIME_OUT_MINUTES;
        return this;
    }

    /**
     * 多线程具体调用的加载Iceberg表的方法
     */
    public static class IcebergLoadTaskGenerator implements Callable<Tuple2<Table, TableLoader>> {
        private final TableIdentifier tableIdentifier;
        private final IcebergCatalogReader reader;
        private final SpeedLayer speedLayer;

        public IcebergLoadTaskGenerator(
                TableIdentifier tableIdentifier,
                IcebergCatalogReader reader,
                SpeedLayer speedLayer
        ) {
            this.tableIdentifier = tableIdentifier;
            this.reader = reader;
            this.speedLayer = speedLayer;
        }

        @Override
        public Tuple2<Table, TableLoader> call() {
            if (speedLayer != null) {
                IcebergTableMeta icebergTableMeta = speedLayer.getIcebergTableMeta(tableIdentifier);
                if (icebergTableMeta != null) {
                    return new Tuple2<>(icebergTableMeta.table, icebergTableMeta.tableLoader);
                }
            }
            TableLoader tableLoader = reader.getTableLoaderInstance(tableIdentifier);
            try {
                Table table = tableLoader.loadTable();
                if (speedLayer != null) {
                    speedLayer.setIcebergTableMeta(
                            tableIdentifier,
                            IcebergTableMeta.builder()
                                    .database(tableIdentifier.namespace().toString())
                                    .tableName(tableIdentifier.name())
                                    .table(table)
                                    .tableLoader(tableLoader)
                                    .build()
                    );
                }
                return new Tuple2<>(table, tableLoader);
            } catch (NoSuchTableException e) {
                return null;
            }
        }
    }
}