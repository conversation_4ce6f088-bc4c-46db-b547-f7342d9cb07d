package com.tencent.andata.utils;

import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import org.apache.commons.lang.StringUtils;

public class WorkDurationUtils {

    /**
     * 计算工作日时长
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param holidayDays 节假日list的string
     * @return 工作日时长
     */
    public static double getWorkDuration(String startTime, String endTime, String holidayDays) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return 0;
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        double workDuration = 0;
        try {
            Date start = format.parse(startTime);
            Date end = format.parse(endTime);
            // 结束时间
            if (end.before(start)) {
                return 0;
            }
            if (StringUtils.isBlank(holidayDays)) {
                return Double.valueOf(end.getTime() - start.getTime()) / 1000;
            }
            String[] holidayDayArray = holidayDays.split(",");
            List<String> holidayDayList = new ArrayList<>(Arrays.asList(holidayDayArray));
            workDuration = Double.valueOf(end.getTime() - start.getTime()) / 1000;
            int days = getBetweenDays(startTime, endTime);
            for (int day = 0; day <= days; day++) {
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(start);
                calendar.add(Calendar.DATE, day); //把日期往后增加一天,整数  往后推,负数往前移动
                SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");//这个时间就是日期往后推一天的结果
                String presentDate = sdf1.format(calendar.getTime());
                // 计算工作时长, 统计工作日期
                if (holidayDayList.contains(presentDate)) {
                    if (day == 0) {
                        // 开始
                        workDuration -= (24 * 3600 - ((start.getHours() * 60
                                + start.getMinutes()) * 60 + start.getSeconds()));
                    } else if (day == days) {
                        // 结束日期
                        workDuration -= ((end.getHours() * 60 + end.getMinutes()) * 60 + end.getSeconds());
                    } else {
                        // 开始-->结束日期之间
                        workDuration -= 24 * 3600;
                    }
                }
            }
            if (workDuration < 0) {
                workDuration = 0;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return workDuration;
    }

    /**
     * 计算时间之间的工作日
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param holidayDays 节假日list的string
     * @return 返回工作日
     */
    public static List<String> getWorkDate(String startTime, String endTime, String holidayDays) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS");
        List<String> workDate = new ArrayList<>();
        LocalDateTime startDateTime = LocalDateTime.parse(startTime, formatter);
        LocalDateTime endDateTime = LocalDateTime.parse(endTime, formatter);

        if (endDateTime.isBefore(startDateTime)) {
            return null;
        }
        LocalDate currentDate = startDateTime.toLocalDate();

        while (!currentDate.isAfter(endDateTime.toLocalDate())) {
            if (!holidayDays.contains(currentDate.toString())) {
                workDate.add(currentDate.toString());
            }
            currentDate = currentDate.plusDays(1);
        }
        return workDate;
    }

    /**
     * 获取两个时间之间的间隔天数
     *
     * @param startTimeStr 开始时间
     * @param endTimeStr 结束时间
     * @return 天数          例如2018-11-01 00:00:00至2018-11-30 23:59:59  返回为30
     */
    public static int getBetweenDays(String startTimeStr, String endTimeStr) {
        int betweenDays = 0;
        Date startTime = strToDateLong(startTimeStr);
        Date endTime = strToDateLong(endTimeStr);

        long start = startTime.getTime();
        long end = endTime.getTime();

        betweenDays = (int) (Math.abs(end - start) / (24 * 3600 * 1000));

        return betweenDays;
    }

    // strToDateLong
    public static Date strToDateLong(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        ParsePosition pos = new ParsePosition(0);
        return formatter.parse(strDate, pos);
    }
}
