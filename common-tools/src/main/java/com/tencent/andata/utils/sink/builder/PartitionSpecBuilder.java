package com.tencent.andata.utils.sink.builder;

import com.tencent.andata.utils.cdc.conf.input.InputPartitionArgs;
import com.tencent.andata.utils.sink.partition.PartitionField;
import com.tencent.andata.utils.sink.partition.PartitionSpec;
import com.tencent.andata.utils.sink.partition.PartitionStrategyFactory;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

public class PartitionSpecBuilder {

    public InputPartitionArgs[] inputPartitionArgs;

    public PartitionSpecBuilder setInputPartitionArgs(InputPartitionArgs[] inputPartitionArgs) {
        this.inputPartitionArgs = inputPartitionArgs;
        return this;
    }

    public PartitionSpec build() throws ParseException {
        PartitionSpec basePartitionSpec = new PartitionSpec();
        List<PartitionField> partitionFields = new ArrayList<>();
        if (inputPartitionArgs == null) {
            return null;
        }
        for (InputPartitionArgs spec : inputPartitionArgs) {
            partitionFields.add(
                    new PartitionField(
                            PartitionStrategyFactory.getPartitionStrategy(spec.strategy),
                            spec.field
                    )
            );
        }
        basePartitionSpec.fields = partitionFields;
        return basePartitionSpec;
    }

}