package com.tencent.andata.utils.rowdata;

import java.io.IOException;
import java.io.Serializable;
import java.nio.ByteBuffer;
import java.util.List;
import net.jpountz.lz4.LZ4Compressor;
import net.jpountz.lz4.LZ4Factory;
import net.jpountz.lz4.LZ4FastDecompressor;
import org.apache.flink.core.memory.DataInputDeserializer;
import org.apache.flink.core.memory.DataOutputSerializer;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.RowDataSerializer;
import org.apache.flink.table.types.logical.RowType;

public class RowDataConverter implements Serializable {
    private transient LZ4FastDecompressor lz4FastDecompressor;
    private transient LZ4Compressor lz4Compressor;
    protected final RowDataSerializer rowDataSerializer;
    protected final RowType rowType;
    protected boolean isDataCompress;

    public RowDataConverter(RowType rowType) {
        this.rowDataSerializer = new RowDataSerializer(rowType);
        this.rowType = rowType;
        this.isDataCompress = true;
    }

    public RowDataConverter(RowType rowType, boolean compress) {
        this.rowDataSerializer = new RowDataSerializer(rowType);
        this.rowType = rowType;
        this.isDataCompress = compress;
    }

    void lazyLoadLZ4() {
        if (lz4Compressor == null) {
            LZ4Factory LZ4_FACTORY = LZ4Factory.fastestInstance();
            lz4FastDecompressor = LZ4_FACTORY.fastDecompressor();
            lz4Compressor = LZ4_FACTORY.fastCompressor();
        }
    }

    public byte[] compressBytesUsingLZ4(byte[] bytes) {
        this.lazyLoadLZ4();

        final int len = lz4Compressor.maxCompressedLength(bytes.length);
        final byte[] out = new byte[len];
        final int compressedSize = lz4Compressor.compress(bytes, 0, bytes.length, out, 0);
        return ByteBuffer.allocate(compressedSize + Integer.BYTES)
                .putInt(bytes.length)
                .put(out, 0, compressedSize)
                .array();
    }

    public byte[] decompressBytesUsingLZ4(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        this.lazyLoadLZ4();

        final int decompressedLen = ByteBuffer.wrap(bytes).getInt();
        final byte[] out = new byte[decompressedLen];
        lz4FastDecompressor.decompress(bytes, Integer.BYTES, out, 0, out.length);
        return out;
    }

    public byte[] serializeRowDataToBytes(RowData rowData) throws IOException {
        DataOutputSerializer outputView = new DataOutputSerializer(256);
        this.rowDataSerializer.serialize(rowData, outputView);
        byte[] bytes = outputView.wrapAsByteBuffer().array();

        if (isDataCompress) {
            bytes = compressBytesUsingLZ4(bytes);
        }
        return bytes;
    }

    public RowData deserializeBytesToBinaryRowData(byte[] bytes) throws IOException {
        if (isDataCompress) {
            bytes = decompressBytesUsingLZ4(bytes);
        }
        return this.rowDataSerializer.deserialize(new DataInputDeserializer(bytes));
    }

    /**
     * 从RowData转到GenericRowData
     * @param rowData
     * @param fields
     * @return
     */
    public static GenericRowData copyGenericRowDataFromRowData(RowData rowData, List<RowType.RowField> fields) {
        int rowDataArity = rowData.getArity();

        GenericRowData retRowData = new GenericRowData(fields.size());
        // 如果fields比arity多，那么多余的就用null补上
        for (int i = 0; i < fields.size(); i++) {
            if (i < rowDataArity) {
                RowType.RowField field = fields.get(i);
                RowData.FieldGetter getter = RowData.createFieldGetter(field.getType(), i);
                retRowData.setField(i, getter.getFieldOrNull(rowData));
            } else {
                retRowData.setField(i, null);
            }

        }
        retRowData.setRowKind(rowData.getRowKind());
        return retRowData;
    }

    public GenericRowData deserializeBytesToGenericRowData(byte[] bytes) throws IOException {
        RowData binaryRowData = deserializeBytesToBinaryRowData(bytes);
        return copyGenericRowDataFromRowData(binaryRowData,rowType.getFields());
    }

}