package com.tencent.andata.utils.operator.keyby;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.RowData.FieldGetter;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;

public class RowDataPKKeySelector implements KeySelector<RowData, String> {

    private final RowType rowType;
    private final List<String> pkList;
    private final ArrayList<Tuple3<String, FieldGetter, LogicalType>> fieldGetterList;

    public RowDataPKKeySelector(List<String> pkList, RowType rowType,
                                ArrayList<Tuple3<String, FieldGetter, LogicalType>> fieldGetterList) {
        this.pkList = pkList;
        this.fieldGetterList = fieldGetterList;
        this.rowType = rowType;
    }

    @Override
    public String getKey(RowData rowData) throws Exception {
        return pkList.stream()
                .map(rowType::getFieldIndex)
                .map(fieldGetterList::get)
                .map(t3 -> t3.f1)
                .map(getter -> Objects.toString(getter.getFieldOrNull(rowData), "NULL"))
                .reduce("", (s, s2) -> s + s2);
    }
}