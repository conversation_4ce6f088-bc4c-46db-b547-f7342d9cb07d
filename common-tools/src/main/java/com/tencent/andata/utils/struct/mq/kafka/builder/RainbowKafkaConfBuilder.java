package com.tencent.andata.utils.struct.mq.kafka.builder;

import com.tencent.andata.utils.struct.mq.kafka.KafkaConf;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;

import java.util.Properties;

public class RainbowKafkaConfBuilder implements KafkaConfBuilder {
    public String rainbowGroupName;

    public RainbowKafkaConfBuilder(String rainbowGroupName) {
        this.rainbowGroupName = rainbowGroupName;
    }


    @Override
    public KafkaConf build() throws Exception {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        KVConfBuilder<KafkaConf> kvConfBuilder = new KVConfBuilder<>(KafkaConf.class)
                .setRainbowUtils(rainbowUtils);
        return kvConfBuilder
                .setGroupName(rainbowGroupName)
                .build();
    }
}
