package com.tencent.andata.utils.sink.partition.strategy;

import java.text.ParseException;

public abstract class BasePartitionStrategy {
    public String name;

    public BasePartitionStrategy(String strategyString) throws ParseException {
        this.init(strategyString);
    }

    /**
     * 根据策略字符串进行初始化, Demo如下
     * hash:32
     * range:month:2:2022-01-01
     * range:day
     *
     * @param strategyString 策略字符串
     * @throws Exception
     */
    public abstract void init(String strategyString) throws ParseException;
}