package com.tencent.andata.utils.lookup.jdbc;

import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.calcite.tools.ValidationException;

public class HashMapJDBCLookupQuery extends StandardJDBCLookupQuery<HashMap<String, Object>, List<HashMap<String, Object>>> {
    public HashMapJDBCLookupQuery(
            DatabaseEnum databaseEnum,
            DatabaseConf databaseConf,
            JDBCQuerySqlBuilder jdbcQuerySqlBuilder
    ) throws ValidationException {
        super(databaseEnum, databaseConf, new HashMapJDBCQueryParser(), jdbcQuerySqlBuilder);
        ((JDBCSqlBuilderImpl) jdbcQuerySqlBuilder).databaseEnum(databaseEnum);
    }

    public static class HashMapJDBCQueryParser implements JDBCQueryParser<HashMap<String, Object>, List<HashMap<String, Object>>> {

        @Override
        public PreparedStatement setStatement(
                HashMap<String, Object> input,
                PreparedStatement statement,
                Map<String, Integer> conditionKeyIndexMap
        ) throws SQLException {
            for (String key : conditionKeyIndexMap.keySet()) {
                final Integer index = conditionKeyIndexMap.get(key);
                Object value = input.getOrDefault(key, null);
                statement.setObject(index, value);
            }
            return statement;
        }

        @Override
        public List<HashMap<String, Object>> parseResultSet(ResultSet rs) throws SQLException {
            //创建对象
            List<HashMap<String, Object>> res = new ArrayList<>();
            while (rs.next()) {  //行遍历
                final ResultSetMetaData metaData = rs.getMetaData();
                HashMap<String, Object> tmp = new HashMap<>();
                for (int i = 0; i < metaData.getColumnCount(); i++) {  //列遍历
                    //获取列名
                    String columnName = metaData.getColumnName(i + 1);
                    //获取列值
                    Object value = rs.getObject(columnName);
                    //存储对象
                    tmp.put(columnName, value);
                }
                res.add(tmp);
            }
            return res;
        }
    }

    @Override
    public String toString() {
        return super.toString();
    }
}