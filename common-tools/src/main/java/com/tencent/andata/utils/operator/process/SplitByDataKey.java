package com.tencent.andata.utils.operator.process;

import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.data.RowData;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.io.Serializable;
import java.util.HashMap;

/**
 * 数据分流算子，根据RowData计算分流的Key
 */
public class SplitByDataKey extends ProcessFunction<MessageRowData, RowData> {

    private final TagKeyGetter genericTagKeyGetter;
    private HashMap<String, OutputTag<RowData>> tagMap;

    @Override
    public void processElement(MessageRowData msgRowData,
                               ProcessFunction<MessageRowData, RowData>.Context context,
                               Collector<RowData> collector) throws Exception {
        String tagKey = genericTagKeyGetter.getTagKey(msgRowData);
        context.output(getTag(tagKey), msgRowData);
    }

    public interface TagKeyGetter extends Serializable {
        public String getTagKey(RowData rowData);
    }

    /**
     * 通过外部传参Tag映射以及Tag key的获取回调方法
     *
     * @param tagKeyGetter 获取Key的回调
     */
    public SplitByDataKey(TagKeyGetter tagKeyGetter) {
        this.genericTagKeyGetter = tagKeyGetter;
        this.tagMap = new HashMap<>();
    }

    /**
     * 运行时维护OutPutTag
     *
     * @param key Tag键值
     * @return
     */
    public OutputTag<RowData> getTag(String key) {
        if (tagMap.containsKey(key)) {
            return tagMap.get(key);
        } else {
            OutputTag<RowData> tag = new OutputTag<RowData>(key) {
            };
            tagMap.put(key, tag);
            return tag;
        }
    }

}