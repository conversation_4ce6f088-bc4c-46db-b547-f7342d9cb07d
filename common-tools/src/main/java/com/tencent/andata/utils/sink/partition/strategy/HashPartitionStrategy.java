package com.tencent.andata.utils.sink.partition.strategy;

import com.google.common.base.Preconditions;
import com.tencent.andata.utils.CommonUtils;
import com.tencent.andata.utils.Constant;

import java.text.ParseException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Hash分区策略，带上size则表示为bucket分区
 */
public class HashPartitionStrategy extends BasePartitionStrategy {
    public int size;

    public HashPartitionStrategy(String strategyString) throws ParseException {
        super(strategyString);
    }

    @Override
    public void init(String strategyString)  {
        List<String> split = Arrays.stream(
                strategyString.split(
                        Constant.PARTITION_STRATEGY_PARSE_DELIMITER
                )
        ).collect(Collectors.toList());
        this.name = CommonUtils.listGetOrDefault(0, null, split);
        String sizeStr = CommonUtils.listGetOrDefault(1, null, split);
        if (sizeStr != null) {
            this.size = Integer.parseInt(sizeStr);
        }
        // name为非空属性
        Preconditions.checkNotNull(this.name);
    }

    @Override
    public String toString() {
        return "HashPartitionStrategy{"
                + "name='" + name + '\''
                + ", size=" + size
                + '}';
    }
}