package com.tencent.andata.utils.ddl.strategy;

import com.tencent.andata.utils.connector.jdbc.catalog.JdbcCatalogUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;

import java.util.Arrays;
import java.util.HashMap;
import java.util.stream.Collectors;

import org.apache.flink.table.catalog.ObjectPath;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;

public class StarRocksTableBuilderStrategy extends BaseTableBuilderStrategy {

    private final DatabaseConf dbConf;
    private final DatabaseEnum dbEnum;
    private final String tableName;
    private final Integer httpPort;

    private String primaryKeyName;
    private String[] primaryKeyList;


    public StarRocksTableBuilderStrategy(String tableName, DatabaseEnum dbEnum, DatabaseConf dbConf, Integer httpPort) {
        this.dbConf = dbConf;
        this.dbEnum = dbEnum;
        this.tableName = tableName;
        this.httpPort = httpPort;
        this.tableProperties = new HashMap<String, String>() {{
            put("connector", "starrocks");
            put("jdbc-url", String.format("jdbc:mysql://%s:%d", dbConf.dbHost, dbConf.dbPort));
            put("load-url", String.format("%s:%d", dbConf.dbHost, httpPort));
            put("username", dbConf.userName);
            put("password", dbConf.password);
            put("database-name", dbConf.dbName);
            put("table-name", tableName);
        }};
    }

    public StarRocksTableBuilderStrategy readModel() {
        // 读和写参数不一致
        this.tableProperties.remove("load-url");
        this.tableProperties.put("scan-url", String.format("%s:%d", dbConf.dbHost, httpPort));
        return this;
    }

    public StarRocksTableBuilderStrategy sinkParallelism(String value) {
        this.tableProperties.put("sink.parallelism", value);
        return this;
    }

    public StarRocksTableBuilderStrategy primaryKeyName(String pk) {
        this.primaryKeyName = pk;
        return this;
    }

    public StarRocksTableBuilderStrategy primaryKeyList(String[] pkList) {
        this.primaryKeyList = pkList;
        return this;
    }

    public StarRocksTableBuilderStrategy property(String key, String value) {
        this.tableProperties.put(key, value);
        return this;
    }

    @Override
    public String getSchemaString() throws TableNotExistException {

        String schemaStr = JdbcCatalogUtils.createCatalog(
                        "cdc_catalog",
                        this.dbConf.dbName,
                        this.dbConf.userName,
                        this.dbConf.password,
                        String.format("jdbc:%s://%s:%d/", dbEnum.toString(), dbConf.dbHost, dbConf.dbPort),
                        this.dbEnum
                )
                .getTable(new ObjectPath(dbConf.dbName, tableName))
                .getUnresolvedSchema().toString();

        if (this.primaryKeyName != null || (this.primaryKeyList != null && this.primaryKeyList.length > 0)) {
            String pkStr =
                    this.primaryKeyList != null ? Arrays.stream(primaryKeyList).map(s -> String.format("`%s`", s))
                            .collect(Collectors.joining(",")) : String.format("`%s`", this.primaryKeyName);
            String pkDDL = String.format("PRIMARY KEY (%s) NOT ENFORCED", pkStr);

            // 去掉DDL的最后一个反括号
            schemaStr = String.format("%s  ,%s\n" + ")", schemaStr.substring(0, schemaStr.length() - 1), pkDDL);
        }

        return schemaStr;
    }
}
