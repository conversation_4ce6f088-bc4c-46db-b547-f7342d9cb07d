package com.tencent.andata.utils.lookup.jdbc;

import com.tencent.andata.utils.lookup.impl.RedisCacheImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.util.HashMap;
import java.util.List;
import org.apache.calcite.tools.ValidationException;

public class CachedHashMapJDBCLookupQuery extends HashMapJDBCLookupQuery {

    public CachedHashMapJDBCLookupQuery(
            DatabaseEnum databaseEnum,
            DatabaseConf databaseConf,
            JDBCQuerySqlBuilder jdbcQuerySqlBuilder,
            String redisHost,
            int redisPort
    ) throws ValidationException {
        super(databaseEnum, databaseConf, jdbcQuerySqlBuilder);
        // 创建Redis缓存并设置到父类
        RedisCacheImpl<HashMap<String, Object>, List<HashMap<String, Object>>> redisCache =
            RedisCacheImpl.createListCache(redisHost, redisPort, 3600);
        this.setCache(redisCache);
    }

    public CachedHashMapJDBCLookupQuery(
            DatabaseEnum databaseEnum,
            DatabaseConf databaseConf,
            JDBCQuerySqlBuilder jdbcQuerySqlBuilder,
            String redisHost,
            int redisPort,
            int expireSeconds
    ) throws ValidationException {
        super(databaseEnum, databaseConf, jdbcQuerySqlBuilder);
        // 创建Redis缓存（自定义过期时间）并设置到父类
        RedisCacheImpl<HashMap<String, Object>, List<HashMap<String, Object>>> redisCache =
            RedisCacheImpl.createListCache(redisHost, redisPort, expireSeconds);
        this.setCache(redisCache);
    }
}