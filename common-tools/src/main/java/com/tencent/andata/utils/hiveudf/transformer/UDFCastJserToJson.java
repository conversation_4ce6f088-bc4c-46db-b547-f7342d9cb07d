package com.tencent.andata.utils.hiveudf.transformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.utils.hiveudf.utils.ArgsChecker;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;

import org.apache.commons.lang3.SerializationException;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.BinaryObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.BooleanObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Java Serialization 对象 -> Json
 * 即二进制转Json。需要保证对象可在运行环境找到，建议使用自带的类型
 * Usage:
 * SELECT udf_cast_jser_to_json(BYTES_, false/true) FROM andata_dev.ods_tbpm_var_byte_array LIMIT 10;
 */
public class UDFCastJserToJson extends GenericUDF {
    private ArrayList<ObjectInspector> argInspector;
    ObjectMapper objectMapper;
    private static final Logger logger = LoggerFactory.getLogger(UDFCastJserToJson.class);


    @Override
    public ObjectInspector initialize(ObjectInspector[] arguments)
            throws UDFArgumentLengthException, UDFArgumentTypeException {
        ArgsChecker
                .newBuilder()
                .setFuncName("udf_cast_jser_to_json")
                .addColumnClass(BinaryObjectInspector.class)
                .addColumnClass(BooleanObjectInspector.class)
                .build()
                .checkArgsNumbersAndType(arguments);

        objectMapper = new ObjectMapper();
        argInspector = new ArrayList<>(Arrays.asList(arguments));

        return PrimitiveObjectInspectorFactory.javaStringObjectInspector;
    }

    @Override
    public Object evaluate(DeferredObject[] arguments) throws HiveException {
        byte[] bytes = ((BinaryObjectInspector) argInspector.get(0)).getPrimitiveJavaObject(arguments[0].get());
        boolean setErrorElementToEmpty = ((BooleanObjectInspector) argInspector.get(1)).get(arguments[1].get());
        return deserialize(bytes, setErrorElementToEmpty);
    }

    private Object deserialize(byte[] bytes, boolean setErrorElementToEmpty)  {
        Object retObj;
        try {
            if (Objects.isNull(bytes) || bytes.length == 0) {
                return null;
            }
            // 直接序列化，如果无法序列化，则根据入参配置来决定是否忽略报错
            retObj = SerializationUtils.deserialize(bytes);
            if (!(retObj instanceof String)) {
                retObj = objectMapper.writeValueAsString(retObj);
            }
        } catch (JsonProcessingException | NullPointerException e) {
            // 如果配置了 将错误数据设置为空字符串，那么碰到了脏数据就返回空字符串，否则就返回null
            if (setErrorElementToEmpty) {
                return "";
            }
            return null;
        } catch (SerializationException e) {
            // 对于不是SER对象的数据，直接转成String返回。如果报错，那么直接抛出
            logger.error("UDFCastJserToJson error e:", e.getMessage());
            return e.getMessage();
        }
        return retObj;
    }

    @Override
    public String getDisplayString(String[] children) {
        return getStandardDisplayString("udf_cast_jser_to_json", children);
    }

}