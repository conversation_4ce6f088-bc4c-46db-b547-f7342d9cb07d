package com.tencent.andata.utils.cdc.conf.fieldgetter;

import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.LogicalType;

import java.io.Serializable;
import java.util.ArrayList;
import com.google.common.base.Preconditions;
import org.apache.flink.table.types.logical.RowType;

import java.util.List;

public class BaseFieldGetter implements Serializable {

    public ArrayList<Tuple3<String, RowData.FieldGetter, LogicalType>> fieldGetterList;

    static public class BaseFieldGetterBuilder {

        private RowType rowType;

        public BaseFieldGetterBuilder setRowType(RowType rowType) {
            this.rowType = rowType;
            return this;
        }

        public BaseFieldGetter build() {
            Preconditions.checkNotNull(rowType);

            BaseFieldGetter baseFieldGetter = new BaseFieldGetter();
            baseFieldGetter.fieldGetterList = getFieldGetterList(rowType);
            return baseFieldGetter;
        }


        private ArrayList<Tuple3<String, RowData.FieldGetter, LogicalType>> getFieldGetterList(RowType rowType) {
            List<RowType.RowField> fields = rowType.getFields();

            ArrayList<Tuple3<String, RowData.FieldGetter, LogicalType>> retFieldGetterList = new ArrayList<>();
            for (int i = 0; i < fields.size(); ++i) {
                LogicalType logicalType = fields.get(i).getType();

                Tuple3<String, RowData.FieldGetter, LogicalType> tuple = new Tuple3<>();
                tuple.setFields(fields.get(i).getName(), RowData.createFieldGetter(logicalType, i), logicalType);
                retFieldGetterList.add(tuple);
            }
            return retFieldGetterList;
        }
    }

    public static BaseFieldGetterBuilder builder() {
        return new BaseFieldGetterBuilder();
    }

}