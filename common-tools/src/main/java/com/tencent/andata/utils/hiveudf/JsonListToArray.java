package com.tencent.andata.utils.hiveudf;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.StringObjectInspector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Description(name = "json_list_str_to_array",
        value = "_FUNC_(CHAR) - Returns a  Array ßHive structure."
)
public class JsonListToArray extends GenericUDF {

    private static final Logger LOG = LoggerFactory.getLogger(JsonListToArray.class);
    ObjectMapper objectMapper;
    StringObjectInspector elementOI;

    /**
     * run evaluateValue
     */
    public List<String> evaluateValue(String s) throws IOException {
        // TODO：如果为null，那么就返回空List。但这个不太好，后面加个参数来确定函数行为
        try {
            if (s == null) {
                throw new NullPointerException();
            }
            List<Object> objects = objectMapper.readValue(
                    s,
                    objectMapper
                            .getTypeFactory()
                            .constructCollectionType(List.class, Object.class)
            );

            List<String> list = new ArrayList<>();
            for (Object obj : objects) {
                String writeValueAsString = objectMapper.writeValueAsString(obj);
                list.add(writeValueAsString);
            }
            return list;
        } catch (MismatchedInputException | NullPointerException | JsonParseException e) {
            return null;
        }
    }

    @Override
    public ObjectInspector initialize(ObjectInspector[] objectInspectors) throws UDFArgumentException {
        if (objectInspectors.length != 1) {
            throw new UDFArgumentException(
                    " ToJson takes an object as an argument");
        }
        ObjectInspector oi = objectInspectors[0];
        if (!(oi instanceof StringObjectInspector)) {
            throw new UDFArgumentException("first argument  be a string");
        }

        this.objectMapper = new ObjectMapper();
        this.elementOI = (StringObjectInspector) oi;
        return ObjectInspectorFactory
                .getStandardListObjectInspector(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
    }


    @Override
    public Object evaluate(DeferredObject[] deferredObjects) throws HiveException {
        String arg = elementOI.getPrimitiveJavaObject(deferredObjects[0].get());

        try {
            return evaluateValue(arg);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public String getDisplayString(String[] strings) {
        return "stringToList";
    }
}
