package com.tencent.andata.utils.cdc.deserializer;

import java.util.Map;
import org.apache.kafka.connect.json.JsonConverter;

/**
 * 功能说明参考 {@link  RowKindJsonDeserializationSchemaBase}。
 * 使用官方提供 {@link JsonConverter} 进行序列化。
 */
public class RowKindJsonDeserializationSchemaV1 extends RowKindJsonDeserializationSchemaBase {

    @Override
    protected JsonConverter initializeJsonConverter(Map<String, Object> customConverterConfigs) {
        final JsonConverter jsonConverter = new JsonConverter();
        jsonConverter.configure(customConverterConfigs);
        return jsonConverter;
    }

}
