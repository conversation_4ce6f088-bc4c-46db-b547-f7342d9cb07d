package com.tencent.andata.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class HttpClientUtils implements Serializable {
    private static final Logger logger = LogManager.getLogger(HttpClientUtils.class);

    private final int socketTimeout;
    private final int connectTimeout;
    private final ObjectMapper objectMapper;

    public HttpClientUtils(int socketTimeout, int connectTimeout) {
        this.socketTimeout = socketTimeout;
        this.connectTimeout = connectTimeout;
        objectMapper = new ObjectMapper();
    }

    public String post(String url, Map<String, Object> data) throws IOException {
        return post(url, data, new HashMap<String, String>());
    }

    /**
     * 封装POST请求（Map入参）
     *
     * @param url    请求的路径
     * @param data   请求的参数
     * @param header Header
     * @return res
     * @throws IOException ex
     */
    public String post(String url, Map<String, Object> data, Map<String, String> header) throws IOException {
        return post(url, objectMapper.writeValueAsString(data), header);
    }

    /**
     * 封装POST请求（String入参）
     *
     * @param url  请求的路径
     * @param data String类型数据
     * @return res
     * @throws IOException ex
     */
    public String post(String url, String data, Map<String, String> header) throws IOException {
        //        1、创建HttpClient对象
        HttpClient httpClient = HttpClientBuilder.create().build();
        //        2、创建请求方式的实例
        HttpPost httpPost = new HttpPost(url);
        //        3、添加请求参数(设置请求和传输超时时间)
        RequestConfig requestConfig = RequestConfig
                .custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).build();
        httpPost.setConfig(requestConfig);
        Set<Map.Entry<String, String>> headerSet = header.entrySet();
        for (Map.Entry<String, String> entry : headerSet) {
            httpPost.setHeader(entry.getKey(), entry.getValue());
        }
        final StringEntity stringEntity = new StringEntity(data, StandardCharsets.UTF_8);
        stringEntity.setContentType("application/json");
        stringEntity.setContentEncoding("UTF-8");
        //        设置请求参数
        httpPost.setEntity(stringEntity);
        //        4、发送Http请求
        HttpResponse response = httpClient.execute(httpPost);
        //        5、获取返回的内容
        String result = null;
        int statusCode = response.getStatusLine().getStatusCode();
        if (200 == statusCode) {
            result = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        } else {
            logger.info(String.format("请求第三方接口出现错误，状态码为:{%s}", statusCode));
            return null;
        }
        //        6、释放资源
        httpPost.abort();
        httpClient.getConnectionManager().shutdown();
        return result;
    }

    /**
     * 封装GET请求
     *
     * @param url url
     * @return res
     * @throws IOException ex
     */
    public String get(String url) throws IOException {
        //        1、创建HttpClient对象
        HttpClient httpClient = HttpClientBuilder.create().build();
        //        2、创建请求方式的实例
        HttpGet httpGet = new HttpGet(url);
        //        3、添加请求参数(设置请求和传输超时时间)
        RequestConfig requestConfig = RequestConfig
                .custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).build();
        httpGet.setConfig(requestConfig);
        //        4、发送Http请求
        HttpResponse response = httpClient.execute(httpGet);
        //        5、获取返回的内容
        String result = null;
        int statusCode = response.getStatusLine().getStatusCode();
        if (200 == statusCode) {
            result = EntityUtils.toString(response.getEntity());
        } else {
            logger.info(String.format("请求第三方接口出现错误，状态码为:{%s}", statusCode));
            return null;
        }
        //        6、释放资源
        httpGet.abort();
        httpClient.getConnectionManager().shutdown();
        return result;
    }
}
