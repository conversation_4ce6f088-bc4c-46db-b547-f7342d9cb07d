package com.tencent.andata.utils.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("ARRAY<STRING>")
)
public class StringToArray extends ScalarFunction {

    public @DataTypeHint("ARRAY<STRING>") String[] eval(String value, String delimiter) {
        if (value == null) {
            return null;
        }
        return value.split(delimiter);
    }
}