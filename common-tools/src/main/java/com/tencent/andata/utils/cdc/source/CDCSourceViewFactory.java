package com.tencent.andata.utils.cdc.source;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.cdc.deserializer.RowKindJsonDeserializationSchemaBase.TableIRowKindJson;
import com.tencent.andata.utils.cdc.deserializer.RowKindJsonDeserializationSchemaV2;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.ververica.cdc.connectors.mysql.source.MySqlSource;
import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.DebeziumSourceFunction;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.connector.source.Source;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonToRowDataConverters;
import org.apache.flink.formats.json.JsonToRowDataConverters.JsonToRowDataConverter;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude.Include;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.catalog.ResolvedSchema;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.conversion.RowRowConverter;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;

@Log4j2
public class CDCSourceViewFactory {

    static Properties debeziumProps = new Properties();
    static ObjectMapper mapper = new ObjectMapper().setSerializationInclusion(Include.ALWAYS);


    /**
     * 生成PG的CDC Stream Source
     *
     * @param dbConf 数据库链接信息
     * @param tableList 需要同步的表
     * @return CDC DataStream
     */
    private static DebeziumSourceFunction<TableIRowKindJson> getPostgreSQLSource(DatabaseConf dbConf,
            List<String> tableList) {

        //设置属性
        debeziumProps.setProperty("converters", "dateConverters");
        debeziumProps.setProperty("heartbeat.interval.ms", "60000");
        debeziumProps.setProperty("dateConverters.type", "com.tencent.andata.utils.cdc.CDCDateTimeConverter");

        return PostgreSQLSource.<TableIRowKindJson>builder()
                .hostname(dbConf.dbHost)
                .port(dbConf.dbPort)
                .database(dbConf.dbName)
                .username(dbConf.userName)
                .password(dbConf.password)
                .schemaList("public")
                .tableList(tableList.stream()
                        .map(x -> String.format("%s.%s", "public", x))
                        .collect(Collectors.joining(",")))
                .decodingPluginName("pgoutput")
                .debeziumProperties(debeziumProps)
                .deserializer(new RowKindJsonDeserializationSchemaV2())
                .slotName(String.format("%s_%s", dbConf.dbName, tableList.get(0)))
                .build();
    }

    /**
     * 生成MySQL的CDC Source
     *
     * @param dbConf 数据库链接信息
     * @param tableList 需要同步的表
     * @return CDC DataStream
     */
    private static Source<TableIRowKindJson, ?, ?> getMySQLSource(DatabaseConf dbConf, List<String> tableList) {
        String dbTables = tableList.stream()
                .map(x -> String.format("%s.%s", dbConf.dbName, x))
                .collect(Collectors.joining(","));

        Properties prop = new Properties();
        prop.setProperty("useSSL", "false");

        //自定义时间转换配置
        debeziumProps.setProperty("converters", "dateConverters");
        debeziumProps.setProperty("database.ssl.mode", "disabled");
        debeziumProps.setProperty("dateConverters.type", "com.tencent.andata.utils.cdc.CDCDateTimeConverter");
        return MySqlSource.<TableIRowKindJson>builder()
                .hostname(dbConf.dbHost)
                .port(dbConf.dbPort)
                .jdbcProperties(prop)
                .databaseList(dbConf.dbName)
                .username(dbConf.userName)
                .password(dbConf.password)
                //.serverId(String.valueOf(Math.abs((dbTables).hashCode())))
                .tableList(dbTables)
                .scanNewlyAddedTableEnabled(true)
                .debeziumProperties(debeziumProps)
                .serverTimeZone("Asia/Shanghai")
                .deserializer(new RowKindJsonDeserializationSchemaV2())
                .build();
    }

    /**
     * 根据同步任务来获取source view
     *
     * @param fEnv stream环境
     * @param sourceTblNode 要同步的表
     * @param dbType db类型
     */
    public static void buildSourceView(DatabaseConf dbConf,
            FlinkEnv fEnv, ArrayNode sourceTblNode, DatabaseEnum dbType, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tEnv = fEnv.streamTEnv();
        StreamExecutionEnvironment env = fEnv.env();
        String uid = String.format("%s:%d-%s:%s", dbConf.dbHost, dbConf.dbPort, dbType, dbConf.dbName);
        List<String> sourceTableList = StreamSupport.stream(sourceTblNode.spliterator(), false)
                .map(x -> x.get("rdbTable").asText())
                .collect(Collectors.toList());

        switch (dbType) {
            case PGSQL:
                //注册pg cdc flink table
                TableUtils.pgdbTable2FlinkTable(dbConf, sourceTblNode, dbType, tEnv, "source");
                generateViews(sourceTblNode, tEnv,
                        env.addSource(getPostgreSQLSource(dbConf, sourceTableList)), parameterTool);
                break;
            case MYSQL:
                //注册mysql cdc flink table
                TableUtils.rdbTable2FlinkTable(dbConf, sourceTblNode, dbType, tEnv);
                generateViews(sourceTblNode, tEnv, (DataStreamSource<TableIRowKindJson>)
                        env.fromSource(getMySQLSource(dbConf, sourceTableList),
                                WatermarkStrategy.noWatermarks(), "MySQL Source").uid(uid), parameterTool);
                break;
            default:
                throw new RuntimeException(
                        String.format("Input DBType %s cannot be recognized.", dbType));
        }
    }


    private static void generateViews(ArrayNode sourceTblNode, StreamTableEnvironment tEnv,
            DataStreamSource<TableIRowKindJson> source, ParameterTool parameterTool) {
        final boolean isChangeLog = parameterTool.getBoolean("isChangeLog", true);
        for (JsonNode node : sourceTblNode) {
            final String sourcefTable = node.get("fTable").asText();
            final String sourceTable = node.get("rdbTable").asText();
            final String sourceView = node.get("rdbTable").asText() + "_view";

            /*
             *   使用jdbc的方式获取关系型数据库中表的schema信息，sourceDB在这里又充当了schemaManager角色
             *   更好的方式是使用一个公共的schemaManager中心来获取表的元数据，比如可以使用flink默认的catalog
             *   但是这个catalog是临时的，更好的方式应该是有一个元数据中心
             *   Schema schema = CDCUtils.getRdbmsSchema(dbConf, sourceTable, MYSQL, prop);
             *   RowType rowType = TableUtils.convertFlinkSchemaToRowType(schema, false);
             *   DataType dataType = InternalTypeInfo.of(rowType).getDataType();
             */

            final ResolvedSchema sourceSchema = tEnv.from(sourcefTable).getResolvedSchema();
            String[] fieldNames = sourceSchema.getColumnNames().toArray(new String[0]);
            TypeInformation<?>[] fieldTypeInfos = sourceSchema.getColumnDataTypes()
                    .stream()
                    .map(x -> InternalTypeInfo.of(x.getLogicalType()))
                    .toArray(TypeInformation[]::new);

            LogicalType[] logicalTypes = sourceSchema.getColumnDataTypes()
                    .stream()
                    .map(DataType::getLogicalType)
                    .toArray(LogicalType[]::new);

            DataType dataType = InternalTypeInfo.ofFields(logicalTypes, fieldNames).getDataType();

            // 创建JsonNode到RowData的转换器
            JsonToRowDataConverter runtimeConverter =
                    new JsonToRowDataConverters(false, true, TimestampFormat.SQL)
                            .createRowConverter(RowType.of(logicalTypes, fieldNames));

            // 创建RowData到Row的转换器
            RowRowConverter rowRowConverter = RowRowConverter.create(dataType);

            // 分流匹配对应的表
            final SingleOutputStreamOperator<TableIRowKindJson> filterStream = source
                    .keyBy(TableIRowKindJson::getTable)
                    .filter(value -> value.getTable().equals(sourceTable))
                    .filter(value -> StringUtils.isNotEmpty(value.getJson()))
                    .name(sourceTable);

            if (!isChangeLog) {
                tEnv.createTemporaryView(sourceView, filterStream.filter(
                                x -> x.getRowKind() == RowKind.INSERT || x.getRowKind() == RowKind.UPDATE_AFTER)
                        .map(value -> {
                            JsonNode message = mapper.readValue(value.getJson(), JsonNode.class);
                            RowData rowData = (RowData) runtimeConverter.convert(message);
                            rowData.setRowKind(RowKind.INSERT);
                            return rowRowConverter.toExternal(rowData);
                            //return toRowMapFunc.map(rowData);
                        }).returns(Types.ROW_NAMED(fieldNames, fieldTypeInfos)));
            } else {
                tEnv.createTemporaryView(sourceView, tEnv.fromChangelogStream(filterStream
                        .map(value -> {
                            try {
                                JsonNode message = mapper.readValue(value.getJson(), JsonNode.class);
                                RowData rowData = (RowData) runtimeConverter.convert(message);
                                rowData.setRowKind(value.getRowKind());
                                return rowRowConverter.toExternal(rowData);
                            } catch (Exception e) {
                                log.error("error_data: " + value);
                                log.error("error_msg: " + e.getMessage());
                                Row row = Row.withNames();
                                row.setField("errorData", null);
                                return row;
                            }
                            //return toRowMapFunc.map(rowData);
                        })
                        .filter(x -> !x.getFieldNames(false).contains("errorData"))
                        .returns(Types.ROW_NAMED(fieldNames, fieldTypeInfos))));
            }
        }
    }
}
