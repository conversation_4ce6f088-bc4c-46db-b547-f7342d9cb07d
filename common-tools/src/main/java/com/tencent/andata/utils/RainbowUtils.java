package com.tencent.andata.utils;

import com.tencent.rainbow.base.config.KvGroup;
import com.tencent.rainbow.base.config.RainbowGroup;
import com.tencent.rainbow.base.entity.RainbowInfo;
import com.tencent.rainbow.base.enums.GroupType;
import com.tencent.rainbow.sdk.RainbowSdkApplication;
import java.util.ArrayList;
import java.util.Map;
import java.util.Properties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

public class RainbowUtils {

    private RainbowSdkApplication rainbowSdkApplication = new RainbowSdkApplication();
    private String appID;
    private String envName;
    private String userId;
    private String userSecretKey;
    private JSONUtils jsonUtils;

    public RainbowUtils(Properties properties) {
        appID = (String) properties.get("RAINBOW_APP_ID");
        envName = (String) properties.get("RAINBOW_ENV_NAME");
        userId = (String) properties.get("RAINBOW_USER_ID");
        userSecretKey = (String) properties.get("RAINBOW_SECRET_KEY");
        jsonUtils = new JSONUtils();
    }

    /**
     * 获取KV组的数据，返回一个Map
     *
     * @param group
     * @return Map
     */
    public Map<String, KvGroup.KvDataValue> getKVGroupMap(String group) {
        RainbowInfo rainbowInfo = new RainbowInfo(appID, envName, group, userId, userSecretKey);
        // 获取配置
        RainbowGroup rainbowGroup = rainbowSdkApplication.getGroup(rainbowInfo);
        // 判断获取配置是否成功
        if (rainbowGroup.getErrorCode() != 0) {
            System.out.printf(
                    "获取配置失败！错误码：{%s}；错误原因：{%s}", rainbowGroup.getErrorCode(), rainbowGroup.getErrorMsg());
            return null;
        }
        // 获取配置类型
        GroupType groupType = rainbowGroup.getGroupType();

        // 根据配置类型进行强转后获取配置信息
        if (groupType == GroupType.KV) {
            KvGroup kvGroup = (KvGroup) rainbowGroup;
            // data为kv对应的每一行
            return kvGroup.getData();
        }
        // TODO: 这里还有别的类型，比如File什么的，现在先不支持，但这块最好还是Throw一个NotImplementErr比较好
        return null;
    }

    public String getStringValue(String group, String key) {
        // 根据group和key获取字符串配置
        Map<String, KvGroup.KvDataValue> data = getKVGroupMap(group);
        if (data == null || data.get(key) == null) {
            return "";
        }
        return data.get(key).getValue();
    }

    public ObjectNode getJSONValue(String group, String key) throws JsonProcessingException {
        // 根据group和key获取JSON配置
        String str = getStringValue(group, key);
        return jsonUtils.getJSONObjectNodeByString(str);
    }

    public ArrayList<ObjectNode> getJSONArrayValue(String group, String key)
            throws JsonProcessingException {
        // 根据group和key获取列表配置
        String str = getStringValue(group, key);
        return jsonUtils.getJSONObjectArrayByString(str);
    }

    public ArrayList<String> getKeyListByGroup(String group) {
        // 获取组下的所有配置键
        Map<String, KvGroup.KvDataValue> data = getKVGroupMap(group);
        if (data == null) {
            return new ArrayList<>();
        }
        return new ArrayList<>(data.keySet());
    }
}