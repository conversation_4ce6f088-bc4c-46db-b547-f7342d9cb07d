package com.tencent.andata.utils.ddl.strategy;

import java.util.Arrays;
import java.util.HashMap;
import java.util.stream.Collectors;
import org.apache.iceberg.BaseTable;
import org.apache.iceberg.Table;
import org.apache.iceberg.flink.FlinkSchemaUtil;

/**
 * Iceberg 表生成策略
 */
public class IcebergTableBuilderStrategy extends BaseTableBuilderStrategy {

    private final Table icebergTable;
    private String primaryKeyName;
    private String[] primaryKeyList;

    public IcebergTableBuilderStrategy(Table icebergTable) {
        this.icebergTable = icebergTable;
        String version = String.valueOf(((BaseTable) icebergTable).operations().current().formatVersion());
        // 获取表property
        String[] params = icebergTable.name().split("\\.");
        this.tableProperties = new HashMap<String, String>() {{
            put("connector", "iceberg");
            put("uri", "thrift://ss-qe-nginx-tauth.tencent-distribute.com:8108");
            put("warehouse", icebergTable.location());
            put("format-version", version);
            put("catalog-name", params[0]);
            put("catalog-database", params[1]);
            put("catalog-table", params[2]);
        }};
    }

    public IcebergTableBuilderStrategy primaryKeyName(String pk) {
        this.primaryKeyName = pk;
        return this;
    }

    public IcebergTableBuilderStrategy primaryKeyList(String[] pkList) {
        this.primaryKeyList = pkList;
        return this;
    }

    public IcebergTableBuilderStrategy writeDistributionMode(String value) {
        this.tableProperties.put("write.distribution-mode", value);
        return this;
    }

    public IcebergTableBuilderStrategy cacheEnabled(String value) {
        this.tableProperties.put("cache-enabled", value);
        return this;
    }

    public IcebergTableBuilderStrategy formatVersion(String value) {
        this.tableProperties.put("format-version", value);
        return this;
    }

    public IcebergTableBuilderStrategy writeUpsertEnabled(String value) {
        // 实现Upsert入库还需要在Insert SQL 中添加OPTIONS('equality-field-columns'='pk')
        this.tableProperties.put("table.exec.iceberg.write-upsert-enabled", value);
        return this;
    }

    public IcebergTableBuilderStrategy property(String key, String value) {
        this.tableProperties.put(key, value);
        return this;
    }


    public IcebergTableBuilderStrategy writeBloomFilter(String value) {
        // 对字段联合主键
        String[] primaryArr = value.split(",");
        if (primaryArr == null || primaryArr.length == 0) {
            return this;
        }
        for (int i = 0; i < primaryArr.length; i++) {
            this.tableProperties.put("write.parquet.bloom-filter-enable." + primaryArr[i], "true");
        }
        return this;
    }


    @Override
    public String getSchemaString() {
        String icebergTableSchemal = FlinkSchemaUtil.toSchema(icebergTable.schema()).toSchema().toString();

        if (this.primaryKeyName != null || (this.primaryKeyList != null && this.primaryKeyList.length > 0)) {
            String pkStr = this.primaryKeyList != null
                    ? Arrays.stream(primaryKeyList)
                            .map(s -> String.format("`%s`", s))
                            .collect(Collectors.joining(","))
                    : String.format("`%s`", this.primaryKeyName);
            String pkDDL = String.format("PRIMARY KEY (%s) NOT ENFORCED", pkStr);
            icebergTableSchemal = String.format(
                    "%s  ,%s\n" + ")",
                    // 去掉DDL的最后一个反括号
                    icebergTableSchemal.substring(0, icebergTableSchemal.length() - 1),
                    pkDDL
            );
        }
        return icebergTableSchemal;
    }
}
