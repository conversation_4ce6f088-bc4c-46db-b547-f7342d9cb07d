package com.tencent.andata.utils.connector.jdbc.dialect;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import org.apache.flink.table.catalog.ObjectPath;
import org.apache.flink.table.types.DataType;

/**
 * Separate the jdbc meta-information type to flink table type into the interface.
 */
public interface JdbcDialectTypeMapper {

    DataType mapping(ObjectPath tablePath, ResultSetMetaData metadata, int colIndex)
            throws SQLException;
}