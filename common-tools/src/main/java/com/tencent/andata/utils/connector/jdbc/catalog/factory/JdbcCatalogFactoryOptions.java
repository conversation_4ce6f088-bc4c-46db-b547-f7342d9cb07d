package com.tencent.andata.utils.connector.jdbc.catalog.factory;

import org.apache.flink.annotation.Internal;
import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;
import org.apache.flink.connector.jdbc.catalog.JdbcCatalog;
import org.apache.flink.table.catalog.CommonCatalogOptions;

/**
 * {@link ConfigOption}s for {@link JdbcCatalog}.
 */
@Internal
public class JdbcCatalogFactoryOptions {

    public static final String IDENTIFIER = "jdbc";

    public static final ConfigOption<String> DEFAULT_DATABASE =
            ConfigOptions.key(CommonCatalogOptions.DEFAULT_DATABASE_KEY)
                    .stringType()
                    .noDefaultValue();

    public static final ConfigOption<String> USERNAME =
            ConfigOptions.key("username").stringType().noDefaultValue();

    public static final ConfigOption<String> PASSWORD =
            ConfigOptions.key("password").stringType().noDefaultValue();

    public static final ConfigOption<String> BASE_URL =
            ConfigOptions.key("base-url").stringType().noDefaultValue();

    private JdbcCatalogFactoryOptions() {
    }
}