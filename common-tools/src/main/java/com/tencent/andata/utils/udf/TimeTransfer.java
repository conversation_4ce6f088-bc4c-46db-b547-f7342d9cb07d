package com.tencent.andata.utils.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

public class TimeTransfer extends ScalarFunction {

    public String eval(String time) {
        if (StringUtils.isEmpty(time)) {
            return "";
        }
        String[] timeList = time.split(":");
        if (timeList.length < 3) {
            return "";
        }
        StringBuffer result = new StringBuffer();
        result.append(timeList[0] + "小时");
        result.append(timeList[0] + "分");
        result.append(timeList[0] + "秒");

        return result.toString();
    }

    /*public static void main(String[] arge){
        System.out.println(new TimeTransfer().eval("1:2:11"));
    }*/
}
