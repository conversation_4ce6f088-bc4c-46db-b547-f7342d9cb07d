package com.tencent.andata.utils;

import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Jedis;

public class RedisDimUtils {

    public static String getDimInfo(String tableName, String id) {
        //1.查询Redis
        Jedis jedis = JedisUtils.getJedis();
        String redisKey = "dim:" + tableName + ":" + id;
        String dimInfo = jedis.get(redisKey);

        jedis.close(); // 释放jedis连接，防止连接池被耗尽

        return dimInfo == null ? "" : dimInfo;
    }

    //删除维表数据
    public static void delDimInfo(String tableName, String id) {
        Jedis jedis = JedisUtils.getJedis();
        String redisKey = "dim:" + tableName + ":" + id;
        jedis.del(redisKey);
        jedis.close();
    }

    //写入维表数据
    public static void setDimInfo(String tableName, String id, String dimInfo, int expireSeconds) {
        Jedis jedis = JedisUtils.getJedis();
        String redisKey = "dim:" + tableName + ":" + id;
        if (StringUtils.isNotEmpty(dimInfo)) {
            jedis.set(redisKey, dimInfo);
        }
        if (expireSeconds > 0) {
            jedis.expire(redisKey, expireSeconds);
        }
        jedis.close();
    }
}
