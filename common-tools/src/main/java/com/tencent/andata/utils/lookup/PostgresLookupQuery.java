package com.tencent.andata.utils.lookup;

import com.tencent.andata.utils.struct.DatabaseConf;

public abstract class PostgresLookupQuery <IN, OUT> extends JDBCLookupQuery<IN, OUT> {
    public PostgresLookupQuery(DatabaseConf databaseConf) {
        super();
        // 使用Mysql Driver
        this.driverName = "org.postgresql.Driver";
        // url
        this.url = String.format(
                "jdbc:postgresql://%s:%s/%s",
                databaseConf.dbHost,
                databaseConf.dbPort,
                databaseConf.dbName
        );
        this.userName = databaseConf.userName;
        this.password = databaseConf.password;
    }

}
