package com.tencent.andata.utils.lookup.jdbc;

import com.tencent.andata.utils.lookup.enums.JDBCDriver;
import com.tencent.andata.utils.lookup.impl.AbstractLookupQueryImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import lombok.Getter;
import org.apache.calcite.tools.ValidationException;
import org.apache.flink.util.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractJDBCLookupQuery<IN, OUT> extends AbstractLookupQueryImpl<IN, OUT> {
    private static final Logger LOG = LoggerFactory.getLogger(AbstractJDBCLookupQuery.class);
    private static final int CONNECTION_CHECK_TIMEOUT_SECONDS = 5;

    protected String url;
    protected String userName;
    protected String password;
    protected String driverName;
    protected transient HikariDataSource dataSource;

    // Auto-scaling configuration and metrics
    private transient ScheduledExecutorService autoScalerExecutor;
    private final AtomicLong lastScaleTs = new AtomicLong(0L);
    private final AutoScaleMetrics autoScaleMetrics = new AutoScaleMetrics();
    private final AtomicInteger lowStableRounds = new AtomicInteger(0);
    /**
     * -- GETTER --
     *  获取当前自动扩缩容配置
     */
    @Getter
    private volatile AutoScaleConfig autoScaleConfig = AutoScaleConfig.defaultConfig();

    public AbstractJDBCLookupQuery(DatabaseEnum databaseEnum, DatabaseConf databaseConf) throws ValidationException {
        switch (databaseEnum) {
            case MYSQL:
                this.driverName = JDBCDriver.MYSQL.value;
                break;
            case PGSQL:
                this.driverName = JDBCDriver.PGSQL.value;
                break;
            default:
                throw new ValidationException("Only support mysql and pgsql!");
        }
        this.url = String.format(
                "jdbc:%s://%s:%s/%s",
                databaseEnum,
                databaseConf.dbHost,
                databaseConf.dbPort,
                databaseConf.dbName
        );
        this.userName = databaseConf.userName;
        this.password = databaseConf.password;

        if (databaseEnum == DatabaseEnum.MYSQL) {
            this.url += "?characterEncoding=utf-8" +
                    "&useOldAliasMetadataBehavior=true" +
                    "&zeroDateTimeBehavior=convertToNull" +
                    "&tinyInt1isBit=false" +
                    "&useSSL=false";
        }
    }

    abstract protected OUT executeQuery(Connection connection, IN in) throws Exception;

    @Override
    protected OUT doQuery(IN in) {
        try (Connection connection = dataSource.getConnection()) {
            return this.executeQuery(connection, in);
        } catch (SQLException e) {
            LOG.error("数据库连接异常 - URL: {}, 用户: {}, SQL状态: {}, 错误码: {}, 错误信息: {}",
                url, userName, e.getSQLState(), e.getErrorCode(), e.getMessage(), e);
            throw new RuntimeException("数据库查询失败: " + e.getMessage(), e);
        } catch (Exception e) {
            LOG.error("查询执行异常 - 输入参数: {}, 异常类型: {}, 错误信息: {}",
                in, e.getClass().getName(), e.getMessage(), e);
            throw new RuntimeException("查询执行失败: " + e.getMessage(), e);
        }
    }

    @Override
    protected void onRetryCallback() {
        try {
            if (this.dataSource == null || this.dataSource.isClosed()) {
                this.refresh();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void open() throws SQLException {
        Preconditions.checkNotNull(driverName);
        Preconditions.checkNotNull(url);
        Preconditions.checkNotNull(userName);
        Preconditions.checkNotNull(password);

        // 初始化连接池
        HikariConfig config = new HikariConfig();
        config.setDriverClassName(driverName);
        config.setJdbcUrl(url);
        config.setUsername(userName);
        config.setPassword(password);

        // 连接池配置
        config.setMaximumPoolSize(5);
        config.setMinimumIdle(5);
        config.setIdleTimeout(600000); // 10分钟（释放闲置）
        config.setConnectionTimeout(20000);
        config.setMaxLifetime(1800000); // 30分钟（避免雪崩）
        config.setKeepaliveTime(30000); // 30秒心跳（关键！）
        config.setAutoCommit(true);
        config.setPoolName("HikariPool-Lookup");

        config.addDataSourceProperty("connectionTestQuery", "SELECT 1");
        config.setInitializationFailTimeout(0);  // 启动失败快速响应
        config.setLeakDetectionThreshold(60000); // 60秒泄漏检测

        // 测试连接的有效性
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(CONNECTION_CHECK_TIMEOUT_SECONDS * 1000L);

        this.dataSource = new HikariDataSource(config);

        startAutoScalerIfEnabled();
    }

    public void close() throws SQLException {
        stopAutoScaler();
        if (this.dataSource != null) {
            try {
                this.dataSource.close();
            } finally {
                this.dataSource = null;
            }
        }
    }

    private void refresh() throws Exception {
        this.close();
        this.open();
    }

    private void startAutoScalerIfEnabled() {
        final AutoScaleConfig config = this.autoScaleConfig;
        if (!config.isAutoScaleEnabled()) {
            return;
        }

        // 线程安全检查：确保dataSource已初始化且可用
        if (this.dataSource == null || this.dataSource.isClosed()) {
            LOG.warn("Cannot start AutoScaler: DataSource is null or closed");
            return;
        }

        // 避免重复创建executor
        if (this.autoScalerExecutor != null && !this.autoScalerExecutor.isShutdown()) {
            return;
        }

        try {
            this.autoScalerExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "HikariPool-AutoScaler-" + System.currentTimeMillis());
                t.setDaemon(true);
                t.setUncaughtExceptionHandler((thread, ex) ->
                        LOG.error("Uncaught exception in AutoScaler thread: {}", ex.getMessage()));
                return t;
            });

            // 延迟启动，给连接池一些初始化时间
            final long period = config.getPeriodMs();
            this.autoScalerExecutor.scheduleAtFixedRate(this::autoScaleTick, period, period, TimeUnit.MILLISECONDS);
            LOG.info("Hikari AutoScaler started with config: {}", config);
        } catch (Exception e) {
            LOG.error("Failed to start AutoScaler: {}", e.getMessage());
        }
    }

    private void stopAutoScaler() {
        if (this.autoScalerExecutor != null) {
            try {
                // 优雅关闭：先尝试正常关闭
                this.autoScalerExecutor.shutdown();

                // 等待5秒让任务完成
                if (!this.autoScalerExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    // 如果5秒内没有完成，强制关闭
                    this.autoScalerExecutor.shutdownNow();

                    // 再等待5秒确认关闭
                    if (!this.autoScalerExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        LOG.warn("AutoScaler executor did not terminate gracefully");
                    }
                }
            } catch (InterruptedException e) {
                // 如果当前线程被中断，立即强制关闭
                this.autoScalerExecutor.shutdownNow();
                // 恢复中断状态
                Thread.currentThread().interrupt();
                LOG.warn("AutoScaler shutdown interrupted");
            } catch (Exception e) {
                LOG.warn("Error during AutoScaler shutdown: {}", e.getMessage());
            } finally {
                this.autoScalerExecutor = null;
            }
        }
    }

    private void autoScaleTick() {
        try {
            // 获取当前配置快照，确保一次tick中使用一致的配置
            final AutoScaleConfig config = this.autoScaleConfig;

            // 线程安全检查：确保dataSource在整个方法执行期间不为null
            final HikariDataSource currentDataSource = this.dataSource;
            if (currentDataSource == null || currentDataSource.isClosed()) {
                return;
            }

            final HikariPoolMXBean mxBean = currentDataSource.getHikariPoolMXBean();
            if (mxBean == null) {
                return;
            }

            // 一次性获取所有监控数据，避免多次调用时状态不一致
            final int awaiting = mxBean.getThreadsAwaitingConnection();
            final int active = mxBean.getActiveConnections();
            final int total = mxBean.getTotalConnections();
            final int idle = mxBean.getIdleConnections();

            // 边界检查：确保数据有效性
            if (total <= 0 || active < 0 || idle < 0 || awaiting < 0) {
                return;
            }

            final double utilization = (double) active / (double) total;
            final long now = System.currentTimeMillis();

            // 记录tick指标
            final int currentMax = currentDataSource.getMaximumPoolSize();
            final int currentMin = currentDataSource.getMinimumIdle();
            autoScaleMetrics.recordTick(utilization, awaiting, total, currentMax, currentMin);

            // 冷却时间检查
            if (now - lastScaleTs.get() < config.getCooldownMs()) {
                return;
            }

            // 扩容逻辑：当利用率高或有等待线程时立即扩容
            if (utilization >= config.getHighUtilization() || awaiting >= config.getAwaitingThreshold()) {
                final int targetMax = Math.min(currentMax + config.getScaleStep(), config.getMaxPoolSizeBound());
                if (targetMax > currentMax) {
                    currentDataSource.setMaximumPoolSize(targetMax);

                    // 修复：简化MinIdle计算逻辑，确保不超过最大连接数
                    final int currentMinIdle = currentDataSource.getMinimumIdle();
                    final int targetMinIdle = Math.min(currentMinIdle, targetMax);
                    currentDataSource.setMinimumIdle(targetMinIdle);

                    lastScaleTs.set(now);
                    lowStableRounds.set(0);

                    // 记录扩容指标
                    final int addedConnections = targetMax - currentMax;
                    autoScaleMetrics.recordScaleUp(addedConnections);

                    LOG.info("Hikari autoscale up: active={}, total={}, awaiting={}, utilization={}%, newMax={}, newMinIdle={}, added={}",
                            active, total, awaiting, String.format("%.2f", utilization * 100), targetMax, targetMinIdle, addedConnections);
                }
                return;
            }

            // 缩容逻辑：当持续低利用率且无等待线程时缩容
            if (utilization <= config.getLowUtilization() && awaiting == 0) {
                if (lowStableRounds.incrementAndGet() >= config.getLowStableRoundsNeeded()) {
                    final int currentMinIdle = currentDataSource.getMinimumIdle();

                    final int newMinIdle = Math.max(1, currentMinIdle - config.getScaleStep());
                    final int newMax = Math.max(config.getMinPoolSizeBound(), currentMax - config.getScaleStep());

                    boolean changed = false;
                    int removedConnections = 0;

                    // 修复：先设置MinIdle，再设置MaxPoolSize，避免违反Hikari约束
                    if (newMinIdle < currentMinIdle) {
                        currentDataSource.setMinimumIdle(newMinIdle);
                        changed = true;
                    }
                    if (newMax < currentMax) {
                        removedConnections = currentMax - newMax;
                        currentDataSource.setMaximumPoolSize(newMax);
                        changed = true;
                    }

                    if (changed) {
                        try {
                            mxBean.softEvictConnections();
                        } catch (Exception e) {
                            LOG.warn("Unexpected error during connection eviction: {}", e.getMessage());
                        }

                        lastScaleTs.set(now);
                        lowStableRounds.set(0);

                        // 记录缩容指标
                        autoScaleMetrics.recordScaleDown(removedConnections);
                        LOG.info("Hikari autoscale down: active={}, total={}, idle={}, utilization={}%, newMax={}, newMinIdle={}, removed={}",
                                active, total, idle, String.format("%.2f", utilization * 100), newMax, newMinIdle, removedConnections);
                    }
                }
            } else {
                // 重置稳定轮数计数器
                lowStableRounds.set(0);
            }
        } catch (Exception e) {
            autoScaleMetrics.recordTickError();
            LOG.warn("Hikari autoscale tick error: {}", e.getMessage());
        }
    }

    /**
     * 设置自动扩缩容配置
     * 注意：配置变更会在下次tick时生效
     */
    public void setAutoScaleConfig(AutoScaleConfig autoScaleConfig) {
        if (autoScaleConfig == null) {
            throw new IllegalArgumentException("AutoScaleConfig cannot be null");
        }
        autoScaleConfig.validate();
        this.autoScaleConfig = autoScaleConfig;
        LOG.info("AutoScale configuration updated: {}", autoScaleConfig);
    }

    /**
     * 更新自动扩缩容配置并重启AutoScaler（如果需要）
     */
    public void updateAutoScaleConfig(AutoScaleConfig newConfig) {
        final AutoScaleConfig oldConfig = this.autoScaleConfig;
        setAutoScaleConfig(newConfig);

        // 如果周期发生变化或启用状态发生变化，需要重启AutoScaler
        if (oldConfig.getPeriodMs() != newConfig.getPeriodMs() ||
                oldConfig.isAutoScaleEnabled() != newConfig.isAutoScaleEnabled()) {

            stopAutoScaler();
            if (newConfig.isAutoScaleEnabled()) {
                startAutoScalerIfEnabled();
            }
        }
    }

    /**
     * 重置自动扩缩容监控指标
     */
    public void resetAutoScaleMetrics() {
        autoScaleMetrics.reset();
        LOG.info("AutoScale metrics reset");
    }

    /**
     * 获取当前连接池状态信息
     */
    public String getConnectionPoolStatus() {
        if (this.dataSource == null) {
            return "DataSource is null";
        }

        try {
            final HikariPoolMXBean mxBean = this.dataSource.getHikariPoolMXBean();
            if (mxBean == null) {
                return "MXBean is null";
            }

            final int idle = mxBean.getIdleConnections();
            final int total = mxBean.getTotalConnections();
            final int min = this.dataSource.getMinimumIdle();
            final int active = mxBean.getActiveConnections();
            final int max = this.dataSource.getMaximumPoolSize();
            final int awaiting = mxBean.getThreadsAwaitingConnection();
            final double utilization = total > 0 ? (double) active / total : 0.0;

            return String.format("ConnectionPool{active=%d, total=%d, idle=%d, awaiting=%d, max=%d, min=%d, utilization=%.2f%%}",
                    active, total, idle, awaiting, max, min, utilization * 100);
        } catch (Exception e) {
            return "Error getting pool status: " + e.getMessage();
        }
    }
}