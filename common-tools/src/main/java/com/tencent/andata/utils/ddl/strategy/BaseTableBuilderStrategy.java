package com.tencent.andata.utils.ddl.strategy;

import com.tencent.andata.utils.ddl.interfaces.TablePropertyStrategy;
import com.tencent.andata.utils.ddl.interfaces.TableSchemaStrategy;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import org.apache.parquet.Strings;


public abstract class BaseTableBuilderStrategy implements TablePropertyStrategy, TableSchemaStrategy {

    protected HashMap<String, String> tableProperties;

    public String getPropertyString() {
        ArrayList<String> propertiesDeclareList = new ArrayList<>();
        for (Map.Entry<String, String> property : tableProperties.entrySet()) {
            propertiesDeclareList.add(String.format(" '%s' = '%s'", property.getKey(), property.getValue()));
        }
        return Strings.join(propertiesDeclareList, ",\n");
    }

    /**
     * 额外的connector配置信息，需参考官方文档进行设置
     *
     * @param key
     * @param value
     * @return
     */
    @Override
    public TablePropertyStrategy property(String key, String value) {
        this.tableProperties.put(key, value);
        return this;
    }

}
