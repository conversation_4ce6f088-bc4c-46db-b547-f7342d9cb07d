package com.tencent.andata.utils.hiveudf.transformer;

import com.tencent.andata.utils.hiveudf.utils.ArgsChecker;
import java.util.ArrayList;
import java.util.Arrays;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.DoubleObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.LongObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.StringObjectInspector;

/**
 * UDFExtractTBPMVarValueByType
 */
public class UDFExtractTBPMVarValueByType extends GenericUDF {

    final int tbpmVarTypeINDEX = 0;
    final int tbpmDoubleValIndex = 1;
    final int tbpmLongValIndex = 2;
    final int tbpmTextValIndex = 3;
    final int tbpmText2ValIndex = 4;
    final int tbpmJsonValIndex = 5;
    private ArrayList<ObjectInspector> argInspector;

    public static String camelToSnake(String camel, boolean upper) {
        StringBuilder stringBuilder = new StringBuilder();
        for (char c : camel.toCharArray()) {
            char nc = upper ? Character.toUpperCase(c) : Character.toLowerCase(c);
            if (Character.isUpperCase(c)) {
                stringBuilder.append('_').append(nc);
            } else {
                stringBuilder.append(nc);
            }
        }
        return stringBuilder.toString();
    }

    @Override
    public ObjectInspector initialize(ObjectInspector[] arguments) throws UDFArgumentException {
        ArgsChecker
                .newBuilder()
                .setFuncName("udf_extract_tbpm_var_val")
                .addColumnClass(StringObjectInspector.class) // var type
                .addColumnClass(DoubleObjectInspector.class) // `double`
                .addColumnClass(LongObjectInspector.class) // `long`
                .addColumnClass(StringObjectInspector.class) // `text`
                .addColumnClass(StringObjectInspector.class) // `text2`
                .addColumnClass(StringObjectInspector.class) // `json`
                .build()
                .checkArgsNumbersAndType(arguments);
        argInspector = new ArrayList<>(Arrays.asList(arguments));
        return PrimitiveObjectInspectorFactory.javaStringObjectInspector;
    }

    @Override
    public Object evaluate(DeferredObject[] arguments) throws HiveException {
        String varTypeString = ((StringObjectInspector) argInspector.get(tbpmVarTypeINDEX))
                .getPrimitiveJavaObject(arguments[tbpmVarTypeINDEX].get());

        TBPMVarTypeEnum varTypeEnum = TBPMVarTypeEnum.valueOf(camelToSnake(varTypeString, true));
        try {
            switch (varTypeEnum) {
                case SERIALIZABLE:
                case LONG_JSON:
                case LONG_STRING:
                    return ((StringObjectInspector) argInspector.get(tbpmJsonValIndex))
                            .getPrimitiveJavaObject(arguments[tbpmJsonValIndex].get());
                case DOUBLE:
                    return Double.toString(((DoubleObjectInspector) argInspector.get(tbpmDoubleValIndex))
                            .get(arguments[tbpmDoubleValIndex].get()));
                case BOOLEAN:
                case INTEGER:
                case LONG:
                case LOCAL_DATETIME:
                    return Long.toString(((LongObjectInspector) argInspector.get(tbpmLongValIndex))
                            .get(arguments[tbpmLongValIndex].get()));
                case JSON:
                case STRING:
                case NULL:
                    return ((StringObjectInspector) argInspector.get(tbpmTextValIndex))
                            .getPrimitiveJavaObject(arguments[tbpmTextValIndex].get());
                default:
                    return "";
            }
        } catch (NullPointerException ignore) {
            System.out.println("UDFExtractTBPMVarValueByType error");
        }
        return null;
    }

    @Override
    public String getDisplayString(String[] children) {
        return getStandardDisplayString("udf_extract_tbpm_var_val", children);
    }

    enum TBPMVarTypeEnum {
        SERIALIZABLE, LONG_JSON, LONG_STRING, JSON, BOOLEAN, STRING, INTEGER, DOUBLE, LONG, LOCAL_DATETIME, NULL
    }
}
