package com.tencent.andata.utils.udf;

import org.apache.flink.table.functions.ScalarFunction;

import java.sql.Timestamp;

public class SqlTimestampTransform {
    public static class SqlToLong extends ScalarFunction {
        public Long eval(Timestamp timestamp) {
            if (timestamp == null) {
                return null;
            }
            return timestamp.getTime();
        }

        public Long eval(Timestamp timestamp, String tz) {
            if (timestamp == null) {
                return null;
            }
            if ("UTC".equalsIgnoreCase(tz)) {
                return timestamp.getTime() - 28800000;
            } else if ("CST".equalsIgnoreCase(tz)) {
                return timestamp.getTime() + 28800000;
            }
            return eval(timestamp);
        }
    }
}
