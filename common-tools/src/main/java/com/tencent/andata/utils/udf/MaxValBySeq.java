package com.tencent.andata.utils.udf;

import lombok.Getter;
import lombok.Setter;
import org.apache.flink.table.api.dataview.MapView;
import org.apache.flink.table.functions.AggregateFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;
import java.util.Objects;

public class MaxValBySeq extends AggregateFunction<String, MaxValBySeq.MaxValBySeqAccumulator> {

    private static final long serialVersionUID = -5860934997657147836L;

    protected static final Logger LOG = LoggerFactory.getLogger(MaxValBySeq.class);

    // 自定义一个根据seq计算 maxVal 的 accmulator
    public static class MaxValBySeqAccumulator {
        public MaxRecord max;
        public Long mapSize;
        public MapView<MaxRecord, Long> map;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof MaxValBySeqAccumulator)) {
                return false;
            }
            MaxValBySeqAccumulator that = (MaxValBySeqAccumulator) o;
            return Objects.equals(max, that.max)
                    && Objects.equals(mapSize, that.mapSize)
                    && Objects.equals(map, that.map);
        }

        @Override
        public int hashCode() {
            return Objects.hash(max, mapSize, map);
        }
    }


    @Override
    // 创建一个 accumulator
    public MaxValBySeqAccumulator createAccumulator() {
        final MaxValBySeqAccumulator acc = new MaxValBySeqAccumulator();
        acc.max = null;
        acc.mapSize = 0L;
        acc.map = new MapView<>();
        return acc;
    }

    /**
     * accumulate
     * @param acc
     * @param val
     * @param seq
     */
    public void accumulate(MaxValBySeqAccumulator acc, String val, Long seq) {
        if (Objects.nonNull(seq) && seq > 0) {
            try {
                MaxRecord record = new MaxRecord(val, seq);
                if (acc.mapSize == 0L || acc.max.seq.compareTo(seq) < 0) {
                    acc.max = record;
                }

                Long count = acc.map.get(record);
                if (count == null) {
                    count = 0L;
                }
                count += 1L;
                if (count == 0) {
                    // remove it when count is increased from -1 to 0
                    acc.map.remove(record);
                } else {
                    // store it when count is NOT zero
                    acc.map.put(record, count);
                }
                if (count == 1L) {
                    // previous count is zero, this is the first time to see the key
                    acc.mapSize += 1;
                }
            } catch (Exception e) {
                LOG.error("MaxValBySeq accumulate error is ", e.getMessage());
            }
        }
    }

    /**
     * retract
     * @param acc
     * @param val
     * @param seq
     */
    public void retract(MaxValBySeqAccumulator acc, String val, Long seq) {
        if (Objects.nonNull(seq) && seq > 0) {
            try {
                MaxRecord record = new MaxRecord(val, seq);
                Long count = acc.map.get(record);
                if (count == null) {
                    count = 0L;
                }
                count -= 1;
                if (count == 0) {
                    // remove it when count is decreased from 1 to 0
                    acc.map.remove(record);
                    acc.mapSize -= 1L;

                    // if the total count is 0, we could just simply set the f0(max) to the initial
                    // value
                    if (acc.mapSize == 0) {
                        acc.max = null;
                        return;
                    }
                    // if v is the current max value, we have to iterate the map to find the 2nd biggest
                    // value to replace v as the max value
                    if (record.equals(acc.max)) {
                        updateMax(acc);
                    }
                } else {
                    // store it when count is NOT zero
                    acc.map.put(record, count);
                    // we do not take negative number account into mapSize
                }
            } catch (Exception e) {
                LOG.error("MaxValBySeq retract error is ", e.getMessage());
            }
        }
    }

    /**
     * updateMax
     * @param acc
     * @throws Exception
     */
    private void updateMax(MaxValBySeqAccumulator acc) throws Exception {
        boolean hasMax = false;
        for (MaxRecord key : acc.map.keys()) {
            if (!hasMax || acc.max.seq.compareTo(key.seq) < 0) {
                acc.max = key;
                hasMax = true;
            }
        }
        // The behavior of deleting expired data in the state backend is uncertain.
        // so `mapSize` data may exist, while `map` data may have been deleted
        // when both of them are expired.
        if (!hasMax) {
            acc.mapSize = 0L;
            // we should also override max value, because it may have an old value.
            acc.max = null;
        }
    }

    /**
     * Session window 可以使用这个方法将几个单独窗口的结果合并
     * @param acc
     * @param its
     */
    public void merge(MaxValBySeqAccumulator acc, Iterable<MaxValBySeqAccumulator> its) {
        try {
            boolean needUpdateMax = false;
            for (MaxValBySeqAccumulator a : its) {
                // set max element
                if (acc.mapSize == 0
                        || (a.mapSize > 0 && a.max != null && acc.max.seq.compareTo(a.max.seq) < 0)) {
                    acc.max = a.max;
                }
                // merge the count for each key
                for (Map.Entry<MaxRecord, Long> entry : a.map.entries()) {
                    MaxRecord key = entry.getKey();
                    Long otherCount = entry.getValue();
                    Long thisCount = acc.map.get(key);
                    if (thisCount == null) {
                        thisCount = 0L;
                    }
                    long mergedCount = otherCount + thisCount;
                    if (mergedCount == 0) {
                        // remove it when count is increased from -1 to 0
                        acc.map.remove(key);
                        if (thisCount > 0) {
                            // origin is > 0, and retract to 0
                            acc.mapSize -= 1;
                            if (key.equals(acc.max)) {
                                needUpdateMax = true;
                            }
                        }
                    } else if (mergedCount < 0) {
                        acc.map.put(key, mergedCount);
                        if (thisCount > 0) {
                            // origin is > 0, and retract to < 0
                            acc.mapSize -= 1;
                            if (key.equals(acc.max)) {
                                needUpdateMax = true;
                            }
                        }
                    } else { // mergedCount > 0
                        acc.map.put(key, mergedCount);
                        if (thisCount <= 0) {
                            // origin is <= 0, and accumulate to > 0
                            acc.mapSize += 1;
                        }
                    }
                }
            }
            if (needUpdateMax) {
                updateMax(acc);
            }
        } catch (Exception e) {
            LOG.error("MaxValBySeq merge error is ", e.getMessage());
        }
    }

    /**
     * resetAccumulator
     * @param acc
     */
    public void resetAccumulator(MaxValBySeqAccumulator acc) {
        acc.max = null;
        acc.mapSize = 0L;
        acc.map.clear();
    }

    /**
     *  获取返回结果
     * @param acc
     * @return
     */
    @Override
    public String getValue(MaxValBySeqAccumulator acc) {
        if (acc.mapSize > 0) {
            return acc.max.val;
        } else {
            return null;
        }
    }

    // Obj
    @Getter
    @Setter
    public static class MaxRecord {
        public String val = "";
        public Long seq = Long.MIN_VALUE;

        public MaxRecord(String val, Long seq) {
            this.val = val;
            this.seq = seq;
        }

        /**
         * hashCode
         * @return
         */
        @Override
        public int hashCode() {
            return val.hashCode() + seq.hashCode();
        }

        /**
         * equals
         * @param obj
         * @return
         */
        @Override
        public boolean equals(Object obj) {
            MaxRecord selfKey = (MaxRecord) obj;
            boolean val = Objects.equals(this.val, selfKey.getVal());
            boolean seq = Objects.equals(this.seq, selfKey.getSeq());
            return val && seq;
        }
    }
}