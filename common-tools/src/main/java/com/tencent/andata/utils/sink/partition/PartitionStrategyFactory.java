package com.tencent.andata.utils.sink.partition;

import com.google.common.base.Preconditions;
import com.tencent.andata.utils.CommonUtils;
import com.tencent.andata.utils.Constant;
import com.tencent.andata.utils.sink.partition.strategy.BasePartitionStrategy;
import com.tencent.andata.utils.sink.partition.strategy.DateRangeStrategy;
import com.tencent.andata.utils.sink.partition.strategy.HashPartitionStrategy;
import com.tencent.andata.utils.struct.PartitionStrategyEnum;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Locale;
import java.util.stream.Collectors;

public class PartitionStrategyFactory {
    /**
     * 分区策略工厂函数，根据分区字符串进行解析生成具体的策略
     *
     * @param strategyStr 策略字符串，如 hash:32 range:day range:month:2:2022-01-01
     * @return 分区策略
     * @throws Exception
     */
    public static BasePartitionStrategy getPartitionStrategy(String strategyStr) throws ParseException {
        // 获取第一个配置，判断分区类型
        String partitionTypeStr = CommonUtils.listGetOrDefault(
                0,
                null,
                Arrays.stream(strategyStr.split(
                        Constant.PARTITION_STRATEGY_PARSE_DELIMITER
                )).collect(Collectors.toList())
        );
        Preconditions.checkNotNull(partitionTypeStr);
        // 基于分区类型生成具体的策略
        switch (PartitionStrategyEnum.valueOf(partitionTypeStr.toUpperCase(Locale.ROOT))) {
            case HASH:
                return new HashPartitionStrategy(strategyStr);
            case RANGE:
                return new DateRangeStrategy(strategyStr);
            default:
                throw new RuntimeException(
                        String.format("Input strategy %s not support.", partitionTypeStr));
        }
    }
}