package com.tencent.andata.utils.operator.map;

import com.tencent.andata.utils.cdc.conf.fieldgetter.BaseFieldGetter;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.LogicalTypeRoot;

import java.util.regex.Pattern;


public class RowDataCleanMap implements MapFunction<RowData, RowData> {

    private final BaseFieldGetter sourceBaseFieldGetter;

    public RowDataCleanMap(BaseFieldGetter sourceBaseFieldGetter) {
        this.sourceBaseFieldGetter = sourceBaseFieldGetter;
    }

    @Override
    public RowData map(RowData rowData) throws Exception {
        boolean isInstanceOfGenericRowData = rowData instanceof GenericRowData;
        GenericRowData retRow = isInstanceOfGenericRowData
                ? (GenericRowData) rowData :
                new GenericRowData(rowData.getArity());
        for (int i = 0; i < sourceBaseFieldGetter.fieldGetterList.size(); ++i) {
            RowData.FieldGetter fieldGetter = sourceBaseFieldGetter.fieldGetterList.get(i).getField(1);
            Object fieldData;
            LogicalTypeRoot typeRoot = ((LogicalType) sourceBaseFieldGetter
                    .fieldGetterList
                    .get(i)
                    .getField(2))
                    .getTypeRoot();
            if (typeRoot != LogicalTypeRoot.VARCHAR) {
                // 对于非String类数据，直接根据是否复用判断是否需要用Getter捞数据出来
                if (!isInstanceOfGenericRowData) {
                    fieldData = fieldGetter.getFieldOrNull(rowData);
                    retRow.setField(i, fieldData);
                }
            } else {
                // 对于String类型，不管怎样都得读取数据来做处理
                fieldData = fieldGetter.getFieldOrNull(rowData);
                if (fieldData != null) {
                    fieldData = StringData.fromString(
                            Pattern.compile("[\\u0000-\\u001f]|\\p{C}")
                                    .matcher(fieldData.toString())
                                    .replaceAll(""));
                }
                retRow.setField(i, fieldData);
            }
        }
        retRow.setRowKind(rowData.getRowKind());
        return retRow;
    }

}