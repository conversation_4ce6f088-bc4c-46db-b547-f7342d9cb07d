package com.tencent.andata.utils.lookup.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.utils.lookup.interfaces.Cacheable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

@Data
public class RedisCacheImpl<IN, OUT> implements Cacheable<IN, OUT> {

    private static final Logger LOG = LoggerFactory.getLogger(RedisCacheImpl.class);
    private final int port;
    private final String host;
    private final JavaType outType;
    // 默认缓存过期时间（秒）
    private final int expireSeconds;
    private final JedisPool jedisPool;
    private final ObjectMapper objectMapper;

    public RedisCacheImpl(String host, int port, Class<OUT> outClass) {
        this(host, port, 3600, outClass); // 默认1小时过期
    }

    public RedisCacheImpl(String host, int port, int expireSeconds, Class<OUT> outClass) {
        this(host, port, expireSeconds,
                new ObjectMapper().getTypeFactory().constructType(outClass));
    }

    private RedisCacheImpl(String host, int port, int expireSeconds, JavaType outType) {
        this.host = host;
        this.port = port;
        this.expireSeconds = expireSeconds;
        this.objectMapper = new ObjectMapper();
        this.outType = outType;

        LOG.info("----初始化Redis连接池-----");
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(8);
        poolConfig.setMinIdle(0);
        poolConfig.setMaxTotal(10);
        poolConfig.setMaxWaitMillis(1000);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        this.jedisPool = new JedisPool(poolConfig, host, port, 10000);
    }

    public static RedisCacheImpl<HashMap<String, Object>, List<HashMap<String, Object>>> createListCache(
            String host,
            int port,
            int expireSeconds
    ) {
        ObjectMapper mapper = new ObjectMapper();
        JavaType mapType = mapper.getTypeFactory().constructParametricType(
                HashMap.class, String.class, Object.class);

        JavaType listType = mapper.getTypeFactory().constructCollectionType(List.class, mapType);

        return new RedisCacheImpl<>(host, port, expireSeconds, listType);
    }

    @Override
    public OUT getCache(IN in) {
        if (in == null) {
            return null;
        }
        try (Jedis jedis = jedisPool.getResource()) {
            String key = generateKey(in);
            String value = jedis.get(key);
            if (value == null) {
                return null;
            }
            return objectMapper.readValue(value, outType);
        } catch (Exception e) {
            LOG.error("Redis get cache error: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public void setCache(IN in, OUT out) {
        if (in == null || out == null) {
            return;
        }
        try (Jedis jedis = jedisPool.getResource()) {
            String key = generateKey(in);
            String value = objectMapper.writeValueAsString(out);
            jedis.setex(key, expireSeconds, value);
        } catch (JsonProcessingException e) {
            LOG.error("Redis serialization error: {}", e.getMessage());
        } catch (Exception e) {
            LOG.error("Redis set cache error: {}", e.getMessage());
        }
    }

    private String generateKey(IN in) throws JsonProcessingException {
        if (!(in instanceof HashMap)) {
            return String.format("lookup:%s", objectMapper.writeValueAsString(in));
        }

        HashMap<String, Object> params = (HashMap<String, Object>) in;
        StringBuilder keyBuilder = new StringBuilder("lookup:");

        boolean isFirst = true;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (!isFirst) {
                keyBuilder.append("-");
            }
            keyBuilder.append(entry.getKey())
                    .append(":")
                    .append(entry.getValue());
            isFirst = false;
        }

        return keyBuilder.toString();
    }

    public void close() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
        }
    }
}