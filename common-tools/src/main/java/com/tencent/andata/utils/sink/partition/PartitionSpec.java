package com.tencent.andata.utils.sink.partition;

import com.google.common.base.Preconditions;
import com.tencent.andata.utils.sink.partition.strategy.DateRangeStrategy;
import com.tencent.andata.utils.sink.partition.strategy.HashPartitionStrategy;
import org.apache.iceberg.Schema;

import java.util.List;
import java.util.stream.Collectors;

public class PartitionSpec {

    public List<PartitionField> fields;

    /**
     * 构造Iceberg写入分区
     * @param schema
     * @return
     */
    public org.apache.iceberg.PartitionSpec buildIcebergPartition(Schema schema) {
        // 根据schema获取builder
        org.apache.iceberg.PartitionSpec.Builder partitionBuilder = org.apache.iceberg.PartitionSpec.builderFor(schema);
        for (PartitionField pField : fields) {
            if (pField.strategy instanceof HashPartitionStrategy) {
                // iceberg hash分区就是bucket分区
                HashPartitionStrategy strategy = (HashPartitionStrategy) pField.strategy;
                // bucket分区size不能为0
                Preconditions.checkArgument(strategy.size != 0);
                partitionBuilder = partitionBuilder.bucket(pField.field, strategy.size);
            } else if (pField.strategy instanceof DateRangeStrategy) {
                // 日期分区
                DateRangeStrategy strategy = (DateRangeStrategy) pField.strategy;
                switch (strategy.dateType) {
                    case DAY:
                        partitionBuilder = partitionBuilder.day(pField.field);
                        break;
                    case MONTH:
                        partitionBuilder = partitionBuilder.month(pField.field);
                        break;
                    case YEAR:
                        partitionBuilder = partitionBuilder.year(pField.field);
                        break;
                }
            }
        }
        return partitionBuilder.build();
    }

    @Override
    public String toString() {
        String res = fields.stream().map(PartitionField::toString).collect(Collectors.joining(","));
        return String.format(
                "PartitionSpec{%s}",
                res
        );
    }
}
