package com.tencent.andata.utils.factory;

import com.tencent.andata.utils.connector.jdbc.catalog.JdbcCatalogUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.util.HashMap;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.catalog.ObjectPath;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;

@Deprecated
public class JDBCTableDDLFactory extends BaseTableDDLFactory {

    private final String dbTableName;
    private final DatabaseConf databaseConf;

    /**
     * jdbc模版
     * @param dbTableName 表名 databaseEnum db类型 databaseConf db配置
     * @throws Exception 异常
     */

    public JDBCTableDDLFactory(String dbTableName, DatabaseEnum databaseEnum, DatabaseConf databaseConf)
            throws Exception {
        if (databaseEnum != DatabaseEnum.PGSQL) {
            throw new Exception("Only supper PGSQL JDBC");
        }
        this.databaseConf = databaseConf;
        this.dbTableName = dbTableName;
        // 设置Property
        this.tableProperties = new HashMap<String, String>() {{
            put("connector", "jdbc");
            put("url", String.format(
                    "*****************************************************&?"
                            + "currentSchema=public&serverTimezone=Asia/Shanghai",
                    databaseConf.dbHost,
                    databaseConf.dbPort,
                    databaseConf.dbName
            ));
            put("username", databaseConf.userName);
            put("password", databaseConf.password);
            put("table-name", dbTableName);
        }};
    }

    /**
     * 返回DDL
     *
     * @return
     * @throws TableNotExistException
     */
    public String getTableSchemaDDL() throws TableNotExistException {
        return getTableSchema().toString();
    }

    /**
     * 返回Schema
     *
     * @return 返回schema
     */
    public Schema getTableSchema() throws TableNotExistException {
        return JdbcCatalogUtils.createCatalog(
                        "cdc_catalog",
                        databaseConf.dbName,
                        databaseConf.userName,
                        databaseConf.password,
                        String.format("jdbc:postgresql://%s:%d/", databaseConf.dbHost, databaseConf.dbPort),
                        DatabaseEnum.PGSQL
                )
                .getTable(new ObjectPath(databaseConf.dbName, dbTableName))
                .getUnresolvedSchema();
    }
}
