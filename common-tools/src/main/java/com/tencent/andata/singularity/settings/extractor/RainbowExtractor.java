package com.tencent.andata.singularity.settings.extractor;

import com.tencent.andata.utils.RainbowUtils;
import java.util.HashMap;
import java.util.Map;

public class RainbowExtractor implements BaseExtractor {
    RainbowUtils rainbowUtils;

    public RainbowExtractor(RainbowUtils rainbowUtils) {
        this.rainbowUtils = rainbowUtils;
    }

    @Override
    public String extractString(String path, String key) {
        return this.extractAllRawValue(path).get(key);
    }

    @Override
    public Map<String, String> extractAllRawValue(String path) {
        Map<String, String> retMap = new HashMap<>();

        this.rainbowUtils.getKVGroupMap(path).forEach(
                (key, value) -> retMap.put(key, value.getValue())
        );

        return retMap;
    }
}