package com.tencent.andata.singularity.settings.extractor;

import java.util.Map;

public class DefaultExtractor implements BaseExtractor {
    @Override
    public String extractString(String path, String key) {
        throw new RuntimeException("No extractor is bind to Settings, extract failed");
    }

    @Override
    public Map<String, String> extractAllRawValue(String path) {
        throw new RuntimeException("No extractor is bind to Settings, extract failed");
    }
}