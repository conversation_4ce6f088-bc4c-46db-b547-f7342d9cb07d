package com.tencent.andata.singularity.settings;

import com.tencent.andata.singularity.settings.extractor.BaseExtractor;
import com.tencent.andata.singularity.settings.extractor.DefaultExtractor;
import java.util.Map;

public class Settings {
    // Facade + IoC

    // extractor
    private static BaseExtractor extractor = new DefaultExtractor();

    public static void setExtractor(BaseExtractor extractor) {
        // EnvExtractor/RainbowExtractor/CombineExtractor/ReportExtractor
        Settings.extractor = extractor;
    }

    public static String extract(String path, String key) {
        // 只是做KV容器来拉取配置的话，直接捞出来对应的数据就行
        // 但这块就不支持Rainbow所支持的那种File什么的特性，可以看成是一个大的Properties
        return extractor.extractString(path, key);
    }

    public static Map<String, String> extractAll(String path) {
        return extractor.extractAllRawValue(path);
    }

    private Settings() {
    }
}