/**
 * Autogenerated by Avro
 * 
 * DO NOT EDIT DIRECTLY
 */
package com.tencent.andata.struct.avro.dts;  
@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class IncidentOperation extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"IncidentOperation\",\"namespace\":\"com.tencent.andata.struct.avro.dts\",\"fields\":[{\"name\":\"id\",\"type\":\"long\"},{\"name\":\"tenant_id\",\"type\":\"long\"},{\"name\":\"operation_id\",\"type\":\"string\"},{\"name\":\"incident_id\",\"type\":\"string\"},{\"name\":\"operation_type\",\"type\":\"int\"},{\"name\":\"operator_type\",\"type\":\"int\"},{\"name\":\"operator\",\"type\":\"string\"},{\"name\":\"operate_time\",\"type\":\"long\"},{\"name\":\"before_status\",\"type\":\"int\"},{\"name\":\"after_status\",\"type\":\"int\"},{\"name\":\"before_current_operator\",\"type\":\"string\"},{\"name\":\"after_current_operator\",\"type\":\"string\"},{\"name\":\"req_labels\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"params\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"before_incident\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"after_incident\",\"type\":[\"null\",\"string\"],\"default\":null}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }
  @Deprecated public long id;
  @Deprecated public long tenant_id;
  @Deprecated public java.lang.CharSequence operation_id;
  @Deprecated public java.lang.CharSequence incident_id;
  @Deprecated public int operation_type;
  @Deprecated public int operator_type;
  @Deprecated public java.lang.CharSequence operator;
  @Deprecated public long operate_time;
  @Deprecated public int before_status;
  @Deprecated public int after_status;
  @Deprecated public java.lang.CharSequence before_current_operator;
  @Deprecated public java.lang.CharSequence after_current_operator;
  @Deprecated public java.lang.CharSequence req_labels;
  @Deprecated public java.lang.CharSequence params;
  @Deprecated public java.lang.CharSequence before_incident;
  @Deprecated public java.lang.CharSequence after_incident;

  /**
   * Default constructor.
   */
  public IncidentOperation() {}

  /**
   * All-args constructor.
   */
  public IncidentOperation(java.lang.Long id, java.lang.Long tenant_id, java.lang.CharSequence operation_id, java.lang.CharSequence incident_id, java.lang.Integer operation_type, java.lang.Integer operator_type, java.lang.CharSequence operator, java.lang.Long operate_time, java.lang.Integer before_status, java.lang.Integer after_status, java.lang.CharSequence before_current_operator, java.lang.CharSequence after_current_operator, java.lang.CharSequence req_labels, java.lang.CharSequence params, java.lang.CharSequence before_incident, java.lang.CharSequence after_incident) {
    this.id = id;
    this.tenant_id = tenant_id;
    this.operation_id = operation_id;
    this.incident_id = incident_id;
    this.operation_type = operation_type;
    this.operator_type = operator_type;
    this.operator = operator;
    this.operate_time = operate_time;
    this.before_status = before_status;
    this.after_status = after_status;
    this.before_current_operator = before_current_operator;
    this.after_current_operator = after_current_operator;
    this.req_labels = req_labels;
    this.params = params;
    this.before_incident = before_incident;
    this.after_incident = after_incident;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call. 
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return id;
    case 1: return tenant_id;
    case 2: return operation_id;
    case 3: return incident_id;
    case 4: return operation_type;
    case 5: return operator_type;
    case 6: return operator;
    case 7: return operate_time;
    case 8: return before_status;
    case 9: return after_status;
    case 10: return before_current_operator;
    case 11: return after_current_operator;
    case 12: return req_labels;
    case 13: return params;
    case 14: return before_incident;
    case 15: return after_incident;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }
  // Used by DatumReader.  Applications should not call. 
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: id = (java.lang.Long)value$; break;
    case 1: tenant_id = (java.lang.Long)value$; break;
    case 2: operation_id = (java.lang.CharSequence)value$; break;
    case 3: incident_id = (java.lang.CharSequence)value$; break;
    case 4: operation_type = (java.lang.Integer)value$; break;
    case 5: operator_type = (java.lang.Integer)value$; break;
    case 6: operator = (java.lang.CharSequence)value$; break;
    case 7: operate_time = (java.lang.Long)value$; break;
    case 8: before_status = (java.lang.Integer)value$; break;
    case 9: after_status = (java.lang.Integer)value$; break;
    case 10: before_current_operator = (java.lang.CharSequence)value$; break;
    case 11: after_current_operator = (java.lang.CharSequence)value$; break;
    case 12: req_labels = (java.lang.CharSequence)value$; break;
    case 13: params = (java.lang.CharSequence)value$; break;
    case 14: before_incident = (java.lang.CharSequence)value$; break;
    case 15: after_incident = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'id' field.
   */
  public java.lang.Long getId() {
    return id;
  }

  /**
   * Sets the value of the 'id' field.
   * @param value the value to set.
   */
  public void setId(java.lang.Long value) {
    this.id = value;
  }

  /**
   * Gets the value of the 'tenant_id' field.
   */
  public java.lang.Long getTenantId() {
    return tenant_id;
  }

  /**
   * Sets the value of the 'tenant_id' field.
   * @param value the value to set.
   */
  public void setTenantId(java.lang.Long value) {
    this.tenant_id = value;
  }

  /**
   * Gets the value of the 'operation_id' field.
   */
  public java.lang.CharSequence getOperationId() {
    return operation_id;
  }

  /**
   * Sets the value of the 'operation_id' field.
   * @param value the value to set.
   */
  public void setOperationId(java.lang.CharSequence value) {
    this.operation_id = value;
  }

  /**
   * Gets the value of the 'incident_id' field.
   */
  public java.lang.CharSequence getIncidentId() {
    return incident_id;
  }

  /**
   * Sets the value of the 'incident_id' field.
   * @param value the value to set.
   */
  public void setIncidentId(java.lang.CharSequence value) {
    this.incident_id = value;
  }

  /**
   * Gets the value of the 'operation_type' field.
   */
  public java.lang.Integer getOperationType() {
    return operation_type;
  }

  /**
   * Sets the value of the 'operation_type' field.
   * @param value the value to set.
   */
  public void setOperationType(java.lang.Integer value) {
    this.operation_type = value;
  }

  /**
   * Gets the value of the 'operator_type' field.
   */
  public java.lang.Integer getOperatorType() {
    return operator_type;
  }

  /**
   * Sets the value of the 'operator_type' field.
   * @param value the value to set.
   */
  public void setOperatorType(java.lang.Integer value) {
    this.operator_type = value;
  }

  /**
   * Gets the value of the 'operator' field.
   */
  public java.lang.CharSequence getOperator() {
    return operator;
  }

  /**
   * Sets the value of the 'operator' field.
   * @param value the value to set.
   */
  public void setOperator(java.lang.CharSequence value) {
    this.operator = value;
  }

  /**
   * Gets the value of the 'operate_time' field.
   */
  public java.lang.Long getOperateTime() {
    return operate_time;
  }

  /**
   * Sets the value of the 'operate_time' field.
   * @param value the value to set.
   */
  public void setOperateTime(java.lang.Long value) {
    this.operate_time = value;
  }

  /**
   * Gets the value of the 'before_status' field.
   */
  public java.lang.Integer getBeforeStatus() {
    return before_status;
  }

  /**
   * Sets the value of the 'before_status' field.
   * @param value the value to set.
   */
  public void setBeforeStatus(java.lang.Integer value) {
    this.before_status = value;
  }

  /**
   * Gets the value of the 'after_status' field.
   */
  public java.lang.Integer getAfterStatus() {
    return after_status;
  }

  /**
   * Sets the value of the 'after_status' field.
   * @param value the value to set.
   */
  public void setAfterStatus(java.lang.Integer value) {
    this.after_status = value;
  }

  /**
   * Gets the value of the 'before_current_operator' field.
   */
  public java.lang.CharSequence getBeforeCurrentOperator() {
    return before_current_operator;
  }

  /**
   * Sets the value of the 'before_current_operator' field.
   * @param value the value to set.
   */
  public void setBeforeCurrentOperator(java.lang.CharSequence value) {
    this.before_current_operator = value;
  }

  /**
   * Gets the value of the 'after_current_operator' field.
   */
  public java.lang.CharSequence getAfterCurrentOperator() {
    return after_current_operator;
  }

  /**
   * Sets the value of the 'after_current_operator' field.
   * @param value the value to set.
   */
  public void setAfterCurrentOperator(java.lang.CharSequence value) {
    this.after_current_operator = value;
  }

  /**
   * Gets the value of the 'req_labels' field.
   */
  public java.lang.CharSequence getReqLabels() {
    return req_labels;
  }

  /**
   * Sets the value of the 'req_labels' field.
   * @param value the value to set.
   */
  public void setReqLabels(java.lang.CharSequence value) {
    this.req_labels = value;
  }

  /**
   * Gets the value of the 'params' field.
   */
  public java.lang.CharSequence getParams() {
    return params;
  }

  /**
   * Sets the value of the 'params' field.
   * @param value the value to set.
   */
  public void setParams(java.lang.CharSequence value) {
    this.params = value;
  }

  /**
   * Gets the value of the 'before_incident' field.
   */
  public java.lang.CharSequence getBeforeIncident() {
    return before_incident;
  }

  /**
   * Sets the value of the 'before_incident' field.
   * @param value the value to set.
   */
  public void setBeforeIncident(java.lang.CharSequence value) {
    this.before_incident = value;
  }

  /**
   * Gets the value of the 'after_incident' field.
   */
  public java.lang.CharSequence getAfterIncident() {
    return after_incident;
  }

  /**
   * Sets the value of the 'after_incident' field.
   * @param value the value to set.
   */
  public void setAfterIncident(java.lang.CharSequence value) {
    this.after_incident = value;
  }

  /** Creates a new IncidentOperation RecordBuilder */
  public static com.tencent.andata.struct.avro.dts.IncidentOperation.Builder newBuilder() {
    return new com.tencent.andata.struct.avro.dts.IncidentOperation.Builder();
  }
  
  /** Creates a new IncidentOperation RecordBuilder by copying an existing Builder */
  public static com.tencent.andata.struct.avro.dts.IncidentOperation.Builder newBuilder(com.tencent.andata.struct.avro.dts.IncidentOperation.Builder other) {
    return new com.tencent.andata.struct.avro.dts.IncidentOperation.Builder(other);
  }
  
  /** Creates a new IncidentOperation RecordBuilder by copying an existing IncidentOperation instance */
  public static com.tencent.andata.struct.avro.dts.IncidentOperation.Builder newBuilder(com.tencent.andata.struct.avro.dts.IncidentOperation other) {
    return new com.tencent.andata.struct.avro.dts.IncidentOperation.Builder(other);
  }
  
  /**
   * RecordBuilder for IncidentOperation instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<IncidentOperation>
    implements org.apache.avro.data.RecordBuilder<IncidentOperation> {

    private long id;
    private long tenant_id;
    private java.lang.CharSequence operation_id;
    private java.lang.CharSequence incident_id;
    private int operation_type;
    private int operator_type;
    private java.lang.CharSequence operator;
    private long operate_time;
    private int before_status;
    private int after_status;
    private java.lang.CharSequence before_current_operator;
    private java.lang.CharSequence after_current_operator;
    private java.lang.CharSequence req_labels;
    private java.lang.CharSequence params;
    private java.lang.CharSequence before_incident;
    private java.lang.CharSequence after_incident;

    /** Creates a new Builder */
    private Builder() {
      super(com.tencent.andata.struct.avro.dts.IncidentOperation.SCHEMA$);
    }
    
    /** Creates a Builder by copying an existing Builder */
    private Builder(com.tencent.andata.struct.avro.dts.IncidentOperation.Builder other) {
      super(other);
    }
    
    /** Creates a Builder by copying an existing IncidentOperation instance */
    private Builder(com.tencent.andata.struct.avro.dts.IncidentOperation other) {
            super(com.tencent.andata.struct.avro.dts.IncidentOperation.SCHEMA$);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.tenant_id)) {
        this.tenant_id = data().deepCopy(fields()[1].schema(), other.tenant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.operation_id)) {
        this.operation_id = data().deepCopy(fields()[2].schema(), other.operation_id);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.incident_id)) {
        this.incident_id = data().deepCopy(fields()[3].schema(), other.incident_id);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.operation_type)) {
        this.operation_type = data().deepCopy(fields()[4].schema(), other.operation_type);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.operator_type)) {
        this.operator_type = data().deepCopy(fields()[5].schema(), other.operator_type);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.operator)) {
        this.operator = data().deepCopy(fields()[6].schema(), other.operator);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.operate_time)) {
        this.operate_time = data().deepCopy(fields()[7].schema(), other.operate_time);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.before_status)) {
        this.before_status = data().deepCopy(fields()[8].schema(), other.before_status);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.after_status)) {
        this.after_status = data().deepCopy(fields()[9].schema(), other.after_status);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.before_current_operator)) {
        this.before_current_operator = data().deepCopy(fields()[10].schema(), other.before_current_operator);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.after_current_operator)) {
        this.after_current_operator = data().deepCopy(fields()[11].schema(), other.after_current_operator);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.req_labels)) {
        this.req_labels = data().deepCopy(fields()[12].schema(), other.req_labels);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.params)) {
        this.params = data().deepCopy(fields()[13].schema(), other.params);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.before_incident)) {
        this.before_incident = data().deepCopy(fields()[14].schema(), other.before_incident);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.after_incident)) {
        this.after_incident = data().deepCopy(fields()[15].schema(), other.after_incident);
        fieldSetFlags()[15] = true;
      }
    }

    /** Gets the value of the 'id' field */
    public java.lang.Long getId() {
      return id;
    }
    
    /** Sets the value of the 'id' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setId(long value) {
      validate(fields()[0], value);
      this.id = value;
      fieldSetFlags()[0] = true;
      return this; 
    }
    
    /** Checks whether the 'id' field has been set */
    public boolean hasId() {
      return fieldSetFlags()[0];
    }
    
    /** Clears the value of the 'id' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearId() {
      fieldSetFlags()[0] = false;
      return this;
    }

    /** Gets the value of the 'tenant_id' field */
    public java.lang.Long getTenantId() {
      return tenant_id;
    }
    
    /** Sets the value of the 'tenant_id' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setTenantId(long value) {
      validate(fields()[1], value);
      this.tenant_id = value;
      fieldSetFlags()[1] = true;
      return this; 
    }
    
    /** Checks whether the 'tenant_id' field has been set */
    public boolean hasTenantId() {
      return fieldSetFlags()[1];
    }
    
    /** Clears the value of the 'tenant_id' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearTenantId() {
      fieldSetFlags()[1] = false;
      return this;
    }

    /** Gets the value of the 'operation_id' field */
    public java.lang.CharSequence getOperationId() {
      return operation_id;
    }
    
    /** Sets the value of the 'operation_id' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setOperationId(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.operation_id = value;
      fieldSetFlags()[2] = true;
      return this; 
    }
    
    /** Checks whether the 'operation_id' field has been set */
    public boolean hasOperationId() {
      return fieldSetFlags()[2];
    }
    
    /** Clears the value of the 'operation_id' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearOperationId() {
      operation_id = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /** Gets the value of the 'incident_id' field */
    public java.lang.CharSequence getIncidentId() {
      return incident_id;
    }
    
    /** Sets the value of the 'incident_id' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setIncidentId(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.incident_id = value;
      fieldSetFlags()[3] = true;
      return this; 
    }
    
    /** Checks whether the 'incident_id' field has been set */
    public boolean hasIncidentId() {
      return fieldSetFlags()[3];
    }
    
    /** Clears the value of the 'incident_id' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearIncidentId() {
      incident_id = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /** Gets the value of the 'operation_type' field */
    public java.lang.Integer getOperationType() {
      return operation_type;
    }
    
    /** Sets the value of the 'operation_type' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setOperationType(int value) {
      validate(fields()[4], value);
      this.operation_type = value;
      fieldSetFlags()[4] = true;
      return this; 
    }
    
    /** Checks whether the 'operation_type' field has been set */
    public boolean hasOperationType() {
      return fieldSetFlags()[4];
    }
    
    /** Clears the value of the 'operation_type' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearOperationType() {
      fieldSetFlags()[4] = false;
      return this;
    }

    /** Gets the value of the 'operator_type' field */
    public java.lang.Integer getOperatorType() {
      return operator_type;
    }
    
    /** Sets the value of the 'operator_type' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setOperatorType(int value) {
      validate(fields()[5], value);
      this.operator_type = value;
      fieldSetFlags()[5] = true;
      return this; 
    }
    
    /** Checks whether the 'operator_type' field has been set */
    public boolean hasOperatorType() {
      return fieldSetFlags()[5];
    }
    
    /** Clears the value of the 'operator_type' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearOperatorType() {
      fieldSetFlags()[5] = false;
      return this;
    }

    /** Gets the value of the 'operator' field */
    public java.lang.CharSequence getOperator() {
      return operator;
    }
    
    /** Sets the value of the 'operator' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setOperator(java.lang.CharSequence value) {
      validate(fields()[6], value);
      this.operator = value;
      fieldSetFlags()[6] = true;
      return this; 
    }
    
    /** Checks whether the 'operator' field has been set */
    public boolean hasOperator() {
      return fieldSetFlags()[6];
    }
    
    /** Clears the value of the 'operator' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearOperator() {
      operator = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /** Gets the value of the 'operate_time' field */
    public java.lang.Long getOperateTime() {
      return operate_time;
    }
    
    /** Sets the value of the 'operate_time' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setOperateTime(long value) {
      validate(fields()[7], value);
      this.operate_time = value;
      fieldSetFlags()[7] = true;
      return this; 
    }
    
    /** Checks whether the 'operate_time' field has been set */
    public boolean hasOperateTime() {
      return fieldSetFlags()[7];
    }
    
    /** Clears the value of the 'operate_time' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearOperateTime() {
      fieldSetFlags()[7] = false;
      return this;
    }

    /** Gets the value of the 'before_status' field */
    public java.lang.Integer getBeforeStatus() {
      return before_status;
    }
    
    /** Sets the value of the 'before_status' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setBeforeStatus(int value) {
      validate(fields()[8], value);
      this.before_status = value;
      fieldSetFlags()[8] = true;
      return this; 
    }
    
    /** Checks whether the 'before_status' field has been set */
    public boolean hasBeforeStatus() {
      return fieldSetFlags()[8];
    }
    
    /** Clears the value of the 'before_status' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearBeforeStatus() {
      fieldSetFlags()[8] = false;
      return this;
    }

    /** Gets the value of the 'after_status' field */
    public java.lang.Integer getAfterStatus() {
      return after_status;
    }
    
    /** Sets the value of the 'after_status' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setAfterStatus(int value) {
      validate(fields()[9], value);
      this.after_status = value;
      fieldSetFlags()[9] = true;
      return this; 
    }
    
    /** Checks whether the 'after_status' field has been set */
    public boolean hasAfterStatus() {
      return fieldSetFlags()[9];
    }
    
    /** Clears the value of the 'after_status' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearAfterStatus() {
      fieldSetFlags()[9] = false;
      return this;
    }

    /** Gets the value of the 'before_current_operator' field */
    public java.lang.CharSequence getBeforeCurrentOperator() {
      return before_current_operator;
    }
    
    /** Sets the value of the 'before_current_operator' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setBeforeCurrentOperator(java.lang.CharSequence value) {
      validate(fields()[10], value);
      this.before_current_operator = value;
      fieldSetFlags()[10] = true;
      return this; 
    }
    
    /** Checks whether the 'before_current_operator' field has been set */
    public boolean hasBeforeCurrentOperator() {
      return fieldSetFlags()[10];
    }
    
    /** Clears the value of the 'before_current_operator' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearBeforeCurrentOperator() {
      before_current_operator = null;
      fieldSetFlags()[10] = false;
      return this;
    }

    /** Gets the value of the 'after_current_operator' field */
    public java.lang.CharSequence getAfterCurrentOperator() {
      return after_current_operator;
    }
    
    /** Sets the value of the 'after_current_operator' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setAfterCurrentOperator(java.lang.CharSequence value) {
      validate(fields()[11], value);
      this.after_current_operator = value;
      fieldSetFlags()[11] = true;
      return this; 
    }
    
    /** Checks whether the 'after_current_operator' field has been set */
    public boolean hasAfterCurrentOperator() {
      return fieldSetFlags()[11];
    }
    
    /** Clears the value of the 'after_current_operator' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearAfterCurrentOperator() {
      after_current_operator = null;
      fieldSetFlags()[11] = false;
      return this;
    }

    /** Gets the value of the 'req_labels' field */
    public java.lang.CharSequence getReqLabels() {
      return req_labels;
    }
    
    /** Sets the value of the 'req_labels' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setReqLabels(java.lang.CharSequence value) {
      validate(fields()[12], value);
      this.req_labels = value;
      fieldSetFlags()[12] = true;
      return this; 
    }
    
    /** Checks whether the 'req_labels' field has been set */
    public boolean hasReqLabels() {
      return fieldSetFlags()[12];
    }
    
    /** Clears the value of the 'req_labels' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearReqLabels() {
      req_labels = null;
      fieldSetFlags()[12] = false;
      return this;
    }

    /** Gets the value of the 'params' field */
    public java.lang.CharSequence getParams() {
      return params;
    }
    
    /** Sets the value of the 'params' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setParams(java.lang.CharSequence value) {
      validate(fields()[13], value);
      this.params = value;
      fieldSetFlags()[13] = true;
      return this; 
    }
    
    /** Checks whether the 'params' field has been set */
    public boolean hasParams() {
      return fieldSetFlags()[13];
    }
    
    /** Clears the value of the 'params' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearParams() {
      params = null;
      fieldSetFlags()[13] = false;
      return this;
    }

    /** Gets the value of the 'before_incident' field */
    public java.lang.CharSequence getBeforeIncident() {
      return before_incident;
    }
    
    /** Sets the value of the 'before_incident' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setBeforeIncident(java.lang.CharSequence value) {
      validate(fields()[14], value);
      this.before_incident = value;
      fieldSetFlags()[14] = true;
      return this; 
    }
    
    /** Checks whether the 'before_incident' field has been set */
    public boolean hasBeforeIncident() {
      return fieldSetFlags()[14];
    }
    
    /** Clears the value of the 'before_incident' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearBeforeIncident() {
      before_incident = null;
      fieldSetFlags()[14] = false;
      return this;
    }

    /** Gets the value of the 'after_incident' field */
    public java.lang.CharSequence getAfterIncident() {
      return after_incident;
    }
    
    /** Sets the value of the 'after_incident' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder setAfterIncident(java.lang.CharSequence value) {
      validate(fields()[15], value);
      this.after_incident = value;
      fieldSetFlags()[15] = true;
      return this; 
    }
    
    /** Checks whether the 'after_incident' field has been set */
    public boolean hasAfterIncident() {
      return fieldSetFlags()[15];
    }
    
    /** Clears the value of the 'after_incident' field */
    public com.tencent.andata.struct.avro.dts.IncidentOperation.Builder clearAfterIncident() {
      after_incident = null;
      fieldSetFlags()[15] = false;
      return this;
    }

    @Override
    public IncidentOperation build() {
      try {
        IncidentOperation record = new IncidentOperation();
        record.id = fieldSetFlags()[0] ? this.id : (java.lang.Long) defaultValue(fields()[0]);
        record.tenant_id = fieldSetFlags()[1] ? this.tenant_id : (java.lang.Long) defaultValue(fields()[1]);
        record.operation_id = fieldSetFlags()[2] ? this.operation_id : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.incident_id = fieldSetFlags()[3] ? this.incident_id : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.operation_type = fieldSetFlags()[4] ? this.operation_type : (java.lang.Integer) defaultValue(fields()[4]);
        record.operator_type = fieldSetFlags()[5] ? this.operator_type : (java.lang.Integer) defaultValue(fields()[5]);
        record.operator = fieldSetFlags()[6] ? this.operator : (java.lang.CharSequence) defaultValue(fields()[6]);
        record.operate_time = fieldSetFlags()[7] ? this.operate_time : (java.lang.Long) defaultValue(fields()[7]);
        record.before_status = fieldSetFlags()[8] ? this.before_status : (java.lang.Integer) defaultValue(fields()[8]);
        record.after_status = fieldSetFlags()[9] ? this.after_status : (java.lang.Integer) defaultValue(fields()[9]);
        record.before_current_operator = fieldSetFlags()[10] ? this.before_current_operator : (java.lang.CharSequence) defaultValue(fields()[10]);
        record.after_current_operator = fieldSetFlags()[11] ? this.after_current_operator : (java.lang.CharSequence) defaultValue(fields()[11]);
        record.req_labels = fieldSetFlags()[12] ? this.req_labels : (java.lang.CharSequence) defaultValue(fields()[12]);
        record.params = fieldSetFlags()[13] ? this.params : (java.lang.CharSequence) defaultValue(fields()[13]);
        record.before_incident = fieldSetFlags()[14] ? this.before_incident : (java.lang.CharSequence) defaultValue(fields()[14]);
        record.after_incident = fieldSetFlags()[15] ? this.after_incident : (java.lang.CharSequence) defaultValue(fields()[15]);
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }
}
