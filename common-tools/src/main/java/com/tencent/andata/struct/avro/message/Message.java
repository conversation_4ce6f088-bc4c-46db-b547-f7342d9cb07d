/**
 * Autogenerated by Avro
 * 
 * DO NOT EDIT DIRECTLY
 */
package com.tencent.andata.struct.avro.message;  
@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class Message extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"Message\",\"namespace\":\"com.tencent.andata.struct.avro.message\",\"fields\":[{\"name\":\"schemaName\",\"type\":\"string\"},{\"name\":\"procTime\",\"type\":\"long\"},{\"name\":\"data\",\"type\":\"bytes\"},{\"name\":\"msgType\",\"type\":{\"type\":\"enum\",\"name\":\"MessageType\",\"symbols\":[\"ROW_DATA\",\"RAW_BYTES\"],\"default\":\"ROW_DATA\"}}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }
  @Deprecated public java.lang.CharSequence schemaName;
  @Deprecated public long procTime;
  @Deprecated public java.nio.ByteBuffer data;
  @Deprecated public com.tencent.andata.struct.avro.message.MessageType msgType;

  /**
   * Default constructor.
   */
  public Message() {}

  /**
   * All-args constructor.
   */
  public Message(java.lang.CharSequence schemaName, java.lang.Long procTime, java.nio.ByteBuffer data, com.tencent.andata.struct.avro.message.MessageType msgType) {
    this.schemaName = schemaName;
    this.procTime = procTime;
    this.data = data;
    this.msgType = msgType;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call. 
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return schemaName;
    case 1: return procTime;
    case 2: return data;
    case 3: return msgType;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }
  // Used by DatumReader.  Applications should not call. 
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: schemaName = (java.lang.CharSequence)value$; break;
    case 1: procTime = (java.lang.Long)value$; break;
    case 2: data = (java.nio.ByteBuffer)value$; break;
    case 3: msgType = (com.tencent.andata.struct.avro.message.MessageType)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'schemaName' field.
   */
  public java.lang.CharSequence getSchemaName() {
    return schemaName;
  }

  /**
   * Sets the value of the 'schemaName' field.
   * @param value the value to set.
   */
  public void setSchemaName(java.lang.CharSequence value) {
    this.schemaName = value;
  }

  /**
   * Gets the value of the 'procTime' field.
   */
  public java.lang.Long getProcTime() {
    return procTime;
  }

  /**
   * Sets the value of the 'procTime' field.
   * @param value the value to set.
   */
  public void setProcTime(java.lang.Long value) {
    this.procTime = value;
  }

  /**
   * Gets the value of the 'data' field.
   */
  public java.nio.ByteBuffer getData() {
    return data;
  }

  /**
   * Sets the value of the 'data' field.
   * @param value the value to set.
   */
  public void setData(java.nio.ByteBuffer value) {
    this.data = value;
  }

  /**
   * Gets the value of the 'msgType' field.
   */
  public com.tencent.andata.struct.avro.message.MessageType getMsgType() {
    return msgType;
  }

  /**
   * Sets the value of the 'msgType' field.
   * @param value the value to set.
   */
  public void setMsgType(com.tencent.andata.struct.avro.message.MessageType value) {
    this.msgType = value;
  }

  /** Creates a new Message RecordBuilder */
  public static com.tencent.andata.struct.avro.message.Message.Builder newBuilder() {
    return new com.tencent.andata.struct.avro.message.Message.Builder();
  }
  
  /** Creates a new Message RecordBuilder by copying an existing Builder */
  public static com.tencent.andata.struct.avro.message.Message.Builder newBuilder(com.tencent.andata.struct.avro.message.Message.Builder other) {
    return new com.tencent.andata.struct.avro.message.Message.Builder(other);
  }
  
  /** Creates a new Message RecordBuilder by copying an existing Message instance */
  public static com.tencent.andata.struct.avro.message.Message.Builder newBuilder(com.tencent.andata.struct.avro.message.Message other) {
    return new com.tencent.andata.struct.avro.message.Message.Builder(other);
  }
  
  /**
   * RecordBuilder for Message instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<Message>
    implements org.apache.avro.data.RecordBuilder<Message> {

    private java.lang.CharSequence schemaName;
    private long procTime;
    private java.nio.ByteBuffer data;
    private com.tencent.andata.struct.avro.message.MessageType msgType;

    /** Creates a new Builder */
    private Builder() {
      super(com.tencent.andata.struct.avro.message.Message.SCHEMA$);
    }
    
    /** Creates a Builder by copying an existing Builder */
    private Builder(com.tencent.andata.struct.avro.message.Message.Builder other) {
      super(other);
    }
    
    /** Creates a Builder by copying an existing Message instance */
    private Builder(com.tencent.andata.struct.avro.message.Message other) {
            super(com.tencent.andata.struct.avro.message.Message.SCHEMA$);
      if (isValidValue(fields()[0], other.schemaName)) {
        this.schemaName = data().deepCopy(fields()[0].schema(), other.schemaName);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.procTime)) {
        this.procTime = data().deepCopy(fields()[1].schema(), other.procTime);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.data)) {
        this.data = data().deepCopy(fields()[2].schema(), other.data);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.msgType)) {
        this.msgType = data().deepCopy(fields()[3].schema(), other.msgType);
        fieldSetFlags()[3] = true;
      }
    }

    /** Gets the value of the 'schemaName' field */
    public java.lang.CharSequence getSchemaName() {
      return schemaName;
    }
    
    /** Sets the value of the 'schemaName' field */
    public com.tencent.andata.struct.avro.message.Message.Builder setSchemaName(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.schemaName = value;
      fieldSetFlags()[0] = true;
      return this; 
    }
    
    /** Checks whether the 'schemaName' field has been set */
    public boolean hasSchemaName() {
      return fieldSetFlags()[0];
    }
    
    /** Clears the value of the 'schemaName' field */
    public com.tencent.andata.struct.avro.message.Message.Builder clearSchemaName() {
      schemaName = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /** Gets the value of the 'procTime' field */
    public java.lang.Long getProcTime() {
      return procTime;
    }
    
    /** Sets the value of the 'procTime' field */
    public com.tencent.andata.struct.avro.message.Message.Builder setProcTime(long value) {
      validate(fields()[1], value);
      this.procTime = value;
      fieldSetFlags()[1] = true;
      return this; 
    }
    
    /** Checks whether the 'procTime' field has been set */
    public boolean hasProcTime() {
      return fieldSetFlags()[1];
    }
    
    /** Clears the value of the 'procTime' field */
    public com.tencent.andata.struct.avro.message.Message.Builder clearProcTime() {
      fieldSetFlags()[1] = false;
      return this;
    }

    /** Gets the value of the 'data' field */
    public java.nio.ByteBuffer getData() {
      return data;
    }
    
    /** Sets the value of the 'data' field */
    public com.tencent.andata.struct.avro.message.Message.Builder setData(java.nio.ByteBuffer value) {
      validate(fields()[2], value);
      this.data = value;
      fieldSetFlags()[2] = true;
      return this; 
    }
    
    /** Checks whether the 'data' field has been set */
    public boolean hasData() {
      return fieldSetFlags()[2];
    }
    
    /** Clears the value of the 'data' field */
    public com.tencent.andata.struct.avro.message.Message.Builder clearData() {
      data = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /** Gets the value of the 'msgType' field */
    public com.tencent.andata.struct.avro.message.MessageType getMsgType() {
      return msgType;
    }
    
    /** Sets the value of the 'msgType' field */
    public com.tencent.andata.struct.avro.message.Message.Builder setMsgType(com.tencent.andata.struct.avro.message.MessageType value) {
      validate(fields()[3], value);
      this.msgType = value;
      fieldSetFlags()[3] = true;
      return this; 
    }
    
    /** Checks whether the 'msgType' field has been set */
    public boolean hasMsgType() {
      return fieldSetFlags()[3];
    }
    
    /** Clears the value of the 'msgType' field */
    public com.tencent.andata.struct.avro.message.Message.Builder clearMsgType() {
      msgType = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    @Override
    public Message build() {
      try {
        Message record = new Message();
        record.schemaName = fieldSetFlags()[0] ? this.schemaName : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.procTime = fieldSetFlags()[1] ? this.procTime : (java.lang.Long) defaultValue(fields()[1]);
        record.data = fieldSetFlags()[2] ? this.data : (java.nio.ByteBuffer) defaultValue(fields()[2]);
        record.msgType = fieldSetFlags()[3] ? this.msgType : (com.tencent.andata.struct.avro.message.MessageType) defaultValue(fields()[3]);
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }
}
