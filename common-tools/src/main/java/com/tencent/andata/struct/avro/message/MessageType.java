/**
 * Autogenerated by Avro
 * 
 * DO NOT EDIT DIRECTLY
 */
package com.tencent.andata.struct.avro.message;  
@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public enum MessageType { 
  ROW_DATA, RAW_BYTES  ;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"enum\",\"name\":\"MessageType\",\"namespace\":\"com.tencent.andata.struct.avro.message\",\"symbols\":[\"ROW_DATA\",\"RAW_BYTES\"],\"default\":\"ROW_DATA\"}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }
}
