/**
 * Autogenerated by Avro
 * 
 * DO NOT EDIT DIRECTLY
 */
package com.tencent.andata.struct.avro.dts;  
@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class Incident extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"Incident\",\"namespace\":\"com.tencent.andata.struct.avro.dts\",\"fields\":[{\"name\":\"id\",\"type\":\"long\"},{\"name\":\"tenant_id\",\"type\":\"long\"},{\"name\":\"process_id\",\"type\":\"string\"},{\"name\":\"incident_id\",\"type\":\"string\"},{\"name\":\"title\",\"type\":\"string\"},{\"name\":\"content\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"status\",\"type\":\"int\"},{\"name\":\"priority\",\"type\":\"int\"},{\"name\":\"category_id\",\"type\":\"int\"},{\"name\":\"current_operator\",\"type\":\"string\"},{\"name\":\"service_rate\",\"type\":\"int\"},{\"name\":\"service_comment\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"solution\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"progress\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"customer_id\",\"type\":\"string\"},{\"name\":\"operators\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"staff_group_id\",\"type\":\"int\"},{\"name\":\"is_deleted\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"close_time\",\"type\":\"long\"},{\"name\":\"closer\",\"type\":\"string\"},{\"name\":\"close_type\",\"type\":\"int\"},{\"name\":\"source\",\"type\":\"string\"},{\"name\":\"creator\",\"type\":\"string\"},{\"name\":\"updater\",\"type\":\"string\"},{\"name\":\"create_time\",\"type\":\"long\"},{\"name\":\"update_time\",\"type\":\"long\"},{\"name\":\"should_assign_queue\",\"type\":\"int\"},{\"name\":\"fact_assign_queue\",\"type\":\"int\"},{\"name\":\"agent\",\"type\":\"string\"},{\"name\":\"conversation_id\",\"type\":\"string\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }
  @Deprecated public long id;
  @Deprecated public long tenant_id;
  @Deprecated public java.lang.CharSequence process_id;
  @Deprecated public java.lang.CharSequence incident_id;
  @Deprecated public java.lang.CharSequence title;
  @Deprecated public java.lang.CharSequence content;
  @Deprecated public int status;
  @Deprecated public int priority;
  @Deprecated public int category_id;
  @Deprecated public java.lang.CharSequence current_operator;
  @Deprecated public int service_rate;
  @Deprecated public java.lang.CharSequence service_comment;
  @Deprecated public java.lang.CharSequence solution;
  @Deprecated public java.lang.CharSequence progress;
  @Deprecated public java.lang.CharSequence customer_id;
  @Deprecated public java.lang.CharSequence operators;
  @Deprecated public int staff_group_id;
  @Deprecated public java.lang.Integer is_deleted;
  @Deprecated public long close_time;
  @Deprecated public java.lang.CharSequence closer;
  @Deprecated public int close_type;
  @Deprecated public java.lang.CharSequence source;
  @Deprecated public java.lang.CharSequence creator;
  @Deprecated public java.lang.CharSequence updater;
  @Deprecated public long create_time;
  @Deprecated public long update_time;
  @Deprecated public int should_assign_queue;
  @Deprecated public int fact_assign_queue;
  @Deprecated public java.lang.CharSequence agent;
  @Deprecated public java.lang.CharSequence conversation_id;

  /**
   * Default constructor.
   */
  public Incident() {}

  /**
   * All-args constructor.
   */
  public Incident(java.lang.Long id, java.lang.Long tenant_id, java.lang.CharSequence process_id, java.lang.CharSequence incident_id, java.lang.CharSequence title, java.lang.CharSequence content, java.lang.Integer status, java.lang.Integer priority, java.lang.Integer category_id, java.lang.CharSequence current_operator, java.lang.Integer service_rate, java.lang.CharSequence service_comment, java.lang.CharSequence solution, java.lang.CharSequence progress, java.lang.CharSequence customer_id, java.lang.CharSequence operators, java.lang.Integer staff_group_id, java.lang.Integer is_deleted, java.lang.Long close_time, java.lang.CharSequence closer, java.lang.Integer close_type, java.lang.CharSequence source, java.lang.CharSequence creator, java.lang.CharSequence updater, java.lang.Long create_time, java.lang.Long update_time, java.lang.Integer should_assign_queue, java.lang.Integer fact_assign_queue, java.lang.CharSequence agent, java.lang.CharSequence conversation_id) {
    this.id = id;
    this.tenant_id = tenant_id;
    this.process_id = process_id;
    this.incident_id = incident_id;
    this.title = title;
    this.content = content;
    this.status = status;
    this.priority = priority;
    this.category_id = category_id;
    this.current_operator = current_operator;
    this.service_rate = service_rate;
    this.service_comment = service_comment;
    this.solution = solution;
    this.progress = progress;
    this.customer_id = customer_id;
    this.operators = operators;
    this.staff_group_id = staff_group_id;
    this.is_deleted = is_deleted;
    this.close_time = close_time;
    this.closer = closer;
    this.close_type = close_type;
    this.source = source;
    this.creator = creator;
    this.updater = updater;
    this.create_time = create_time;
    this.update_time = update_time;
    this.should_assign_queue = should_assign_queue;
    this.fact_assign_queue = fact_assign_queue;
    this.agent = agent;
    this.conversation_id = conversation_id;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call. 
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return id;
    case 1: return tenant_id;
    case 2: return process_id;
    case 3: return incident_id;
    case 4: return title;
    case 5: return content;
    case 6: return status;
    case 7: return priority;
    case 8: return category_id;
    case 9: return current_operator;
    case 10: return service_rate;
    case 11: return service_comment;
    case 12: return solution;
    case 13: return progress;
    case 14: return customer_id;
    case 15: return operators;
    case 16: return staff_group_id;
    case 17: return is_deleted;
    case 18: return close_time;
    case 19: return closer;
    case 20: return close_type;
    case 21: return source;
    case 22: return creator;
    case 23: return updater;
    case 24: return create_time;
    case 25: return update_time;
    case 26: return should_assign_queue;
    case 27: return fact_assign_queue;
    case 28: return agent;
    case 29: return conversation_id;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }
  // Used by DatumReader.  Applications should not call. 
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: id = (java.lang.Long)value$; break;
    case 1: tenant_id = (java.lang.Long)value$; break;
    case 2: process_id = (java.lang.CharSequence)value$; break;
    case 3: incident_id = (java.lang.CharSequence)value$; break;
    case 4: title = (java.lang.CharSequence)value$; break;
    case 5: content = (java.lang.CharSequence)value$; break;
    case 6: status = (java.lang.Integer)value$; break;
    case 7: priority = (java.lang.Integer)value$; break;
    case 8: category_id = (java.lang.Integer)value$; break;
    case 9: current_operator = (java.lang.CharSequence)value$; break;
    case 10: service_rate = (java.lang.Integer)value$; break;
    case 11: service_comment = (java.lang.CharSequence)value$; break;
    case 12: solution = (java.lang.CharSequence)value$; break;
    case 13: progress = (java.lang.CharSequence)value$; break;
    case 14: customer_id = (java.lang.CharSequence)value$; break;
    case 15: operators = (java.lang.CharSequence)value$; break;
    case 16: staff_group_id = (java.lang.Integer)value$; break;
    case 17: is_deleted = (java.lang.Integer)value$; break;
    case 18: close_time = (java.lang.Long)value$; break;
    case 19: closer = (java.lang.CharSequence)value$; break;
    case 20: close_type = (java.lang.Integer)value$; break;
    case 21: source = (java.lang.CharSequence)value$; break;
    case 22: creator = (java.lang.CharSequence)value$; break;
    case 23: updater = (java.lang.CharSequence)value$; break;
    case 24: create_time = (java.lang.Long)value$; break;
    case 25: update_time = (java.lang.Long)value$; break;
    case 26: should_assign_queue = (java.lang.Integer)value$; break;
    case 27: fact_assign_queue = (java.lang.Integer)value$; break;
    case 28: agent = (java.lang.CharSequence)value$; break;
    case 29: conversation_id = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'id' field.
   */
  public java.lang.Long getId() {
    return id;
  }

  /**
   * Sets the value of the 'id' field.
   * @param value the value to set.
   */
  public void setId(java.lang.Long value) {
    this.id = value;
  }

  /**
   * Gets the value of the 'tenant_id' field.
   */
  public java.lang.Long getTenantId() {
    return tenant_id;
  }

  /**
   * Sets the value of the 'tenant_id' field.
   * @param value the value to set.
   */
  public void setTenantId(java.lang.Long value) {
    this.tenant_id = value;
  }

  /**
   * Gets the value of the 'process_id' field.
   */
  public java.lang.CharSequence getProcessId() {
    return process_id;
  }

  /**
   * Sets the value of the 'process_id' field.
   * @param value the value to set.
   */
  public void setProcessId(java.lang.CharSequence value) {
    this.process_id = value;
  }

  /**
   * Gets the value of the 'incident_id' field.
   */
  public java.lang.CharSequence getIncidentId() {
    return incident_id;
  }

  /**
   * Sets the value of the 'incident_id' field.
   * @param value the value to set.
   */
  public void setIncidentId(java.lang.CharSequence value) {
    this.incident_id = value;
  }

  /**
   * Gets the value of the 'title' field.
   */
  public java.lang.CharSequence getTitle() {
    return title;
  }

  /**
   * Sets the value of the 'title' field.
   * @param value the value to set.
   */
  public void setTitle(java.lang.CharSequence value) {
    this.title = value;
  }

  /**
   * Gets the value of the 'content' field.
   */
  public java.lang.CharSequence getContent() {
    return content;
  }

  /**
   * Sets the value of the 'content' field.
   * @param value the value to set.
   */
  public void setContent(java.lang.CharSequence value) {
    this.content = value;
  }

  /**
   * Gets the value of the 'status' field.
   */
  public java.lang.Integer getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(java.lang.Integer value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'priority' field.
   */
  public java.lang.Integer getPriority() {
    return priority;
  }

  /**
   * Sets the value of the 'priority' field.
   * @param value the value to set.
   */
  public void setPriority(java.lang.Integer value) {
    this.priority = value;
  }

  /**
   * Gets the value of the 'category_id' field.
   */
  public java.lang.Integer getCategoryId() {
    return category_id;
  }

  /**
   * Sets the value of the 'category_id' field.
   * @param value the value to set.
   */
  public void setCategoryId(java.lang.Integer value) {
    this.category_id = value;
  }

  /**
   * Gets the value of the 'current_operator' field.
   */
  public java.lang.CharSequence getCurrentOperator() {
    return current_operator;
  }

  /**
   * Sets the value of the 'current_operator' field.
   * @param value the value to set.
   */
  public void setCurrentOperator(java.lang.CharSequence value) {
    this.current_operator = value;
  }

  /**
   * Gets the value of the 'service_rate' field.
   */
  public java.lang.Integer getServiceRate() {
    return service_rate;
  }

  /**
   * Sets the value of the 'service_rate' field.
   * @param value the value to set.
   */
  public void setServiceRate(java.lang.Integer value) {
    this.service_rate = value;
  }

  /**
   * Gets the value of the 'service_comment' field.
   */
  public java.lang.CharSequence getServiceComment() {
    return service_comment;
  }

  /**
   * Sets the value of the 'service_comment' field.
   * @param value the value to set.
   */
  public void setServiceComment(java.lang.CharSequence value) {
    this.service_comment = value;
  }

  /**
   * Gets the value of the 'solution' field.
   */
  public java.lang.CharSequence getSolution() {
    return solution;
  }

  /**
   * Sets the value of the 'solution' field.
   * @param value the value to set.
   */
  public void setSolution(java.lang.CharSequence value) {
    this.solution = value;
  }

  /**
   * Gets the value of the 'progress' field.
   */
  public java.lang.CharSequence getProgress() {
    return progress;
  }

  /**
   * Sets the value of the 'progress' field.
   * @param value the value to set.
   */
  public void setProgress(java.lang.CharSequence value) {
    this.progress = value;
  }

  /**
   * Gets the value of the 'customer_id' field.
   */
  public java.lang.CharSequence getCustomerId() {
    return customer_id;
  }

  /**
   * Sets the value of the 'customer_id' field.
   * @param value the value to set.
   */
  public void setCustomerId(java.lang.CharSequence value) {
    this.customer_id = value;
  }

  /**
   * Gets the value of the 'operators' field.
   */
  public java.lang.CharSequence getOperators() {
    return operators;
  }

  /**
   * Sets the value of the 'operators' field.
   * @param value the value to set.
   */
  public void setOperators(java.lang.CharSequence value) {
    this.operators = value;
  }

  /**
   * Gets the value of the 'staff_group_id' field.
   */
  public java.lang.Integer getStaffGroupId() {
    return staff_group_id;
  }

  /**
   * Sets the value of the 'staff_group_id' field.
   * @param value the value to set.
   */
  public void setStaffGroupId(java.lang.Integer value) {
    this.staff_group_id = value;
  }

  /**
   * Gets the value of the 'is_deleted' field.
   */
  public java.lang.Integer getIsDeleted() {
    return is_deleted;
  }

  /**
   * Sets the value of the 'is_deleted' field.
   * @param value the value to set.
   */
  public void setIsDeleted(java.lang.Integer value) {
    this.is_deleted = value;
  }

  /**
   * Gets the value of the 'close_time' field.
   */
  public java.lang.Long getCloseTime() {
    return close_time;
  }

  /**
   * Sets the value of the 'close_time' field.
   * @param value the value to set.
   */
  public void setCloseTime(java.lang.Long value) {
    this.close_time = value;
  }

  /**
   * Gets the value of the 'closer' field.
   */
  public java.lang.CharSequence getCloser() {
    return closer;
  }

  /**
   * Sets the value of the 'closer' field.
   * @param value the value to set.
   */
  public void setCloser(java.lang.CharSequence value) {
    this.closer = value;
  }

  /**
   * Gets the value of the 'close_type' field.
   */
  public java.lang.Integer getCloseType() {
    return close_type;
  }

  /**
   * Sets the value of the 'close_type' field.
   * @param value the value to set.
   */
  public void setCloseType(java.lang.Integer value) {
    this.close_type = value;
  }

  /**
   * Gets the value of the 'source' field.
   */
  public java.lang.CharSequence getSource() {
    return source;
  }

  /**
   * Sets the value of the 'source' field.
   * @param value the value to set.
   */
  public void setSource(java.lang.CharSequence value) {
    this.source = value;
  }

  /**
   * Gets the value of the 'creator' field.
   */
  public java.lang.CharSequence getCreator() {
    return creator;
  }

  /**
   * Sets the value of the 'creator' field.
   * @param value the value to set.
   */
  public void setCreator(java.lang.CharSequence value) {
    this.creator = value;
  }

  /**
   * Gets the value of the 'updater' field.
   */
  public java.lang.CharSequence getUpdater() {
    return updater;
  }

  /**
   * Sets the value of the 'updater' field.
   * @param value the value to set.
   */
  public void setUpdater(java.lang.CharSequence value) {
    this.updater = value;
  }

  /**
   * Gets the value of the 'create_time' field.
   */
  public java.lang.Long getCreateTime() {
    return create_time;
  }

  /**
   * Sets the value of the 'create_time' field.
   * @param value the value to set.
   */
  public void setCreateTime(java.lang.Long value) {
    this.create_time = value;
  }

  /**
   * Gets the value of the 'update_time' field.
   */
  public java.lang.Long getUpdateTime() {
    return update_time;
  }

  /**
   * Sets the value of the 'update_time' field.
   * @param value the value to set.
   */
  public void setUpdateTime(java.lang.Long value) {
    this.update_time = value;
  }

  /**
   * Gets the value of the 'should_assign_queue' field.
   */
  public java.lang.Integer getShouldAssignQueue() {
    return should_assign_queue;
  }

  /**
   * Sets the value of the 'should_assign_queue' field.
   * @param value the value to set.
   */
  public void setShouldAssignQueue(java.lang.Integer value) {
    this.should_assign_queue = value;
  }

  /**
   * Gets the value of the 'fact_assign_queue' field.
   */
  public java.lang.Integer getFactAssignQueue() {
    return fact_assign_queue;
  }

  /**
   * Sets the value of the 'fact_assign_queue' field.
   * @param value the value to set.
   */
  public void setFactAssignQueue(java.lang.Integer value) {
    this.fact_assign_queue = value;
  }

  /**
   * Gets the value of the 'agent' field.
   */
  public java.lang.CharSequence getAgent() {
    return agent;
  }

  /**
   * Sets the value of the 'agent' field.
   * @param value the value to set.
   */
  public void setAgent(java.lang.CharSequence value) {
    this.agent = value;
  }

  /**
   * Gets the value of the 'conversation_id' field.
   */
  public java.lang.CharSequence getConversationId() {
    return conversation_id;
  }

  /**
   * Sets the value of the 'conversation_id' field.
   * @param value the value to set.
   */
  public void setConversationId(java.lang.CharSequence value) {
    this.conversation_id = value;
  }

  /** Creates a new Incident RecordBuilder */
  public static com.tencent.andata.struct.avro.dts.Incident.Builder newBuilder() {
    return new com.tencent.andata.struct.avro.dts.Incident.Builder();
  }
  
  /** Creates a new Incident RecordBuilder by copying an existing Builder */
  public static com.tencent.andata.struct.avro.dts.Incident.Builder newBuilder(com.tencent.andata.struct.avro.dts.Incident.Builder other) {
    return new com.tencent.andata.struct.avro.dts.Incident.Builder(other);
  }
  
  /** Creates a new Incident RecordBuilder by copying an existing Incident instance */
  public static com.tencent.andata.struct.avro.dts.Incident.Builder newBuilder(com.tencent.andata.struct.avro.dts.Incident other) {
    return new com.tencent.andata.struct.avro.dts.Incident.Builder(other);
  }
  
  /**
   * RecordBuilder for Incident instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<Incident>
    implements org.apache.avro.data.RecordBuilder<Incident> {

    private long id;
    private long tenant_id;
    private java.lang.CharSequence process_id;
    private java.lang.CharSequence incident_id;
    private java.lang.CharSequence title;
    private java.lang.CharSequence content;
    private int status;
    private int priority;
    private int category_id;
    private java.lang.CharSequence current_operator;
    private int service_rate;
    private java.lang.CharSequence service_comment;
    private java.lang.CharSequence solution;
    private java.lang.CharSequence progress;
    private java.lang.CharSequence customer_id;
    private java.lang.CharSequence operators;
    private int staff_group_id;
    private java.lang.Integer is_deleted;
    private long close_time;
    private java.lang.CharSequence closer;
    private int close_type;
    private java.lang.CharSequence source;
    private java.lang.CharSequence creator;
    private java.lang.CharSequence updater;
    private long create_time;
    private long update_time;
    private int should_assign_queue;
    private int fact_assign_queue;
    private java.lang.CharSequence agent;
    private java.lang.CharSequence conversation_id;

    /** Creates a new Builder */
    private Builder() {
      super(com.tencent.andata.struct.avro.dts.Incident.SCHEMA$);
    }
    
    /** Creates a Builder by copying an existing Builder */
    private Builder(com.tencent.andata.struct.avro.dts.Incident.Builder other) {
      super(other);
    }
    
    /** Creates a Builder by copying an existing Incident instance */
    private Builder(com.tencent.andata.struct.avro.dts.Incident other) {
            super(com.tencent.andata.struct.avro.dts.Incident.SCHEMA$);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.tenant_id)) {
        this.tenant_id = data().deepCopy(fields()[1].schema(), other.tenant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.process_id)) {
        this.process_id = data().deepCopy(fields()[2].schema(), other.process_id);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.incident_id)) {
        this.incident_id = data().deepCopy(fields()[3].schema(), other.incident_id);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.title)) {
        this.title = data().deepCopy(fields()[4].schema(), other.title);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.content)) {
        this.content = data().deepCopy(fields()[5].schema(), other.content);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.status)) {
        this.status = data().deepCopy(fields()[6].schema(), other.status);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.priority)) {
        this.priority = data().deepCopy(fields()[7].schema(), other.priority);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.category_id)) {
        this.category_id = data().deepCopy(fields()[8].schema(), other.category_id);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.current_operator)) {
        this.current_operator = data().deepCopy(fields()[9].schema(), other.current_operator);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.service_rate)) {
        this.service_rate = data().deepCopy(fields()[10].schema(), other.service_rate);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.service_comment)) {
        this.service_comment = data().deepCopy(fields()[11].schema(), other.service_comment);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.solution)) {
        this.solution = data().deepCopy(fields()[12].schema(), other.solution);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.progress)) {
        this.progress = data().deepCopy(fields()[13].schema(), other.progress);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.customer_id)) {
        this.customer_id = data().deepCopy(fields()[14].schema(), other.customer_id);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.operators)) {
        this.operators = data().deepCopy(fields()[15].schema(), other.operators);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.staff_group_id)) {
        this.staff_group_id = data().deepCopy(fields()[16].schema(), other.staff_group_id);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.is_deleted)) {
        this.is_deleted = data().deepCopy(fields()[17].schema(), other.is_deleted);
        fieldSetFlags()[17] = true;
      }
      if (isValidValue(fields()[18], other.close_time)) {
        this.close_time = data().deepCopy(fields()[18].schema(), other.close_time);
        fieldSetFlags()[18] = true;
      }
      if (isValidValue(fields()[19], other.closer)) {
        this.closer = data().deepCopy(fields()[19].schema(), other.closer);
        fieldSetFlags()[19] = true;
      }
      if (isValidValue(fields()[20], other.close_type)) {
        this.close_type = data().deepCopy(fields()[20].schema(), other.close_type);
        fieldSetFlags()[20] = true;
      }
      if (isValidValue(fields()[21], other.source)) {
        this.source = data().deepCopy(fields()[21].schema(), other.source);
        fieldSetFlags()[21] = true;
      }
      if (isValidValue(fields()[22], other.creator)) {
        this.creator = data().deepCopy(fields()[22].schema(), other.creator);
        fieldSetFlags()[22] = true;
      }
      if (isValidValue(fields()[23], other.updater)) {
        this.updater = data().deepCopy(fields()[23].schema(), other.updater);
        fieldSetFlags()[23] = true;
      }
      if (isValidValue(fields()[24], other.create_time)) {
        this.create_time = data().deepCopy(fields()[24].schema(), other.create_time);
        fieldSetFlags()[24] = true;
      }
      if (isValidValue(fields()[25], other.update_time)) {
        this.update_time = data().deepCopy(fields()[25].schema(), other.update_time);
        fieldSetFlags()[25] = true;
      }
      if (isValidValue(fields()[26], other.should_assign_queue)) {
        this.should_assign_queue = data().deepCopy(fields()[26].schema(), other.should_assign_queue);
        fieldSetFlags()[26] = true;
      }
      if (isValidValue(fields()[27], other.fact_assign_queue)) {
        this.fact_assign_queue = data().deepCopy(fields()[27].schema(), other.fact_assign_queue);
        fieldSetFlags()[27] = true;
      }
      if (isValidValue(fields()[28], other.agent)) {
        this.agent = data().deepCopy(fields()[28].schema(), other.agent);
        fieldSetFlags()[28] = true;
      }
      if (isValidValue(fields()[29], other.conversation_id)) {
        this.conversation_id = data().deepCopy(fields()[29].schema(), other.conversation_id);
        fieldSetFlags()[29] = true;
      }
    }

    /** Gets the value of the 'id' field */
    public java.lang.Long getId() {
      return id;
    }
    
    /** Sets the value of the 'id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setId(long value) {
      validate(fields()[0], value);
      this.id = value;
      fieldSetFlags()[0] = true;
      return this; 
    }
    
    /** Checks whether the 'id' field has been set */
    public boolean hasId() {
      return fieldSetFlags()[0];
    }
    
    /** Clears the value of the 'id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearId() {
      fieldSetFlags()[0] = false;
      return this;
    }

    /** Gets the value of the 'tenant_id' field */
    public java.lang.Long getTenantId() {
      return tenant_id;
    }
    
    /** Sets the value of the 'tenant_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setTenantId(long value) {
      validate(fields()[1], value);
      this.tenant_id = value;
      fieldSetFlags()[1] = true;
      return this; 
    }
    
    /** Checks whether the 'tenant_id' field has been set */
    public boolean hasTenantId() {
      return fieldSetFlags()[1];
    }
    
    /** Clears the value of the 'tenant_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearTenantId() {
      fieldSetFlags()[1] = false;
      return this;
    }

    /** Gets the value of the 'process_id' field */
    public java.lang.CharSequence getProcessId() {
      return process_id;
    }
    
    /** Sets the value of the 'process_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setProcessId(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.process_id = value;
      fieldSetFlags()[2] = true;
      return this; 
    }
    
    /** Checks whether the 'process_id' field has been set */
    public boolean hasProcessId() {
      return fieldSetFlags()[2];
    }
    
    /** Clears the value of the 'process_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearProcessId() {
      process_id = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /** Gets the value of the 'incident_id' field */
    public java.lang.CharSequence getIncidentId() {
      return incident_id;
    }
    
    /** Sets the value of the 'incident_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setIncidentId(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.incident_id = value;
      fieldSetFlags()[3] = true;
      return this; 
    }
    
    /** Checks whether the 'incident_id' field has been set */
    public boolean hasIncidentId() {
      return fieldSetFlags()[3];
    }
    
    /** Clears the value of the 'incident_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearIncidentId() {
      incident_id = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /** Gets the value of the 'title' field */
    public java.lang.CharSequence getTitle() {
      return title;
    }
    
    /** Sets the value of the 'title' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setTitle(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.title = value;
      fieldSetFlags()[4] = true;
      return this; 
    }
    
    /** Checks whether the 'title' field has been set */
    public boolean hasTitle() {
      return fieldSetFlags()[4];
    }
    
    /** Clears the value of the 'title' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearTitle() {
      title = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /** Gets the value of the 'content' field */
    public java.lang.CharSequence getContent() {
      return content;
    }
    
    /** Sets the value of the 'content' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setContent(java.lang.CharSequence value) {
      validate(fields()[5], value);
      this.content = value;
      fieldSetFlags()[5] = true;
      return this; 
    }
    
    /** Checks whether the 'content' field has been set */
    public boolean hasContent() {
      return fieldSetFlags()[5];
    }
    
    /** Clears the value of the 'content' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearContent() {
      content = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /** Gets the value of the 'status' field */
    public java.lang.Integer getStatus() {
      return status;
    }
    
    /** Sets the value of the 'status' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setStatus(int value) {
      validate(fields()[6], value);
      this.status = value;
      fieldSetFlags()[6] = true;
      return this; 
    }
    
    /** Checks whether the 'status' field has been set */
    public boolean hasStatus() {
      return fieldSetFlags()[6];
    }
    
    /** Clears the value of the 'status' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearStatus() {
      fieldSetFlags()[6] = false;
      return this;
    }

    /** Gets the value of the 'priority' field */
    public java.lang.Integer getPriority() {
      return priority;
    }
    
    /** Sets the value of the 'priority' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setPriority(int value) {
      validate(fields()[7], value);
      this.priority = value;
      fieldSetFlags()[7] = true;
      return this; 
    }
    
    /** Checks whether the 'priority' field has been set */
    public boolean hasPriority() {
      return fieldSetFlags()[7];
    }
    
    /** Clears the value of the 'priority' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearPriority() {
      fieldSetFlags()[7] = false;
      return this;
    }

    /** Gets the value of the 'category_id' field */
    public java.lang.Integer getCategoryId() {
      return category_id;
    }
    
    /** Sets the value of the 'category_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setCategoryId(int value) {
      validate(fields()[8], value);
      this.category_id = value;
      fieldSetFlags()[8] = true;
      return this; 
    }
    
    /** Checks whether the 'category_id' field has been set */
    public boolean hasCategoryId() {
      return fieldSetFlags()[8];
    }
    
    /** Clears the value of the 'category_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearCategoryId() {
      fieldSetFlags()[8] = false;
      return this;
    }

    /** Gets the value of the 'current_operator' field */
    public java.lang.CharSequence getCurrentOperator() {
      return current_operator;
    }
    
    /** Sets the value of the 'current_operator' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setCurrentOperator(java.lang.CharSequence value) {
      validate(fields()[9], value);
      this.current_operator = value;
      fieldSetFlags()[9] = true;
      return this; 
    }
    
    /** Checks whether the 'current_operator' field has been set */
    public boolean hasCurrentOperator() {
      return fieldSetFlags()[9];
    }
    
    /** Clears the value of the 'current_operator' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearCurrentOperator() {
      current_operator = null;
      fieldSetFlags()[9] = false;
      return this;
    }

    /** Gets the value of the 'service_rate' field */
    public java.lang.Integer getServiceRate() {
      return service_rate;
    }
    
    /** Sets the value of the 'service_rate' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setServiceRate(int value) {
      validate(fields()[10], value);
      this.service_rate = value;
      fieldSetFlags()[10] = true;
      return this; 
    }
    
    /** Checks whether the 'service_rate' field has been set */
    public boolean hasServiceRate() {
      return fieldSetFlags()[10];
    }
    
    /** Clears the value of the 'service_rate' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearServiceRate() {
      fieldSetFlags()[10] = false;
      return this;
    }

    /** Gets the value of the 'service_comment' field */
    public java.lang.CharSequence getServiceComment() {
      return service_comment;
    }
    
    /** Sets the value of the 'service_comment' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setServiceComment(java.lang.CharSequence value) {
      validate(fields()[11], value);
      this.service_comment = value;
      fieldSetFlags()[11] = true;
      return this; 
    }
    
    /** Checks whether the 'service_comment' field has been set */
    public boolean hasServiceComment() {
      return fieldSetFlags()[11];
    }
    
    /** Clears the value of the 'service_comment' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearServiceComment() {
      service_comment = null;
      fieldSetFlags()[11] = false;
      return this;
    }

    /** Gets the value of the 'solution' field */
    public java.lang.CharSequence getSolution() {
      return solution;
    }
    
    /** Sets the value of the 'solution' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setSolution(java.lang.CharSequence value) {
      validate(fields()[12], value);
      this.solution = value;
      fieldSetFlags()[12] = true;
      return this; 
    }
    
    /** Checks whether the 'solution' field has been set */
    public boolean hasSolution() {
      return fieldSetFlags()[12];
    }
    
    /** Clears the value of the 'solution' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearSolution() {
      solution = null;
      fieldSetFlags()[12] = false;
      return this;
    }

    /** Gets the value of the 'progress' field */
    public java.lang.CharSequence getProgress() {
      return progress;
    }
    
    /** Sets the value of the 'progress' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setProgress(java.lang.CharSequence value) {
      validate(fields()[13], value);
      this.progress = value;
      fieldSetFlags()[13] = true;
      return this; 
    }
    
    /** Checks whether the 'progress' field has been set */
    public boolean hasProgress() {
      return fieldSetFlags()[13];
    }
    
    /** Clears the value of the 'progress' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearProgress() {
      progress = null;
      fieldSetFlags()[13] = false;
      return this;
    }

    /** Gets the value of the 'customer_id' field */
    public java.lang.CharSequence getCustomerId() {
      return customer_id;
    }
    
    /** Sets the value of the 'customer_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setCustomerId(java.lang.CharSequence value) {
      validate(fields()[14], value);
      this.customer_id = value;
      fieldSetFlags()[14] = true;
      return this; 
    }
    
    /** Checks whether the 'customer_id' field has been set */
    public boolean hasCustomerId() {
      return fieldSetFlags()[14];
    }
    
    /** Clears the value of the 'customer_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearCustomerId() {
      customer_id = null;
      fieldSetFlags()[14] = false;
      return this;
    }

    /** Gets the value of the 'operators' field */
    public java.lang.CharSequence getOperators() {
      return operators;
    }
    
    /** Sets the value of the 'operators' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setOperators(java.lang.CharSequence value) {
      validate(fields()[15], value);
      this.operators = value;
      fieldSetFlags()[15] = true;
      return this; 
    }
    
    /** Checks whether the 'operators' field has been set */
    public boolean hasOperators() {
      return fieldSetFlags()[15];
    }
    
    /** Clears the value of the 'operators' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearOperators() {
      operators = null;
      fieldSetFlags()[15] = false;
      return this;
    }

    /** Gets the value of the 'staff_group_id' field */
    public java.lang.Integer getStaffGroupId() {
      return staff_group_id;
    }
    
    /** Sets the value of the 'staff_group_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setStaffGroupId(int value) {
      validate(fields()[16], value);
      this.staff_group_id = value;
      fieldSetFlags()[16] = true;
      return this; 
    }
    
    /** Checks whether the 'staff_group_id' field has been set */
    public boolean hasStaffGroupId() {
      return fieldSetFlags()[16];
    }
    
    /** Clears the value of the 'staff_group_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearStaffGroupId() {
      fieldSetFlags()[16] = false;
      return this;
    }

    /** Gets the value of the 'is_deleted' field */
    public java.lang.Integer getIsDeleted() {
      return is_deleted;
    }
    
    /** Sets the value of the 'is_deleted' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setIsDeleted(java.lang.Integer value) {
      validate(fields()[17], value);
      this.is_deleted = value;
      fieldSetFlags()[17] = true;
      return this; 
    }
    
    /** Checks whether the 'is_deleted' field has been set */
    public boolean hasIsDeleted() {
      return fieldSetFlags()[17];
    }
    
    /** Clears the value of the 'is_deleted' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearIsDeleted() {
      is_deleted = null;
      fieldSetFlags()[17] = false;
      return this;
    }

    /** Gets the value of the 'close_time' field */
    public java.lang.Long getCloseTime() {
      return close_time;
    }
    
    /** Sets the value of the 'close_time' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setCloseTime(long value) {
      validate(fields()[18], value);
      this.close_time = value;
      fieldSetFlags()[18] = true;
      return this; 
    }
    
    /** Checks whether the 'close_time' field has been set */
    public boolean hasCloseTime() {
      return fieldSetFlags()[18];
    }
    
    /** Clears the value of the 'close_time' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearCloseTime() {
      fieldSetFlags()[18] = false;
      return this;
    }

    /** Gets the value of the 'closer' field */
    public java.lang.CharSequence getCloser() {
      return closer;
    }
    
    /** Sets the value of the 'closer' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setCloser(java.lang.CharSequence value) {
      validate(fields()[19], value);
      this.closer = value;
      fieldSetFlags()[19] = true;
      return this; 
    }
    
    /** Checks whether the 'closer' field has been set */
    public boolean hasCloser() {
      return fieldSetFlags()[19];
    }
    
    /** Clears the value of the 'closer' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearCloser() {
      closer = null;
      fieldSetFlags()[19] = false;
      return this;
    }

    /** Gets the value of the 'close_type' field */
    public java.lang.Integer getCloseType() {
      return close_type;
    }
    
    /** Sets the value of the 'close_type' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setCloseType(int value) {
      validate(fields()[20], value);
      this.close_type = value;
      fieldSetFlags()[20] = true;
      return this; 
    }
    
    /** Checks whether the 'close_type' field has been set */
    public boolean hasCloseType() {
      return fieldSetFlags()[20];
    }
    
    /** Clears the value of the 'close_type' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearCloseType() {
      fieldSetFlags()[20] = false;
      return this;
    }

    /** Gets the value of the 'source' field */
    public java.lang.CharSequence getSource() {
      return source;
    }
    
    /** Sets the value of the 'source' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setSource(java.lang.CharSequence value) {
      validate(fields()[21], value);
      this.source = value;
      fieldSetFlags()[21] = true;
      return this; 
    }
    
    /** Checks whether the 'source' field has been set */
    public boolean hasSource() {
      return fieldSetFlags()[21];
    }
    
    /** Clears the value of the 'source' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearSource() {
      source = null;
      fieldSetFlags()[21] = false;
      return this;
    }

    /** Gets the value of the 'creator' field */
    public java.lang.CharSequence getCreator() {
      return creator;
    }
    
    /** Sets the value of the 'creator' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setCreator(java.lang.CharSequence value) {
      validate(fields()[22], value);
      this.creator = value;
      fieldSetFlags()[22] = true;
      return this; 
    }
    
    /** Checks whether the 'creator' field has been set */
    public boolean hasCreator() {
      return fieldSetFlags()[22];
    }
    
    /** Clears the value of the 'creator' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearCreator() {
      creator = null;
      fieldSetFlags()[22] = false;
      return this;
    }

    /** Gets the value of the 'updater' field */
    public java.lang.CharSequence getUpdater() {
      return updater;
    }
    
    /** Sets the value of the 'updater' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setUpdater(java.lang.CharSequence value) {
      validate(fields()[23], value);
      this.updater = value;
      fieldSetFlags()[23] = true;
      return this; 
    }
    
    /** Checks whether the 'updater' field has been set */
    public boolean hasUpdater() {
      return fieldSetFlags()[23];
    }
    
    /** Clears the value of the 'updater' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearUpdater() {
      updater = null;
      fieldSetFlags()[23] = false;
      return this;
    }

    /** Gets the value of the 'create_time' field */
    public java.lang.Long getCreateTime() {
      return create_time;
    }
    
    /** Sets the value of the 'create_time' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setCreateTime(long value) {
      validate(fields()[24], value);
      this.create_time = value;
      fieldSetFlags()[24] = true;
      return this; 
    }
    
    /** Checks whether the 'create_time' field has been set */
    public boolean hasCreateTime() {
      return fieldSetFlags()[24];
    }
    
    /** Clears the value of the 'create_time' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearCreateTime() {
      fieldSetFlags()[24] = false;
      return this;
    }

    /** Gets the value of the 'update_time' field */
    public java.lang.Long getUpdateTime() {
      return update_time;
    }
    
    /** Sets the value of the 'update_time' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setUpdateTime(long value) {
      validate(fields()[25], value);
      this.update_time = value;
      fieldSetFlags()[25] = true;
      return this; 
    }
    
    /** Checks whether the 'update_time' field has been set */
    public boolean hasUpdateTime() {
      return fieldSetFlags()[25];
    }
    
    /** Clears the value of the 'update_time' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearUpdateTime() {
      fieldSetFlags()[25] = false;
      return this;
    }

    /** Gets the value of the 'should_assign_queue' field */
    public java.lang.Integer getShouldAssignQueue() {
      return should_assign_queue;
    }
    
    /** Sets the value of the 'should_assign_queue' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setShouldAssignQueue(int value) {
      validate(fields()[26], value);
      this.should_assign_queue = value;
      fieldSetFlags()[26] = true;
      return this; 
    }
    
    /** Checks whether the 'should_assign_queue' field has been set */
    public boolean hasShouldAssignQueue() {
      return fieldSetFlags()[26];
    }
    
    /** Clears the value of the 'should_assign_queue' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearShouldAssignQueue() {
      fieldSetFlags()[26] = false;
      return this;
    }

    /** Gets the value of the 'fact_assign_queue' field */
    public java.lang.Integer getFactAssignQueue() {
      return fact_assign_queue;
    }
    
    /** Sets the value of the 'fact_assign_queue' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setFactAssignQueue(int value) {
      validate(fields()[27], value);
      this.fact_assign_queue = value;
      fieldSetFlags()[27] = true;
      return this; 
    }
    
    /** Checks whether the 'fact_assign_queue' field has been set */
    public boolean hasFactAssignQueue() {
      return fieldSetFlags()[27];
    }
    
    /** Clears the value of the 'fact_assign_queue' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearFactAssignQueue() {
      fieldSetFlags()[27] = false;
      return this;
    }

    /** Gets the value of the 'agent' field */
    public java.lang.CharSequence getAgent() {
      return agent;
    }
    
    /** Sets the value of the 'agent' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setAgent(java.lang.CharSequence value) {
      validate(fields()[28], value);
      this.agent = value;
      fieldSetFlags()[28] = true;
      return this; 
    }
    
    /** Checks whether the 'agent' field has been set */
    public boolean hasAgent() {
      return fieldSetFlags()[28];
    }
    
    /** Clears the value of the 'agent' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearAgent() {
      agent = null;
      fieldSetFlags()[28] = false;
      return this;
    }

    /** Gets the value of the 'conversation_id' field */
    public java.lang.CharSequence getConversationId() {
      return conversation_id;
    }
    
    /** Sets the value of the 'conversation_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder setConversationId(java.lang.CharSequence value) {
      validate(fields()[29], value);
      this.conversation_id = value;
      fieldSetFlags()[29] = true;
      return this; 
    }
    
    /** Checks whether the 'conversation_id' field has been set */
    public boolean hasConversationId() {
      return fieldSetFlags()[29];
    }
    
    /** Clears the value of the 'conversation_id' field */
    public com.tencent.andata.struct.avro.dts.Incident.Builder clearConversationId() {
      conversation_id = null;
      fieldSetFlags()[29] = false;
      return this;
    }

    @Override
    public Incident build() {
      try {
        Incident record = new Incident();
        record.id = fieldSetFlags()[0] ? this.id : (java.lang.Long) defaultValue(fields()[0]);
        record.tenant_id = fieldSetFlags()[1] ? this.tenant_id : (java.lang.Long) defaultValue(fields()[1]);
        record.process_id = fieldSetFlags()[2] ? this.process_id : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.incident_id = fieldSetFlags()[3] ? this.incident_id : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.title = fieldSetFlags()[4] ? this.title : (java.lang.CharSequence) defaultValue(fields()[4]);
        record.content = fieldSetFlags()[5] ? this.content : (java.lang.CharSequence) defaultValue(fields()[5]);
        record.status = fieldSetFlags()[6] ? this.status : (java.lang.Integer) defaultValue(fields()[6]);
        record.priority = fieldSetFlags()[7] ? this.priority : (java.lang.Integer) defaultValue(fields()[7]);
        record.category_id = fieldSetFlags()[8] ? this.category_id : (java.lang.Integer) defaultValue(fields()[8]);
        record.current_operator = fieldSetFlags()[9] ? this.current_operator : (java.lang.CharSequence) defaultValue(fields()[9]);
        record.service_rate = fieldSetFlags()[10] ? this.service_rate : (java.lang.Integer) defaultValue(fields()[10]);
        record.service_comment = fieldSetFlags()[11] ? this.service_comment : (java.lang.CharSequence) defaultValue(fields()[11]);
        record.solution = fieldSetFlags()[12] ? this.solution : (java.lang.CharSequence) defaultValue(fields()[12]);
        record.progress = fieldSetFlags()[13] ? this.progress : (java.lang.CharSequence) defaultValue(fields()[13]);
        record.customer_id = fieldSetFlags()[14] ? this.customer_id : (java.lang.CharSequence) defaultValue(fields()[14]);
        record.operators = fieldSetFlags()[15] ? this.operators : (java.lang.CharSequence) defaultValue(fields()[15]);
        record.staff_group_id = fieldSetFlags()[16] ? this.staff_group_id : (java.lang.Integer) defaultValue(fields()[16]);
        record.is_deleted = fieldSetFlags()[17] ? this.is_deleted : (java.lang.Integer) defaultValue(fields()[17]);
        record.close_time = fieldSetFlags()[18] ? this.close_time : (java.lang.Long) defaultValue(fields()[18]);
        record.closer = fieldSetFlags()[19] ? this.closer : (java.lang.CharSequence) defaultValue(fields()[19]);
        record.close_type = fieldSetFlags()[20] ? this.close_type : (java.lang.Integer) defaultValue(fields()[20]);
        record.source = fieldSetFlags()[21] ? this.source : (java.lang.CharSequence) defaultValue(fields()[21]);
        record.creator = fieldSetFlags()[22] ? this.creator : (java.lang.CharSequence) defaultValue(fields()[22]);
        record.updater = fieldSetFlags()[23] ? this.updater : (java.lang.CharSequence) defaultValue(fields()[23]);
        record.create_time = fieldSetFlags()[24] ? this.create_time : (java.lang.Long) defaultValue(fields()[24]);
        record.update_time = fieldSetFlags()[25] ? this.update_time : (java.lang.Long) defaultValue(fields()[25]);
        record.should_assign_queue = fieldSetFlags()[26] ? this.should_assign_queue : (java.lang.Integer) defaultValue(fields()[26]);
        record.fact_assign_queue = fieldSetFlags()[27] ? this.fact_assign_queue : (java.lang.Integer) defaultValue(fields()[27]);
        record.agent = fieldSetFlags()[28] ? this.agent : (java.lang.CharSequence) defaultValue(fields()[28]);
        record.conversation_id = fieldSetFlags()[29] ? this.conversation_id : (java.lang.CharSequence) defaultValue(fields()[29]);
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }
}
