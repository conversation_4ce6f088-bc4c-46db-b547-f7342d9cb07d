package com.tencent.andata.common.conf;

import com.tencent.andata.utils.RainbowUtils;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.util.Preconditions;

import java.io.Serializable;

@Builder
@Data
public class MqConf implements Serializable {

    private String broker;
    private String topic;
    private String group;

    /***
     * fromRainbow .
     * @param rainbowUtils .
     * @param groupPath .
     * @return .
     */
    public static MqConf fromRainbow(RainbowUtils rainbowUtils, String groupPath) {
        String brokers = rainbowUtils.getStringValue(groupPath, "BROKERS");
        String topics = rainbowUtils.getStringValue(groupPath, "TOPICS");
        // 这里是默认的group 考虑到如果有多个消费者使用，也可以自己传一个group进来
        String group = rainbowUtils.getStringValue(groupPath, "CONSUMER_GROUP");
        Preconditions.checkNotNull(brokers);
        Preconditions.checkNotNull(topics);
        Preconditions.checkNotNull(group);

        return MqConf.builder()
                .broker(brokers)
                .topic(topics)
                .group(group)
                .build();
    }

    /***
     * fromRainbow .
     * @param rainbowUtils .
     * @param groupPath .
     * @param consumerGroup .
     * @return
     */
    public static MqConf fromRainbow(RainbowUtils rainbowUtils, String groupPath, String consumerGroup) {
        String brokers = rainbowUtils.getStringValue(groupPath, "BROKERS");
        String topics = rainbowUtils.getStringValue(groupPath, "TOPICS");
        // 这里是默认的group 考虑到如果有多个消费者使用，也可以自己传一个group进来
        String group = consumerGroup;
        if (StringUtils.isEmpty(group)) {
            group = rainbowUtils.getStringValue(groupPath, "CONSUMER_GROUP");
        }
        Preconditions.checkNotNull(brokers);
        Preconditions.checkNotNull(topics);
        Preconditions.checkNotNull(group);

        return MqConf.builder()
                .broker(brokers)
                .topic(topics)
                .group(group)
                .build();
    }

    @Override
    public String toString() {
        return String.format("%s-%s-%s", broker, topic, group);
    }
}