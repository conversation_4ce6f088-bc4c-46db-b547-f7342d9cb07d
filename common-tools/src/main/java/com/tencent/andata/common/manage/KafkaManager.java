package com.tencent.andata.common.manage;

import com.tencent.andata.common.conf.MqConf;
import lombok.AllArgsConstructor;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.Preconditions;

import java.util.Properties;

@AllArgsConstructor
public class KafkaManager<T> {

    private MqConf mqConf;
    private KafkaRecordDeserializationSchema<T> recordDeserializer;


    public static <T> Builder<T> builder() {
        return new Builder<T>();
    }

    public static class Builder<T> {
        private MqConf mqConf;
        private KafkaRecordDeserializationSchema<T> recordDeserializer;

        public Builder<T> setMqConf(MqConf mqConf) {
            this.mqConf = mqConf;
            return this;
        }

        public Builder<T> setKafkaRecordDeserializationSchema(KafkaRecordDeserializationSchema<T> recordDeserializer) {
            this.recordDeserializer = recordDeserializer;
            return this;
        }

        public KafkaManager<T> build() {
            Preconditions.checkNotNull(mqConf);
            Preconditions.checkNotNull(recordDeserializer);
            return new KafkaManager<T>(mqConf, recordDeserializer);

        }
    }

    public DataStream<T> getDataStreamSource(StreamExecutionEnvironment env, OffsetsInitializer offset) {
        return env
                .fromSource(
                        getKafkaSource(offset),
                        WatermarkStrategy.noWatermarks(),
                        getUid()
                )
                .uid(getUid())
                .name(getUid())
                .setParallelism(1);
    }

    private KafkaSource<T> getKafkaSource(OffsetsInitializer offset) {
        return KafkaSource.<T>builder()
                .setBootstrapServers(mqConf.getBroker())
                .setTopics(mqConf.getTopic())
                .setStartingOffsets(offset)
                .setGroupId(mqConf.getGroup())
                .setDeserializer(recordDeserializer)
                .setProperties(
                        new Properties() {{
                            // 在CK时Commit数据
                            setProperty("commit.offsets.on.checkpoint", "true");
                            setProperty("enable.auto.commit", "false");
                            // 只读取Kafka中已经Committed的数据
//                                    setProperty(ConsumerConfig.ISOLATION_LEVEL_CONFIG, "read_committed");
                            setProperty("fetch.max.wait.ms", "10000");
                        }}
                )
                .build();
    }

    private String getUid() {
        return String.format("kafka-source-%s", mqConf);
    }

}