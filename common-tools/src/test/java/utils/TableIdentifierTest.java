package utils;

import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.Assert;

public class TableIdentifierTest {
    @Test
    public void testTableIdentifierEquals() {
        String dbName = "testDB", schemaName = "", tableName = "testTable";
        TableIdentifier id1 = new TableIdentifier(DatabaseEnum.MYSQL, dbName, schemaName, tableName);
        TableIdentifier id2 = new TableIdentifier(DatabaseEnum.MYSQL, dbName, schemaName, tableName);
        TableIdentifier id3 = new TableIdentifier(DatabaseEnum.ICEBERG, dbName, schemaName, tableName);
        Assert.assertEquals(id1.toString(), id2.toString());
        Assert.assertEquals(id1, id2);
        Assert.assertNotSame(id2, id3);
    }

    @Test
    public void testTableIdentifierSetContain() {
        Map<TableIdentifier, String> testMap = new HashMap<>();
        String dbName = "testDB", schemaName = "", tableName = "testTable";
        TableIdentifier id1 = new TableIdentifier(DatabaseEnum.MYSQL, dbName, schemaName, tableName);
        TableIdentifier id2 = new TableIdentifier(DatabaseEnum.MYSQL, dbName, schemaName, tableName);
        testMap.put(id1, "testString");

        Assert.assertTrue(testMap.containsKey(id2));
    }
}