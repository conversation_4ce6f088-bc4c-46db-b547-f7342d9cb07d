package utils;

import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.cdc.conf.sink.SwapChain;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import org.apache.flink.table.types.logical.IntType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.types.logical.TimestampType;
import org.apache.flink.table.types.logical.VarCharType;
import org.junit.Test;
import org.junit.Assert;

public class SwapChainTest {
    @Test(expected = IndexOutOfBoundsException.class)
    public void testGetSwapChainFromRowTypeV2() {
        // 20231107新加的测试用例
        // TODO: @Baymax
        final RowType srcRowType = new RowType(
                Arrays.asList(
                        new RowType.RowField("id", new IntType(false)),
                        new RowType.RowField("name", new VarCharType(false, 255)),
                        new RowType.RowField("comment", new VarCharType(true, 1000)),
                        new RowType.RowField("status", new IntType()),
                        new RowType.RowField("default_archive", new IntType()),
                        new RowType.RowField("create_time", new TimestampType()),
                        new RowType.RowField("creator", new VarCharType(false, 64))
                )
        );

        final RowType dstRowType = new RowType(
                Arrays.asList(
                        new RowType.RowField("name", new VarCharType(false, 255)),
                        new RowType.RowField("status", new IntType()),
                        new RowType.RowField("comment", new VarCharType(true, 1000)),
                        new RowType.RowField("default_archive", new IntType())
                )
        );
        ArrayList<SwapChain> swapChains = SwapChain.getChangeRuleFromRowType(srcRowType, dstRowType);
    }

    @Test
    public void testGetSwapChainFromRowType() {
        final RowType srcRowType = new RowType(
                Arrays.asList(
                        new RowType.RowField("id", new IntType(false)),
                        new RowType.RowField("name", new VarCharType(false, 255)),
                        new RowType.RowField("comment", new VarCharType(true, 1000)),
                        new RowType.RowField("status", new IntType()),
                        new RowType.RowField("default_archive", new IntType()),
                        new RowType.RowField("create_time", new TimestampType()),
                        new RowType.RowField("creator", new VarCharType(false, 64))
                )
        );
        final RowType dstRowType = new RowType(
                Arrays.asList(
                        new RowType.RowField("id", new IntType(false)),
                        new RowType.RowField("name", new VarCharType(false, 255)),
                        new RowType.RowField("comment", new VarCharType(true, 1000)),
                        new RowType.RowField("status", new IntType())
                )
        );
        ArrayList<SwapChain> swapChains = SwapChain.getChangeRuleFromRowType(srcRowType, dstRowType);
        Assert.assertEquals(0, swapChains.size());
        final RowType dstRowTypeWithRandomFields = new RowType(
                Arrays.asList(
                        new RowType.RowField("status", new IntType()),
                        new RowType.RowField("comment", new VarCharType(true, 1000)),
                        new RowType.RowField("name", new VarCharType(false, 255)),
                        new RowType.RowField("id", new IntType(false))
                )
        );

        swapChains = SwapChain.getChangeRuleFromRowType(srcRowType, dstRowTypeWithRandomFields);
        Assert.assertEquals(2, swapChains.size());

        final RowType dstRowTypeWithExtraField = new RowType(
                Arrays.asList(
                        new RowType.RowField("id", new IntType(false)),
                        new RowType.RowField("name", new VarCharType(false, 255)),
                        new RowType.RowField("comment", new VarCharType(true, 1000)),
                        new RowType.RowField("status", new IntType()),
                        new RowType.RowField("extra_field", new IntType())
                )
        );

        Assert.assertThrows(
                AssertionError.class,
                () -> SwapChain.getChangeRuleFromRowType(srcRowType, dstRowTypeWithExtraField)
        );
    }
}