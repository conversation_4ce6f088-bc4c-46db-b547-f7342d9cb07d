package utils;

import com.tencent.andata.utils.SnowflakeDistributeId;
import java.util.HashSet;
import org.junit.Assert;
import org.junit.Test;

public class SnowflakeDistributeIdTest {

    private static final HashSet<Long> ids = new HashSet<>();
    private static final SnowflakeDistributeId idWorker = new SnowflakeDistributeId(0, 28);

    @Test
    public void testUniqIdGenerate() {
        for (long i = 0; i < 100L; i++) {
            ids.add(idWorker.nextId());
        }
        Assert.assertEquals(100, ids.size());
    }

}
