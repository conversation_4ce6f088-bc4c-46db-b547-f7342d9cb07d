package utils;

import com.tencent.andata.utils.cdc.conf.input.InputTaskParams;

import java.io.IOException;

import org.junit.Test;
import org.junit.Assert;


public class InputTaskParamsTest {
    @Test
    public void testInputParamsBuildFromCorrectJson() throws IOException, IllegalAccessException {
        final String dbName = "work", dbTableName = "t217_ticket_scenes", dstDB = "andata_dev",
                dstTable = "t217_ticket_scenes";
        String arg = "[{\"dbName\":\"%s\",\"dbTableName\":\"%s\","
                + "\"hiveName\":\"%s\",\"hiveTableName\":\"%s\","
                + "\"primaryKeys\":[\"id\"],\"dbType\":\"MYSQL\",\"sinkType\":\"ICEBERG\"}]";
        arg = String.format(arg, dbName, dbTableName, dstDB, dstTable);

        InputTaskParams[] tasks = InputTaskParams.fromJson(arg);
        InputTaskParams task = tasks[0];
        Assert.assertEquals(1, tasks.length);
        Assert.assertEquals(dbName, task.srcDBName);
        Assert.assertEquals(dbTableName, task.srcDBTableName);
        Assert.assertEquals(dstDB, task.dstDBName);
        Assert.assertEquals(dstTable, task.dstTableName);
    }

    @Test
    public void testInputParamsBuildFromMissingFieldJson() {
        final String dbName = "work", dbTableName = "t217_ticket_scenes", dstDB = "andata_dev",
                dstTable = "t217_ticket_scenes";
        final String jsonWithoutSinkTypeTemplate = "[{\"dbName\":\"%s\",\"dbTableName\":\"%s\","
                + "\"hiveName\":\"%s\",\"hiveTableName\":\"%s\","
                + "\"primaryKeys\":[\"id\"],\"dbType\":\"MYSQL\"}]";
        final String jsonWithoutSinkType = String.format(
                jsonWithoutSinkTypeTemplate, dbName, dbTableName, dstDB, dstTable);
        Assert.assertThrows(IllegalArgumentException.class, () -> InputTaskParams.fromJson(jsonWithoutSinkType));
    }
}