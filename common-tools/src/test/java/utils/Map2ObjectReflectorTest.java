package utils;

import com.tencent.andata.utils.Map2ObjectReflector;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.Assert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Map2ObjectReflectorTest {
    public static class TestClass {
        private static final Logger LOG = LoggerFactory.getLogger(Map2ObjectReflectorTest.class);
        public static Integer st1;
        public String col1;
        public Integer col2;
    }

    @Test
    public void testMap2ObjectReflectorBuild() throws Exception {
        Map<String, String> mockMap = new HashMap<>();
        mockMap.put("col1", "v1");
        mockMap.put("col2", "2");
        mockMap.put("st1", "3");
        TestClass testClass = new Map2ObjectReflector<>(TestClass.class).build(mockMap);
        Assert.assertEquals("v1", testClass.col1);
        Assert.assertEquals(2, testClass.col2.intValue());
        Assert.assertNull(TestClass.st1);
    }
}