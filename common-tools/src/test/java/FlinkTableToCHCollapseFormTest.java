import com.tencent.andata.utils.TableUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.CloseableIterator;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class FlinkTableToCHCollapseFormTest {
    Schema schema;

    private static class CollectSink implements SinkFunction<Row> {

        // must be static
        public static final List<Row> values = Collections.synchronizedList(new ArrayList<>());

        @Override
        public void invoke(Row value, SinkFunction.Context context) {
            values.add(value);
        }
    }

    @Before
    public void construct() {
        schema = Schema
                .newBuilder()
                .column("testCol", DataTypes.STRING())
                .build();
    }

    @Test
    public void testConvertNonUpsertTableToCHCollapseDS() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);
        Table tbl = tEnv.fromValues(DataTypes.ROW(DataTypes.STRING()), Row.of("testCol1"), Row.of("testCol2"));
        CollectSink collectSink = new CollectSink();
        TableUtils.convertNonUpsertTableToCHCollapseDS(tEnv, tbl, true).addSink(collectSink);
        env.execute("Test Job");

        assertTrue(
                CollectSink
                        .values
                        .stream()
                        .map(v -> v.getField(2))
                        .collect(Collectors.toList())
                        .containsAll(Arrays.asList("testCol1", "testCol2"))
        );
    }

    @Test
    public void testConvertNonUpsertTableToCHCollapseTable() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);

        tEnv.createTemporaryView(
                "test_table",
                TableUtils.convertNonUpsertTableToCHCollapseTable(tEnv, tEnv
                        .fromChangelogStream(
                                env
                                        .fromElements(
                                                Row.ofKind(RowKind.INSERT, "testCol1"),
                                                Row.ofKind(RowKind.DELETE, "testCol1"),
                                                Row.ofKind(RowKind.INSERT, "testCol2"),
                                                Row.ofKind(RowKind.UPDATE_BEFORE, "testCol2"),
                                                Row.ofKind(RowKind.UPDATE_AFTER, "testCol2")
                                        ).returns(
                                                TableSchema
                                                        .builder()
                                                        .field("testCol", DataTypes.STRING())
                                                        .build()
                                                        .toRowType()
                                        )
                        ), true)
        );
        CloseableIterator<Row> iterator = tEnv.sqlQuery("SELECT sign, testCol FROM test_table").execute().collect();
        List<String> retVal = new ArrayList<>();
        List<Integer> retSign = new ArrayList<>();

        while (iterator.hasNext()) {
            Row next = iterator.next();
            retSign.add((Integer) next.getField(0));
            retVal.add(next.getField(1).toString());
        }


        env.execute("Test Job");

        assertTrue(retVal.containsAll(Arrays.asList("testCol1", "testCol1", "testCol2", "testCol2", "testCol2")));
        assertTrue(retSign.containsAll(Arrays.asList(1, -1, 1, -1, 1)));
    }
}