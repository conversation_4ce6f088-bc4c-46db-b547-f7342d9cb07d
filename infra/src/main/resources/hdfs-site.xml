<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>

<configuration>

    <property>
        <name>dfs.nameservices</name>
        <value>sgp-tdw-1-v3</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.sgp-tdw-1-v3</name>
        <value>nn1,nn2,nn3</value>
        <description>enable HA mode, one nn is active while another is hot backup.</description>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.sgp-tdw-1-v3.nn1</name>
        <value>sgp-tdw-1-v3-nn-1.sgwoa.com:9000</value>
        <description>RPC address that handles all clients requests.</description>
    </property>

    <property>
        <name>dfs.namenode.http-address.sgp-tdw-1-v3.nn1</name>
        <value>sgp-tdw-1-v3-nn-1.sgwoa.com:8080</value>
        <description>
            The address and the base port where the dfs namenode web ui will listen on.
            If the port is 0 then the server will start on a free port.
        </description>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.sgp-tdw-1-v3.nn2</name>
        <value>sgp-tdw-1-v3-nn-2.sgwoa.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.sgp-tdw-1-v3.nn2</name>
        <value>sgp-tdw-1-v3-nn-2.sgwoa.com:8080</value>
        <description>
            The address and the base port where the dfs namenode web ui will listen on.
            If the port is 0 then the server will start on a free port.
        </description>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.sgp-tdw-1-v3.nn3</name>
        <value>sgp-tdw-1-v3-nn-3.sgwoa.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.sgp-tdw-1-v3.nn3</name>
        <value>sgp-tdw-1-v3-nn-3.sgwoa.com:8080</value>
        <description>
            The address and the base port where the dfs namenode web ui will listen on.
            If the port is 0 then the server will start on a free port.
        </description>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.sgp-tdw-1-v3</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>
</configuration>