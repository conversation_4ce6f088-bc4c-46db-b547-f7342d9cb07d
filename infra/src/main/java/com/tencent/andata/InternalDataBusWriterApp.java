package com.tencent.andata;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.extractor.InternalDBusWriterExtractor;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.dct.api.distributor.iceberg.IcebergDistributorV2;
import com.tencent.andata.dct.api.operator.process.MessageRowDataTagger;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.writer.operator.source.builder.KafkaDBusSourceBuilder;
import java.util.ArrayList;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

public class InternalDataBusWriterApp {

    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        InfraConf infraConf = new InfraConfManager(
                new InternalDBusWriterExtractor(
                        new RainbowUtils(
                                PropertyUtils.loadProperties("env.properties")
                        )
                )
        ).getInternalInfraConf();
        // KafkaSource
        DataStream<MessageRowData> srcDataStream = KafkaDBusSourceBuilder
                .getInstance()
                .setFlinkEnv(env)
                .setMQDistributeConf(infraConf.internalDBusDistributedConf)
                .build();
        IcebergDistributorV2.builder()
                .setDstDatabase(infraConf.nrtConf.odsDatabase)
                .setPatternTableMap(infraConf.internalDBusDistributedConf.patternTableMap)
                // 将数据按照DstTable打标
                .setDispatcherDataStream(
                        srcDataStream.process(
                                new MessageRowDataTagger(
                                        new ArrayList<>(infraConf.internalDBusDistributedConf.patternTableMap.keySet())
                                )
                        )
                )
                .build()
                .distribute();
        env.execute("Internal DBus Writer");
    }
}