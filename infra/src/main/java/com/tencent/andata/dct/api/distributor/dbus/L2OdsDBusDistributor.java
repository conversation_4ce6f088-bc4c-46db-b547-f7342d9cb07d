package com.tencent.andata.dct.api.distributor.dbus;

import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.conf.manager.struct.MQDistributeConf;
import com.tencent.andata.dct.api.operator.map.Convert2MessageProcess;
import com.tencent.andata.dct.api.operator.map.Convert2TableNameDataProcess;
import com.tencent.andata.dct.api.serialize.L2OdsAvroKafkaSerializationSchema;
import com.tencent.andata.struct.avro.message.Message;

import java.util.Properties;
import java.util.Random;

import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.data.RowData;
import org.apache.kafka.clients.producer.ProducerConfig;

public class L2OdsDBusDistributor extends DBusDistributor<RowData> {
    // TODO: 异常数据表从Rainbow获取
    public static final String EXCEPTION_RECORD_TABLE_NAME = "dw_abnormal_record";

    protected L2OdsDBusDistributor(MQDistributeConf dConf, DataStream<RowData> ds) {
        super(dConf, ds);
    }

    static class Builder extends DBusDistributor.Builder<RowData> {
        @Override
        public L2OdsDBusDistributor build() {
            return new L2OdsDBusDistributor(dConf, ds);
        }
    }

    @Override
    public void injectSinkToDS(Properties kafkaProperties) throws RuntimeException {
        // 精准一次需要降低Producer的超时时间，默认是1h
        // 超时时间要大于CK时间
        KafkaSink<Message> sink = KafkaSink.<Message>builder()
                .setKafkaProducerConfig(
                        kafkaProperties
                )
                .setBootstrapServers(dConf.dBusConf.dBusMQConf.entryPoint)
                .setRecordSerializer(
                        new L2OdsAvroKafkaSerializationSchema<>(Message.class, dConf.dBusConf.dBusMQConf.topic)
                )
                // 两阶段提交保证精准一次
                .setTransactionalIdPrefix(
                        String.format("AndataApiExtractorToL2OdsTxId.%d", new Random().nextInt(100000))
                )
                .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
                .build();
        ds
                .flatMap(new Convert2TableNameDataProcess(EXCEPTION_RECORD_TABLE_NAME))
                .setParallelism(4)
                // TODO: 这里后面加个KeyBy算子，保证2Message -> Sink这里有序
                .process(
                        Convert2MessageProcess
                                .builder()
                                .setDataCompress(dConf.dBusConf.payloadCompressed)
                                .setPatternTableMap(dConf.patternTableMap)
                                .build()
                )
                .setParallelism(4)
                .sinkTo(sink)
                .name(getUid())
                .uid(getUid())
                .setParallelism(1);
    }

    public static DBusDistributor.Builder<RowData> builder() {
        return new Builder();
    }

    public static DBusDistributor.Builder<RowData> buildFromInfraConf(InfraConf infraConf) {
        return new L2OdsDBusDistributor.Builder().setDistributeConf(infraConf.l2OdsMQDistributeConf);
    }
}