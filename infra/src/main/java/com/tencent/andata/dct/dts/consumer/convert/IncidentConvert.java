package com.tencent.andata.dct.dts.consumer.convert;

import com.tencent.andata.dct.dts.consumer.model.IncidentInfo;
import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class IncidentConvert implements AnsearchConvert {

    private ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public HashMap<String, Object> convertAnSearchReqBodyJson(MessageRowData rowData) throws JsonProcessingException {
        IncidentInfo incidentInfo = IncidentInfo.constructFromRowData(rowData);
        if (incidentInfo == null) {
            return null;
        }
        HashMap<String, Object> data = objectMapper.convertValue(
                incidentInfo,
                new TypeReference<HashMap<String, Object>>() {}
        );
        data.put("source_name", "incident");

        HashMap<String, Object> reqBody = new HashMap<String, Object>() {{
            put("id", "angpt-incident-" + String.valueOf(incidentInfo.getId()));
            put("doc", incidentInfo.getContent());
            put("deleted", incidentInfo.getIsDeleted());
            put("create_time", incidentInfo.getCreateTime() / 1000);
            put("fields", data);
        }};

        return reqBody;
    }
}
