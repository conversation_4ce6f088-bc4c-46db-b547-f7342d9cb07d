package com.tencent.andata.dct.api.model;

import com.tencent.andata.utils.DateFormatUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;

public class AbnormalRecord {

    private String createTime;
    private String mqMsgTopic;
    private String tableName;
    private String message;
    private String errorInfo;
    private String dwLevel;
    private ObjectMapper objectMapper;

    /**
     * 根据RowData初始化异常表
     *
     * @param messageRowData 实际数据
     * @param errorInfo      异常信息
     * @param dwLevel        异常等级
     */
    public AbnormalRecord(RowData messageRowData, String errorInfo, String dwLevel) {
        createTime = DateFormatUtils.cstTimestampToUTCString(System.currentTimeMillis());
        this.dwLevel = dwLevel;
        this.errorInfo = errorInfo;
        initFromRowData(messageRowData);
        objectMapper = new ObjectMapper();
    }

    public AbnormalRecord(RowData messageRowData, String errorInfo, String dwLevel, String tableName) {
        createTime = DateFormatUtils.cstTimestampToUTCString(System.currentTimeMillis());
        this.dwLevel = dwLevel;
        this.errorInfo = errorInfo;
        this.tableName = tableName;
        initFromRowData(messageRowData);
        objectMapper = new ObjectMapper();
    }

    /**
     * 从数据初始化异常信息
     *
     * @param messageRowData 实际数据
     */
    public void initFromRowData(RowData messageRowData) {
        StringData tableNameData = messageRowData.getString(0);
        if (tableNameData == null) {
            tableName = "";
        } else {
            tableName = tableNameData.toString();
        }
        mqMsgTopic = messageRowData.getString(1).toString();
        message = messageRowData.getString(5).toString();
    }

    /**
     * 将异常信息转化成JSON对象
     *
     * @return JSON对象
     */
    public ObjectNode toObjectNode() {
        ObjectNode result = objectMapper.createObjectNode();
        result.put("create_time", createTime);
        result.put("mq_msg_topic", mqMsgTopic);
        result.put("table_name", tableName);
        result.put("message", message);
        result.put("error_info", errorInfo);
        result.put("dw_level", dwLevel);
        return result;
    }

    @Override
    public String toString() {
        return "AbnormalRecord{"
                + "createTime='"
                + createTime
                + '\''
                + ", mqMsgTopic='"
                + mqMsgTopic
                + '\''
                + ", tableName='"
                + tableName
                + '\''
                + ", errorInfo='"
                + errorInfo
                + '\''
                + ", dwLevel='"
                + dwLevel
                + '\''
                + ", message='"
                + message
                + '\''
                + '}';
    }

    public String getCreateTime() {
        return createTime;
    }

    public String getMqMsgTopic() {
        return mqMsgTopic;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getMessage() {
        return message;
    }

    public String getErrorInfo() {
        return errorInfo;
    }

    public String getDwLevel() {
        return dwLevel;
    }
}