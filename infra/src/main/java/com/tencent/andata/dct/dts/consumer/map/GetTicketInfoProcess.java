package com.tencent.andata.dct.dts.consumer.map;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.LogicalTypeRoot;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;

// 返回工单info的row
public class GetTicketInfoProcess extends ProcessFunction<Row, Row> {

    public static ObjectMapper mapper = new ObjectMapper();
    private static final Logger log = LoggerFactory.getLogger(GetTicketInfoProcess.class);
    private RowType rowType;

    public GetTicketInfoProcess(RowType rowType) {
        this.rowType = rowType;
    }

    @Override
    public void processElement(
            Row row,
            ProcessFunction<Row, Row>.Context context,
            Collector<Row> collector) throws Exception {
        ArrayNode ticketInfo = mapper.readValue(row.<String>getFieldAs("ticket_info"), ArrayNode.class);
        if (ticketInfo.isEmpty()) {
            return;
        }
        JsonNode jsonNode = ticketInfo.get(0);
        Iterator<String> fieldNames = jsonNode.fieldNames();
        Row newRow = Row.withNames();
        while (fieldNames.hasNext()) {
            String name = fieldNames.next();
            JsonNode node = jsonNode.get(name);
            if (node.isTextual()) {
                newRow.setField(name, node.asText());
            } else if (node.isInt()) {
                LogicalType typeAt = rowType.getTypeAt(rowType.getFieldIndex(name));
                if (typeAt.getTypeRoot() == LogicalTypeRoot.BIGINT) {
                    newRow.setField(name, (long) node.asInt());
                } else {
                    newRow.setField(name, node.asInt());
                }
            } else if (node.isLong()) {
                newRow.setField(name, node.asLong());
            }
        }
        log.info("从hbase中捞出来的工单row: {}", newRow);
        collector.collect(newRow);
    }
}

