package com.tencent.andata.dct.dts.consumer.map;

import com.google.common.collect.Lists;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;

public class TCAndonTicketWindowProcess extends ProcessWindowFunction<Row, Row, String, TimeWindow> {

    public static Logger log = LoggerFactory.getLogger(TCAndonTicketWindowProcess.class);
    private String orderField;

    public TCAndonTicketWindowProcess(String orderField) {
        this.orderField = orderField;
    }

    @Override
    public void process(String key,
                        ProcessWindowFunction<Row, Row, String, TimeWindow>.Context context,
                        Iterable<Row> iterable,
                        Collector<Row> collector) throws Exception {
        ArrayList<Row> rows = Lists.newArrayList(iterable);
        Collections.sort(rows, new Comparator<Row>() {
            @Override
            public int compare(Row o1, Row o2) {
                long time1 = Long.parseLong(o1.getField(orderField).toString());
                long time2 =  Long.parseLong(o2.getField(orderField).toString());
                return Long.compare(time1, time2);
            }
        });
        // 下发一条就好了，下游需要从hbase补充数据
        Row row = rows.get(rows.size() - 1);
        log.info("窗口下发的row: {}", row);
        collector.collect(row);
    }
}
