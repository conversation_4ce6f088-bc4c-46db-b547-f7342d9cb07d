package com.tencent.andata.dct.dts.consumer.map;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;

// 接受工单数据，去hbase中捞流水然后拼接
public class TicketFlowGroupProcess extends ProcessFunction<Row, HashMap<String, Object>> {

    public static ObjectMapper mapper = new ObjectMapper();
    public static Logger log = LoggerFactory.getLogger(TicketFlowGroupProcess.class);
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    @Override
    public void processElement(Row row,
                               ProcessFunction<Row, HashMap<String, Object>>.Context context,
                               Collector<HashMap<String, Object>> collector) throws Exception {
        ArrayNode operationData = mapper.readValue(row.<String>getFieldAs("operation_data"), ArrayNode.class);
        log.info("工单数据：{}", row);
        log.info("捞出来的流水data: {}", operationData);
        // row 是工单数据
        // operationData 是对应的所有流水数据
        HashMap<String, Object> data = new HashMap<>();
        data.put("id", row.getField("incident_id").toString());
        data.put("title", row.getField("title").toString());
        data.put("description", row.getField("content").toString());
        data.put("create_time", sdf.format(row.getField("create_time")));
        data.put("update_time", sdf.format(System.currentTimeMillis()));
        // 工单中其他字段
        HashMap<String, Object> incidentFieldMap = new HashMap<>();
        Set<String> fieldNames = row.getFieldNames(true);
        for (String fieldName : fieldNames) {
            if (fieldName.equals("rowkey") || fieldName.equals("operation_data")) {
                continue;
            }
            incidentFieldMap.put(fieldName, row.getField(fieldName));
        }
        // 流水表中其他字段 暂时不需要
        HashMap<String, Object> operationsFieldMap = new HashMap<>();
        data.put("incident", incidentFieldMap);
        data.put("operations", operationsFieldMap);
        // 拼接content
        StringBuilder builder = new StringBuilder();
        Object extFields = row.getField("ext_fields");
        if (extFields != null) {
            JsonNode extFieldsJson = mapper.readTree(extFields.toString());
            Iterator<String> extNames = extFieldsJson.fieldNames();
            while (extNames.hasNext()) {
                builder.append(extFieldsJson.get(extNames.next()).asText());
                builder.append(" ");
            }
            builder.append("\n");
        }
        if (operationData != null && !operationData.isEmpty()) {
            for (JsonNode flowNode : operationData) {
                String flowData = extractStrFromFlow(flowNode);
                if (StringUtils.isNotEmpty(flowData)) {
                    builder.append(flowData);
                    builder.append("\n");
                }
            }
        }
        data.put("content", builder.toString());
        collector.collect(data);
    }


    private String extractStrFromFlow(JsonNode flowNode) throws JsonProcessingException {
        int operationType = flowNode.get("operation_type").asInt();
        JsonNode params = null;

        switch (operationType) {
            case 4:
            case 8:
                params = mapper.readTree(flowNode.get("params").asText());
                return params.get("Progress") == null ? "" : params.get("Progress").asText();
            case 5:
                params = mapper.readTree(flowNode.get("params").asText());
                return params.get("Solution") == null ? "" : params.get("Solution").asText();
            case 6:
                params = mapper.readTree(flowNode.get("params").asText());
                return params.get("ServiceComment") == null ? "" : params.get("ServiceComment").asText();
            case 7:
                params = mapper.readTree(flowNode.get("params").asText());
                return params.get("RefuseReason") == null ? "" : params.get("RefuseReason").asText();
            case 10:
            case 12:
            case 14:
                params = mapper.readTree(flowNode.get("params").asText());
                return params.get("Comment") == null ? "" : params.get("Comment").asText();
            default:
                return "";
        }
    }
}
