package com.tencent.andata.dct.api.source.kafka.deserializer;

import com.tencent.andata.utils.DateFormatUtils;
import com.tencent.andata.utils.JSONUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.connectors.kafka.KafkaDeserializationSchema;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FlinkKafkaDeserializationSchema implements KafkaDeserializationSchema<String> {
    private final JSONUtils jsonUtils;

    public FlinkKafkaDeserializationSchema() {
        jsonUtils = new JSONUtils();
    }

    @Override
    public boolean isEndOfStream(String s) {
        return false;
    }

    @Override
    public String deserialize(ConsumerRecord<byte[], byte[]> consumerRecord) throws Exception {
        String value = new String(consumerRecord.value(), StandardCharsets.UTF_8);
        // 解析消息队列相关元数据
        String topic = consumerRecord.topic();
        long offset = consumerRecord.offset();
        long publishTimestamp = consumerRecord.timestamp();
        String publishTimeString = DateFormatUtils.cstTimestampToUTCString(publishTimestamp);
        String currentTimeString = DateFormatUtils.cstTimestampToUTCString(System.currentTimeMillis());
        ObjectNode obj = jsonUtils.createObjectNode();
        obj.put("mq_msg_id", offset);
        obj.put("message", value);
        obj.put("mq_msg_publish_time", publishTimeString);
        obj.put("ods_create_time", currentTimeString);
        obj.put("mq_msg_topic", topic);
        // 解析table_name
        try {
            ArrayList<ObjectNode> valueObjList = jsonUtils.getJSONObjectArrayByString(value);
            if (valueObjList.size() != 0) {
                ObjectNode valueObj = valueObjList.get(0);
                JsonNode tableField =
                        ObjectUtils.firstNonNull(valueObj.get("table_name"), valueObj.get("table"));
                obj.put("dst_table_name", tableField != null ? tableField.asText() : null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 数据解析失败，后续落库异常表
            obj.set("dst_table_name", null);
        }
        return obj.toString();
    }

    @Override
    public TypeInformation<String> getProducedType() {
        return BasicTypeInfo.STRING_TYPE_INFO;
    }
}