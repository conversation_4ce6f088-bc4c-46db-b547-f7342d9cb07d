package com.tencent.andata.dct.dts.consumer.sink;

import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.TableUtils;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.List;

import static com.tencent.andata.utils.TableUtils.row2Json;
import static com.tencent.andata.utils.TableUtils.sinkToHbase;

public class HbaseSink {

    private final String srcHTblName;
    private final String fHTblName;
    List<String> pkList;

    public HbaseSink(String srcHTblName, String fHtblName, List<String> pkList) {
        this.srcHTblName = srcHTblName;
        this.fHTblName = fHtblName;
        this.pkList = pkList;
    }

    public void sink(FlinkEnvUtils.FlinkEnv flinkEnv, Table tbl) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // 注册hbaseTable
        TableUtils.hbaseTable2FlinkTable(this.fHTblName, this.srcHTblName, "cf", tEnv);
        String rowKey = String.join(" || '-' || ", this.pkList);
        tEnv.createTemporaryView("source_view_" + this.fHTblName, tbl);
        Table hTbl = tEnv.sqlQuery(row2Json(tbl, rowKey, "source_view_" + this.fHTblName));
        StatementSet stmtSet = flinkEnv.stmtSet();
        tEnv.createTemporaryView("h_view" + this.fHTblName, hTbl);
        stmtSet.addInsertSql(sinkToHbase("h_view" + this.fHTblName, fHTblName));
    }

}
