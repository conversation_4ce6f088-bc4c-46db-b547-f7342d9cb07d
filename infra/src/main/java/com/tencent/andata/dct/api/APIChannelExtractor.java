package com.tencent.andata.dct.api;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.dct.api.distributor.dbus.L1OdsDBusDistributor;
import com.tencent.andata.dct.api.distributor.dbus.L2OdsDBusDistributor;
import com.tencent.andata.dct.api.manager.SourceStreamBuilderManager;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.data.RowData;

public class APIChannelExtractor {
    /**
     * 具体Flink执行
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        InfraConf infraConf = new InfraConfManager(
                ExtractorFactory.fromProperties("env.properties")
        ).getInfraConf();
        addTaskToEnv(env, infraConf);
        setCKConf(env);
        env.execute("Kafka data to ODS iceberg table");
    }

    public static void setCKConf(StreamExecutionEnvironment env) {
        env.enableCheckpointing(10 * 1000);
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setMinPauseBetweenCheckpoints(2 * 60 * 1000L);
        checkpointConfig.setMaxConcurrentCheckpoints(1);
        checkpointConfig.setCheckpointTimeout(60 * 1000L);
        checkpointConfig.setTolerableCheckpointFailureNumber(5);
    }

    /**
     * addTask
     *
     * @param env
     * @throws Exception
     */
    public static void addTaskToEnv(StreamExecutionEnvironment env, InfraConf infraConf) throws Exception {
        DataStream<RowData> odsDistributionDataStream = SourceStreamBuilderManager
                .builder()
                .env(env)
                .infraConf(infraConf)
                .build()
                .unionStreams();
        // L1 ODS Kafka 入库
        L1OdsDBusDistributor
                .buildFromInfraConf(infraConf)
                .setDs(odsDistributionDataStream)
                .build()
                .distribute();
        // ------------------------ L2 ODS ----------------------------
        L2OdsDBusDistributor
                .buildFromInfraConf(infraConf)
                .setDs(odsDistributionDataStream)
                .build()
                .distribute();
    }
}