package com.tencent.andata.dct.api.source.pulsar.source;

import com.tencent.andata.utils.DateFormatUtils;
import com.tencent.andata.utils.JSONUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.table.data.RowData;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.PulsarClientException;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

public class ColdDataStoragePulsarSource extends BasePulsarSource {

    private JSONUtils jsonUtils;

    public ColdDataStoragePulsarSource(DeserializationSchema<RowData> deserializer)
            throws PulsarClientException {
        super(deserializer);
        jsonUtils = new JSONUtils();
    }

    /**
     * 数据处理
     */
    @Override
    public void run(SourceContext<RowData> sourceContext) throws IOException {
        final Object lock = sourceContext.getCheckpointLock();

        running = true;

        pulsarConsumer = buildPulsarConsumer();

        LOG.info("Pulsar Cold Data Storage Consumer Running.");

        while (running) {
            Message<byte[]> msg = pulsarConsumer.receive(3, TimeUnit.SECONDS);
            if (msg != null) {
                String msgValue =
                        new String(msg.getValue(), StandardCharsets.UTF_8);
                ObjectNode data = jsonUtils.createObjectNode();
                try {
                    // 获取表名
                    ObjectNode valueObj = jsonUtils.getJSONObjectArrayByString(msgValue).get(0);
                    JsonNode tableField =
                            ObjectUtils.firstNonNull(valueObj.get("table_name"), valueObj.get("table"));
                    data.put("dst_table_name", tableField != null ? tableField.asText() : null);
                } catch (Exception e) {
                    e.printStackTrace();
                    data.set("dst_table_name", null);
                }
                // 获取Topic
                data.put("mq_msg_topic", msg.getTopicName());
                // 获取Offset
                data.put("mq_msg_id", msg.getMessageId().toString());
                // 获取消息队列时间
                data.put(
                        "mq_msg_publish_time", DateFormatUtils.cstTimestampToUTCString(msg.getPublishTime()));
                // 获取创建时间
                data.put(
                        "ods_create_time", DateFormatUtils.cstTimestampToUTCString(System.currentTimeMillis()));
                // 获取数据
                data.put("message", msgValue);
                RowData msgRowData = deserializer.deserialize(data.toString().getBytes(StandardCharsets.UTF_8));
                synchronized (lock) {
                    // 反序列化 & 数据发往下游
                    sourceContext.collect(msgRowData);
                    // 维护offset
                    currentOffset = msg.getMessageId();
                }
            }
        }

        LOG.info("Pulsar Cold Data Storage Consumer Exit.");
    }
}