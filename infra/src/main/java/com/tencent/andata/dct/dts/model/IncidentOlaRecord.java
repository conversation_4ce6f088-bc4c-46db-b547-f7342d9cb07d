/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */

package com.tencent.andata.dct.dts.model;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class IncidentOlaRecord extends org.apache.avro.specific.SpecificRecordBase implements
        org.apache.avro.specific.SpecificRecord {

    public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse(
            "{\"type\":\"record\",\"name\":\"IncidentOlaRecord\",\"namespace\":\"com.tencent.andata.dct.dts.model\",\"fields\":[{\"name\":\"id\",\"type\":\"long\"},{\"name\":\"tenant_id\",\"type\":\"long\"},{\"name\":\"incident_id\",\"type\":\"string\"},{\"name\":\"alarm_type\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"alarm_level\",\"type\":\"int\"},{\"name\":\"urge_times\",\"type\":\"long\"},{\"name\":\"create_time\",\"type\":\"long\"},{\"name\":\"alarm_receiver\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"ticket\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"alarm_title\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"next_receiver\",\"type\":[\"null\",\"string\"],\"default\":null}]}");
    @Deprecated
    public long id;
    @Deprecated
    public long tenant_id;
    @Deprecated
    public java.lang.CharSequence incident_id;
    @Deprecated
    public java.lang.Integer alarm_type;
    @Deprecated
    public int alarm_level;
    @Deprecated
    public long urge_times;
    @Deprecated
    public long create_time;
    @Deprecated
    public java.lang.CharSequence alarm_receiver;
    @Deprecated
    public java.lang.CharSequence ticket;
    @Deprecated
    public java.lang.CharSequence alarm_title;
    @Deprecated
    public java.lang.CharSequence next_receiver;
    /**
     * Default constructor.
     */
    public IncidentOlaRecord() {
    }

    /**
     * All-args constructor.
     */
    public IncidentOlaRecord(java.lang.Long id, java.lang.Long tenant_id, java.lang.CharSequence incident_id,
            java.lang.Integer alarm_type, java.lang.Integer alarm_level, java.lang.Long urge_times,
            java.lang.Long create_time, java.lang.CharSequence alarm_receiver, java.lang.CharSequence ticket,
            java.lang.CharSequence alarm_title, java.lang.CharSequence next_receiver) {
        this.id = id;
        this.tenant_id = tenant_id;
        this.incident_id = incident_id;
        this.alarm_type = alarm_type;
        this.alarm_level = alarm_level;
        this.urge_times = urge_times;
        this.create_time = create_time;
        this.alarm_receiver = alarm_receiver;
        this.ticket = ticket;
        this.alarm_title = alarm_title;
        this.next_receiver = next_receiver;
    }

    public static org.apache.avro.Schema getClassSchema() {
        return SCHEMA$;
    }

    /** Creates a new IncidentOlaRecord RecordBuilder */
    public static com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder newBuilder() {
        return new com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder();
    }

    /** Creates a new IncidentOlaRecord RecordBuilder by copying an existing Builder */
    public static com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder newBuilder(
            com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder other) {
        return new com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder(other);
    }

    /** Creates a new IncidentOlaRecord RecordBuilder by copying an existing IncidentOlaRecord instance */
    public static com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder newBuilder(
            com.tencent.andata.dct.dts.model.IncidentOlaRecord other) {
        return new com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder(other);
    }

    public org.apache.avro.Schema getSchema() {
        return SCHEMA$;
    }

    // Used by DatumWriter.  Applications should not call.
    public java.lang.Object get(int field$) {
        switch (field$) {
            case 0:
                return id;
            case 1:
                return tenant_id;
            case 2:
                return incident_id;
            case 3:
                return alarm_type;
            case 4:
                return alarm_level;
            case 5:
                return urge_times;
            case 6:
                return create_time;
            case 7:
                return alarm_receiver;
            case 8:
                return ticket;
            case 9:
                return alarm_title;
            case 10:
                return next_receiver;
            default:
                throw new org.apache.avro.AvroRuntimeException("Bad index");
        }
    }

    // Used by DatumReader.  Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int field$, java.lang.Object value$) {
        switch (field$) {
            case 0:
                id = (java.lang.Long) value$;
                break;
            case 1:
                tenant_id = (java.lang.Long) value$;
                break;
            case 2:
                incident_id = (java.lang.CharSequence) value$;
                break;
            case 3:
                alarm_type = (java.lang.Integer) value$;
                break;
            case 4:
                alarm_level = (java.lang.Integer) value$;
                break;
            case 5:
                urge_times = (java.lang.Long) value$;
                break;
            case 6:
                create_time = (java.lang.Long) value$;
                break;
            case 7:
                alarm_receiver = (java.lang.CharSequence) value$;
                break;
            case 8:
                ticket = (java.lang.CharSequence) value$;
                break;
            case 9:
                alarm_title = (java.lang.CharSequence) value$;
                break;
            case 10:
                next_receiver = (java.lang.CharSequence) value$;
                break;
            default:
                throw new org.apache.avro.AvroRuntimeException("Bad index");
        }
    }

    /**
     * Gets the value of the 'id' field.
     */
    public java.lang.Long getId() {
        return id;
    }

    /**
     * Sets the value of the 'id' field.
     * @param value the value to set.
     */
    public void setId(java.lang.Long value) {
        this.id = value;
    }

    /**
     * Gets the value of the 'tenant_id' field.
     */
    public java.lang.Long getTenantId() {
        return tenant_id;
    }

    /**
     * Sets the value of the 'tenant_id' field.
     * @param value the value to set.
     */
    public void setTenantId(java.lang.Long value) {
        this.tenant_id = value;
    }

    /**
     * Gets the value of the 'incident_id' field.
     */
    public java.lang.CharSequence getIncidentId() {
        return incident_id;
    }

    /**
     * Sets the value of the 'incident_id' field.
     * @param value the value to set.
     */
    public void setIncidentId(java.lang.CharSequence value) {
        this.incident_id = value;
    }

    /**
     * Gets the value of the 'alarm_type' field.
     */
    public java.lang.Integer getAlarmType() {
        return alarm_type;
    }

    /**
     * Sets the value of the 'alarm_type' field.
     * @param value the value to set.
     */
    public void setAlarmType(java.lang.Integer value) {
        this.alarm_type = value;
    }

    /**
     * Gets the value of the 'alarm_level' field.
     */
    public java.lang.Integer getAlarmLevel() {
        return alarm_level;
    }

    /**
     * Sets the value of the 'alarm_level' field.
     * @param value the value to set.
     */
    public void setAlarmLevel(java.lang.Integer value) {
        this.alarm_level = value;
    }

    /**
     * Gets the value of the 'urge_times' field.
     */
    public java.lang.Long getUrgeTimes() {
        return urge_times;
    }

    /**
     * Sets the value of the 'urge_times' field.
     * @param value the value to set.
     */
    public void setUrgeTimes(java.lang.Long value) {
        this.urge_times = value;
    }

    /**
     * Gets the value of the 'create_time' field.
     */
    public java.lang.Long getCreateTime() {
        return create_time;
    }

    /**
     * Sets the value of the 'create_time' field.
     * @param value the value to set.
     */
    public void setCreateTime(java.lang.Long value) {
        this.create_time = value;
    }

    /**
     * Gets the value of the 'alarm_receiver' field.
     */
    public java.lang.CharSequence getAlarmReceiver() {
        return alarm_receiver;
    }

    /**
     * Sets the value of the 'alarm_receiver' field.
     * @param value the value to set.
     */
    public void setAlarmReceiver(java.lang.CharSequence value) {
        this.alarm_receiver = value;
    }

    /**
     * Gets the value of the 'ticket' field.
     */
    public java.lang.CharSequence getTicket() {
        return ticket;
    }

    /**
     * Sets the value of the 'ticket' field.
     * @param value the value to set.
     */
    public void setTicket(java.lang.CharSequence value) {
        this.ticket = value;
    }

    /**
     * Gets the value of the 'alarm_title' field.
     */
    public java.lang.CharSequence getAlarmTitle() {
        return alarm_title;
    }

    /**
     * Sets the value of the 'alarm_title' field.
     * @param value the value to set.
     */
    public void setAlarmTitle(java.lang.CharSequence value) {
        this.alarm_title = value;
    }

    /**
     * Gets the value of the 'next_receiver' field.
     */
    public java.lang.CharSequence getNextReceiver() {
        return next_receiver;
    }

    /**
     * Sets the value of the 'next_receiver' field.
     * @param value the value to set.
     */
    public void setNextReceiver(java.lang.CharSequence value) {
        this.next_receiver = value;
    }

    /**
     * RecordBuilder for IncidentOlaRecord instances.
     */
    public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<IncidentOlaRecord>
            implements org.apache.avro.data.RecordBuilder<IncidentOlaRecord> {

        private long id;
        private long tenant_id;
        private java.lang.CharSequence incident_id;
        private java.lang.Integer alarm_type;
        private int alarm_level;
        private long urge_times;
        private long create_time;
        private java.lang.CharSequence alarm_receiver;
        private java.lang.CharSequence ticket;
        private java.lang.CharSequence alarm_title;
        private java.lang.CharSequence next_receiver;

        /** Creates a new Builder */
        private Builder() {
            super(com.tencent.andata.dct.dts.model.IncidentOlaRecord.SCHEMA$);
        }

        /** Creates a Builder by copying an existing Builder */
        private Builder(com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder other) {
            super(other);
        }

        /** Creates a Builder by copying an existing IncidentOlaRecord instance */
        private Builder(com.tencent.andata.dct.dts.model.IncidentOlaRecord other) {
            super(com.tencent.andata.dct.dts.model.IncidentOlaRecord.SCHEMA$);
            if (isValidValue(fields()[0], other.id)) {
                this.id = data().deepCopy(fields()[0].schema(), other.id);
                fieldSetFlags()[0] = true;
            }
            if (isValidValue(fields()[1], other.tenant_id)) {
                this.tenant_id = data().deepCopy(fields()[1].schema(), other.tenant_id);
                fieldSetFlags()[1] = true;
            }
            if (isValidValue(fields()[2], other.incident_id)) {
                this.incident_id = data().deepCopy(fields()[2].schema(), other.incident_id);
                fieldSetFlags()[2] = true;
            }
            if (isValidValue(fields()[3], other.alarm_type)) {
                this.alarm_type = data().deepCopy(fields()[3].schema(), other.alarm_type);
                fieldSetFlags()[3] = true;
            }
            if (isValidValue(fields()[4], other.alarm_level)) {
                this.alarm_level = data().deepCopy(fields()[4].schema(), other.alarm_level);
                fieldSetFlags()[4] = true;
            }
            if (isValidValue(fields()[5], other.urge_times)) {
                this.urge_times = data().deepCopy(fields()[5].schema(), other.urge_times);
                fieldSetFlags()[5] = true;
            }
            if (isValidValue(fields()[6], other.create_time)) {
                this.create_time = data().deepCopy(fields()[6].schema(), other.create_time);
                fieldSetFlags()[6] = true;
            }
            if (isValidValue(fields()[7], other.alarm_receiver)) {
                this.alarm_receiver = data().deepCopy(fields()[7].schema(), other.alarm_receiver);
                fieldSetFlags()[7] = true;
            }
            if (isValidValue(fields()[8], other.ticket)) {
                this.ticket = data().deepCopy(fields()[8].schema(), other.ticket);
                fieldSetFlags()[8] = true;
            }
            if (isValidValue(fields()[9], other.alarm_title)) {
                this.alarm_title = data().deepCopy(fields()[9].schema(), other.alarm_title);
                fieldSetFlags()[9] = true;
            }
            if (isValidValue(fields()[10], other.next_receiver)) {
                this.next_receiver = data().deepCopy(fields()[10].schema(), other.next_receiver);
                fieldSetFlags()[10] = true;
            }
        }

        /** Gets the value of the 'id' field */
        public java.lang.Long getId() {
            return id;
        }

        /** Sets the value of the 'id' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setId(long value) {
            validate(fields()[0], value);
            this.id = value;
            fieldSetFlags()[0] = true;
            return this;
        }

        /** Checks whether the 'id' field has been set */
        public boolean hasId() {
            return fieldSetFlags()[0];
        }

        /** Clears the value of the 'id' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearId() {
            fieldSetFlags()[0] = false;
            return this;
        }

        /** Gets the value of the 'tenant_id' field */
        public java.lang.Long getTenantId() {
            return tenant_id;
        }

        /** Sets the value of the 'tenant_id' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setTenantId(long value) {
            validate(fields()[1], value);
            this.tenant_id = value;
            fieldSetFlags()[1] = true;
            return this;
        }

        /** Checks whether the 'tenant_id' field has been set */
        public boolean hasTenantId() {
            return fieldSetFlags()[1];
        }

        /** Clears the value of the 'tenant_id' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearTenantId() {
            fieldSetFlags()[1] = false;
            return this;
        }

        /** Gets the value of the 'incident_id' field */
        public java.lang.CharSequence getIncidentId() {
            return incident_id;
        }

        /** Sets the value of the 'incident_id' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setIncidentId(java.lang.CharSequence value) {
            validate(fields()[2], value);
            this.incident_id = value;
            fieldSetFlags()[2] = true;
            return this;
        }

        /** Checks whether the 'incident_id' field has been set */
        public boolean hasIncidentId() {
            return fieldSetFlags()[2];
        }

        /** Clears the value of the 'incident_id' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearIncidentId() {
            incident_id = null;
            fieldSetFlags()[2] = false;
            return this;
        }

        /** Gets the value of the 'alarm_type' field */
        public java.lang.Integer getAlarmType() {
            return alarm_type;
        }

        /** Sets the value of the 'alarm_type' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setAlarmType(java.lang.Integer value) {
            validate(fields()[3], value);
            this.alarm_type = value;
            fieldSetFlags()[3] = true;
            return this;
        }

        /** Checks whether the 'alarm_type' field has been set */
        public boolean hasAlarmType() {
            return fieldSetFlags()[3];
        }

        /** Clears the value of the 'alarm_type' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearAlarmType() {
            alarm_type = null;
            fieldSetFlags()[3] = false;
            return this;
        }

        /** Gets the value of the 'alarm_level' field */
        public java.lang.Integer getAlarmLevel() {
            return alarm_level;
        }

        /** Sets the value of the 'alarm_level' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setAlarmLevel(int value) {
            validate(fields()[4], value);
            this.alarm_level = value;
            fieldSetFlags()[4] = true;
            return this;
        }

        /** Checks whether the 'alarm_level' field has been set */
        public boolean hasAlarmLevel() {
            return fieldSetFlags()[4];
        }

        /** Clears the value of the 'alarm_level' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearAlarmLevel() {
            fieldSetFlags()[4] = false;
            return this;
        }

        /** Gets the value of the 'urge_times' field */
        public java.lang.Long getUrgeTimes() {
            return urge_times;
        }

        /** Sets the value of the 'urge_times' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setUrgeTimes(long value) {
            validate(fields()[5], value);
            this.urge_times = value;
            fieldSetFlags()[5] = true;
            return this;
        }

        /** Checks whether the 'urge_times' field has been set */
        public boolean hasUrgeTimes() {
            return fieldSetFlags()[5];
        }

        /** Clears the value of the 'urge_times' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearUrgeTimes() {
            fieldSetFlags()[5] = false;
            return this;
        }

        /** Gets the value of the 'create_time' field */
        public java.lang.Long getCreateTime() {
            return create_time;
        }

        /** Sets the value of the 'create_time' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setCreateTime(long value) {
            validate(fields()[6], value);
            this.create_time = value;
            fieldSetFlags()[6] = true;
            return this;
        }

        /** Checks whether the 'create_time' field has been set */
        public boolean hasCreateTime() {
            return fieldSetFlags()[6];
        }

        /** Clears the value of the 'create_time' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearCreateTime() {
            fieldSetFlags()[6] = false;
            return this;
        }

        /** Gets the value of the 'alarm_receiver' field */
        public java.lang.CharSequence getAlarmReceiver() {
            return alarm_receiver;
        }

        /** Sets the value of the 'alarm_receiver' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setAlarmReceiver(
                java.lang.CharSequence value) {
            validate(fields()[7], value);
            this.alarm_receiver = value;
            fieldSetFlags()[7] = true;
            return this;
        }

        /** Checks whether the 'alarm_receiver' field has been set */
        public boolean hasAlarmReceiver() {
            return fieldSetFlags()[7];
        }

        /** Clears the value of the 'alarm_receiver' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearAlarmReceiver() {
            alarm_receiver = null;
            fieldSetFlags()[7] = false;
            return this;
        }

        /** Gets the value of the 'ticket' field */
        public java.lang.CharSequence getTicket() {
            return ticket;
        }

        /** Sets the value of the 'ticket' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setTicket(java.lang.CharSequence value) {
            validate(fields()[8], value);
            this.ticket = value;
            fieldSetFlags()[8] = true;
            return this;
        }

        /** Checks whether the 'ticket' field has been set */
        public boolean hasTicket() {
            return fieldSetFlags()[8];
        }

        /** Clears the value of the 'ticket' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearTicket() {
            ticket = null;
            fieldSetFlags()[8] = false;
            return this;
        }

        /** Gets the value of the 'alarm_title' field */
        public java.lang.CharSequence getAlarmTitle() {
            return alarm_title;
        }

        /** Sets the value of the 'alarm_title' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setAlarmTitle(java.lang.CharSequence value) {
            validate(fields()[9], value);
            this.alarm_title = value;
            fieldSetFlags()[9] = true;
            return this;
        }

        /** Checks whether the 'alarm_title' field has been set */
        public boolean hasAlarmTitle() {
            return fieldSetFlags()[9];
        }

        /** Clears the value of the 'alarm_title' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearAlarmTitle() {
            alarm_title = null;
            fieldSetFlags()[9] = false;
            return this;
        }

        /** Gets the value of the 'next_receiver' field */
        public java.lang.CharSequence getNextReceiver() {
            return next_receiver;
        }

        /** Sets the value of the 'next_receiver' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder setNextReceiver(
                java.lang.CharSequence value) {
            validate(fields()[10], value);
            this.next_receiver = value;
            fieldSetFlags()[10] = true;
            return this;
        }

        /** Checks whether the 'next_receiver' field has been set */
        public boolean hasNextReceiver() {
            return fieldSetFlags()[10];
        }

        /** Clears the value of the 'next_receiver' field */
        public com.tencent.andata.dct.dts.model.IncidentOlaRecord.Builder clearNextReceiver() {
            next_receiver = null;
            fieldSetFlags()[10] = false;
            return this;
        }

        @Override
        public IncidentOlaRecord build() {
            try {
                IncidentOlaRecord record = new IncidentOlaRecord();
                record.id = fieldSetFlags()[0] ? this.id : (java.lang.Long) defaultValue(fields()[0]);
                record.tenant_id = fieldSetFlags()[1] ? this.tenant_id : (java.lang.Long) defaultValue(fields()[1]);
                record.incident_id =
                        fieldSetFlags()[2] ? this.incident_id : (java.lang.CharSequence) defaultValue(fields()[2]);
                record.alarm_type =
                        fieldSetFlags()[3] ? this.alarm_type : (java.lang.Integer) defaultValue(fields()[3]);
                record.alarm_level =
                        fieldSetFlags()[4] ? this.alarm_level : (java.lang.Integer) defaultValue(fields()[4]);
                record.urge_times = fieldSetFlags()[5] ? this.urge_times : (java.lang.Long) defaultValue(fields()[5]);
                record.create_time = fieldSetFlags()[6] ? this.create_time : (java.lang.Long) defaultValue(fields()[6]);
                record.alarm_receiver =
                        fieldSetFlags()[7] ? this.alarm_receiver : (java.lang.CharSequence) defaultValue(fields()[7]);
                record.ticket = fieldSetFlags()[8] ? this.ticket : (java.lang.CharSequence) defaultValue(fields()[8]);
                record.alarm_title =
                        fieldSetFlags()[9] ? this.alarm_title : (java.lang.CharSequence) defaultValue(fields()[9]);
                record.next_receiver =
                        fieldSetFlags()[10] ? this.next_receiver : (java.lang.CharSequence) defaultValue(fields()[10]);
                return record;
            } catch (Exception e) {
                throw new org.apache.avro.AvroRuntimeException(e);
            }
        }
    }
}
