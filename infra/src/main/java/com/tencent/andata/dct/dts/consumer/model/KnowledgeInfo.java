package com.tencent.andata.dct.dts.consumer.model;

import com.tencent.andata.utils.DateFormatUtils;
import com.tencent.andata.utils.rowdata.MessageRowData;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.flink.table.data.TimestampData;
import org.apache.flink.types.RowKind;

@Data
@Builder
public class KnowledgeInfo {
    @JsonProperty(value = "id")
    private Long id;
    @JsonProperty(value = "tenant_id")
    private Long tenantId;
    @JsonProperty(value = "incident_id")
    private String incidentId;
    private String title;
    private String content;
    @JsonProperty(value = "before_content")
    private String beforeContent;
    @JsonProperty(value = "after_content")
    private String afterContent;
    private Integer status;
    private String source;
    @JsonProperty(value = "first_category_id")
    private Integer firstCategoryId;
    @JsonProperty(value = "second_category_id")
    private Integer secondCategoryId;
    @JsonProperty(value = "is_deleted")
    private Integer isDeleted;
    @JsonProperty(value = "create_by")
    private String createBy;
    @JsonProperty(value = "import_by")
    private String importBy;
    @JsonProperty(value = "update_by")
    private String updateBy;
    @JsonProperty(value = "create_at")
    private String createAt;
    @JsonProperty(value = "import_at")
    private String importAt;
    @JsonProperty(value = "update_at")
    private String updateAt;
    private String tags;
    private Integer level;

    /***
     * constructFromRowData 根据rowdata构建KnowledgeInfo
     * @param rowData .
     * @return .
     */
    public static KnowledgeInfo constructFromRowData(MessageRowData rowData) {
        Integer isDeleted = 0;
        if (rowData.getRowKind().equals(RowKind.DELETE)) {
            isDeleted = 1;
        } else {
            isDeleted = rowData.getInt(11);
        }
        KnowledgeInfoBuilder builder = KnowledgeInfo.builder()
                .id(rowData.getLong(0))
                .tenantId(rowData.getLong(1))
                .incidentId(rowData.getString(2).toString())
                .title(rowData.getString(3).toString())
                .content(rowData.getString(4).toString())
                .beforeContent(rowData.getString(5).toString())
                .afterContent(rowData.getString(6).toString())
                .status(rowData.getInt(7))
                .source(rowData.getString(8).toString())
                .firstCategoryId(rowData.getInt(9))
                .secondCategoryId(rowData.getInt(10))
                .isDeleted(isDeleted)
                .createBy(rowData.getString(12).toString())
                .importBy(rowData.getString(13).toString())
                .updateBy(rowData.getString(14).toString())
                .createAt(rowData.getField(15) != null
                        ? DateFormatUtils.timestampToString(
                        rowData.getTimestamp(15, 6)
                                .toTimestamp().getTime() + 28800000,
                        "yyyy-MM-dd HH:mm:ss") : "1970-01-01 00:00:00")
                .importAt(rowData.getField(16) != null
                        ? DateFormatUtils.timestampToString(
                        rowData.getTimestamp(16, 6)
                                .toTimestamp().getTime() + 28800000,
                        "yyyy-MM-dd HH:mm:ss") : "1970-01-01 00:00:00")
                .updateAt(rowData.getField(17) != null
                        ? DateFormatUtils.timestampToString(
                        rowData.getTimestamp(17, 6)
                                .toTimestamp().getTime() + 28800000,
                        "yyyy-MM-dd HH:mm:ss") : "1970-01-01 00:00:00");

        String dbName = rowData.getAttr().getSrc().getDbName();
        if ("tc_andon_rt".equals(dbName)) {
            builder
                    .level(rowData.getInt(18))
                    .tags(rowData.getString(19) == null ? "" : rowData.getString(19).toString());
        } else {
            builder
                    .tags(rowData.getString(18) == null ? "" : rowData.getString(18).toString())
                    .level(rowData.getInt(19));
        }

        return builder.build();
    }

}
