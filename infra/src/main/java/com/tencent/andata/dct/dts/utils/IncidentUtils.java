package com.tencent.andata.dct.dts.utils;

import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.types.logical.VarCharType;

import java.util.List;
import java.util.stream.Collectors;

public class IncidentUtils {
    RainbowUtils rainbowUtils;

    /**
     * 获取AndonMvp表信息数据
     *
     * @param rainbowUtils
     */
    public IncidentUtils(RainbowUtils rainbowUtils) {
        this.rainbowUtils = rainbowUtils;
    }

    /**
     * 获取知识库的RowType
     *
     * @return
     */
    public RowType knowledgeRowType() throws Exception {
        return getRowType("t_knowledge", "d_knowledge");
    }

    /**
     * 获取工单表的RowType
     *
     * @return
     */
    public RowType incidentsRowType() throws Exception {
        return getRowType("incidents", "d_incident");
    }

    /**
     * 获取工单流水表RowType
     *
     * @return
     */
    public RowType incidentOperationRowType() throws Exception {
        return getRowType("incident_operations", "d_incident");
    }

    /**
     * 获取OLA告警表RowType
     *
     * @return
     */
    public RowType olaRecordRowType() throws Exception {
        return getRowType("c_ola_record", "d_record");
    }

    private RowType getRowType(String tableName, String dbName) throws Exception {
        DatabaseConf databaseConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("cdc.database.mysql.%s", dbName))
                .build();

        Schema rdbmsSchema = CDCUtils.getRdbmsSchema(
                databaseConf,
                tableName,
                DatabaseEnum.MYSQL,
                null
        );
        RowType rowType = TableUtils.convertFlinkSchemaToRowType(rdbmsSchema, false);
        List<RowType.RowField> newFields = rowType.getFields().stream().map(field -> {
            String name = field.getType().getTypeRoot().name();
            if (name.startsWith("TIMESTAMP")) {
                field = new RowType.RowField(field.getName(), new VarCharType());
            }
            return field;
        }).collect(Collectors.toList());
        rowType = new RowType(newFields);
        return rowType;
    }
}