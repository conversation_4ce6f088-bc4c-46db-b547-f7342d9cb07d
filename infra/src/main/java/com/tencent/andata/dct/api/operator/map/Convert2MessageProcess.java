package com.tencent.andata.dct.api.operator.map;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.struct.avro.message.MessageType;
import com.tencent.andata.utils.RowDataSplitCleanFunction;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.rowdata.RowDataConverter;
import java.io.Serializable;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonRowDataDeserializationSchema;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Collector;
import org.apache.flink.streaming.api.functions.ProcessFunction;

public class Convert2MessageProcess extends ProcessFunction<Tuple2<String, String>, Message> {
    static class DesConf implements Serializable {
        boolean isDataCompressed = false;
        RowType rowType;
        ArrayList<Tuple2<RowData.FieldGetter, LogicalType>> getterLogicalTypeTupleList;
        transient RowDataConverter rowDataConverter;
        transient JsonRowDataDeserializationSchema jsonDeserializer;

        public DesConf(RowType f0) {
            this.rowType = f0;
            getterLogicalTypeTupleList = CDCUtils.convertRowTypeToFieldGetterTupleList(rowType);
        }

        public ArrayList<Tuple2<RowData.FieldGetter, LogicalType>> getGetterAndLogicalType() {
            return this.getterLogicalTypeTupleList;
        }

        public JsonRowDataDeserializationSchema getDeserializer() {
            if (jsonDeserializer == null) {
                jsonDeserializer = new JsonRowDataDeserializationSchema(
                        rowType,
                        InternalTypeInfo.of(rowType),
                        false,
                        true,
                        TimestampFormat.SQL
                );
            }
            return this.jsonDeserializer;
        }

        public RowDataConverter getConverter() {
            if (rowDataConverter == null) {
                rowDataConverter = new RowDataConverter(rowType, isDataCompressed);
            }
            return this.rowDataConverter;
        }
    }

    public static class Builder {
        HashMap<String, DesConf> tagHashMap = new HashMap<>();
        boolean isDataCompressed = false;

        public Builder addPatternAndRowType(String pattern, RowType rowType) {
            tagHashMap.put(pattern, new DesConf(rowType));
            return this;
        }

        public Builder setDataCompress(boolean isDataCompressed) {
            this.isDataCompressed = isDataCompressed;
            return this;
        }

        public Builder setPatternTableMap(HashMap<String, InfraConfManager.DstTBLInfo> patternTableMap) {
            patternTableMap.forEach(
                    (key, value) -> this.addPatternAndRowType(
                            key,
                            value.schema.f2
                    )
            );
            return this;
        }

        public Convert2MessageProcess build() {
            // 设置正文Data是 压缩 & 非压缩
            tagHashMap.values().forEach(desConf -> desConf.isDataCompressed = isDataCompressed);
            // 生成Process
            Convert2MessageProcess retConvertProcess = new Convert2MessageProcess();
            retConvertProcess.tagHashMap = this.tagHashMap;
            return retConvertProcess;
        }
    }

    HashMap<String, DesConf> tagHashMap;

    protected Convert2MessageProcess() {
    }

    public static Builder builder() {
        return new Builder();
    }

    @Override
    public void processElement(
            Tuple2<String, String> tp2,
            ProcessFunction<Tuple2<String, String>, Message>.Context context,
            Collector<Message> collector
    ) throws Exception {
        String msgChainKey = tp2.f0;
        String msgData = tp2.f1;
        // 如果数据在TagHashMap中，那么就进行反序列化 & 分发
        // 如果这里这些没有匹配到，直接丢弃，不送到下游
        // TODO: 没有命中的还是得做个记录，后面想想怎么整。一层ODS保证不会丢失，但最好还是做个记录
        if (tagHashMap.containsKey(msgChainKey)) {
            DesConf tagProperties = tagHashMap.get(msgChainKey);

            RowData retRowData = RowDataSplitCleanFunction.map(
                    tagProperties
                            .getDeserializer()
                            .deserialize(msgData.getBytes(StandardCharsets.UTF_8)),
                    tagProperties.getGetterAndLogicalType()
            );

            RowDataConverter converter = tagProperties.getConverter();

            Message retMessage = new Message();
            // TODO: 构造Message这里正常应该也有个Builder的，但这块比较简单，就先这样
            retMessage.setData(ByteBuffer.wrap(converter.serializeRowDataToBytes(retRowData)));
            retMessage.setMsgType(MessageType.ROW_DATA);
            retMessage.setSchemaName(msgChainKey);
            retMessage.setProcTime(System.currentTimeMillis());

            collector.collect(retMessage);
        }

    }
}