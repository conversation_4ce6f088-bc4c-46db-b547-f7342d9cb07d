package com.tencent.andata.dct.api.process;

import com.tencent.andata.utils.JSONUtils;
import java.io.Serializable;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.table.data.RowData;

public abstract class BaseSpecialProcess implements Serializable {

    protected JSONUtils jsonUtils;

    // 特殊处理抽象基类
    public abstract ObjectNode handle(ObjectNode messageValue, RowData rowData);
}