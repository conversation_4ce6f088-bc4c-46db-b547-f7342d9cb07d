package com.tencent.andata.dct.api.serialize;

import org.apache.avro.io.BinaryEncoder;
import org.apache.avro.io.DatumWriter;
import org.apache.avro.io.EncoderFactory;
import org.apache.avro.reflect.ReflectDatumWriter;
import org.apache.flink.api.common.serialization.SerializationSchema;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class KafkaAvro2BytesValueSerSchema<T> implements SerializationSchema<T> {
    Class<T> tClass;
    DatumWriter<T> writer;
    BinaryEncoder encoder;

    public KafkaAvro2BytesValueSerSchema(Class<T> tClass) {
        this.tClass = tClass;
    }

    void initConverter() {
        if (writer == null) {
            writer = new ReflectDatumWriter<>(tClass);
        }
    }

    @Override
    public byte[] serialize(T message) {
        this.initConverter();
        // 这里每次都重新生成一个ByteArray
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        encoder = EncoderFactory.get().binaryEncoder(outputStream, encoder);
        try {
            this.writer.write(message, encoder);
            encoder.flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return outputStream.toByteArray();
    }
}