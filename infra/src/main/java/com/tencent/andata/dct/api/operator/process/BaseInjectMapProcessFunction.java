package com.tencent.andata.dct.api.operator.process;

import java.util.List;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.data.RowData;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.HashMap;

public abstract class BaseInjectMapProcessFunction<I, O> extends ProcessFunction<I, O> {
    protected HashMap<String, OutputTag<RowData>> tagHashMap;

    /**
     * 功能：将数据按照指定的Key进行分流
     *
     * @param tagKeyList
     */
    public BaseInjectMapProcessFunction(List<String> tagKeyList) {
        this.tagHashMap = new HashMap<>();
    }

    /**
     * 从Input中获取当前数据的Table或者Schema Key
     *
     * @param in Input
     * @return Key
     */
    protected abstract String getKey(I in);

    /**
     * 从Input中获取数据值
     * 如果需要对数据做特殊处理的话在这里处理
     *
     * @param in Input
     * @return Value
     */
    protected abstract RowData getValue(I in) throws Exception;

    /**
     * 根据Key获取测流Tag
     * 使用懒加载的方式
     *
     * @param key Key
     * @return outputTag
     */
    protected OutputTag<RowData> getTag(String key) {
        OutputTag<RowData> res;
        if (tagHashMap.containsKey(key)) {
            res = tagHashMap.get(key);
        } else {
            res = new OutputTag<RowData>(key) {
            };
            tagHashMap.put(key, res);
        }
        return res;
    }


    /**
     * 这里是Process的分发/清洗流程，子类只需要实现具体方法即可
     *
     * @param input     input
     * @param context   context
     * @param collector collector
     * @throws Exception
     */
    @Override
    public void processElement(
            I input, ProcessFunction<I, O>.Context context, Collector<O> collector
    ) throws Exception {
        // 获取table or schema Key
        // TODO: 如果有不同库的重名的数据流，会有问题
        String key = getKey(input);
        // 获取值
        RowData rowData = getValue(input);
        // 根据Key获取测流Tag
        OutputTag<RowData> outputTag = getTag(key);
        if (outputTag != null) {
            // 发往测流
            context.output(outputTag, rowData);
        }
    }
}