package com.tencent.andata.dct.api.serialize;

import com.tencent.andata.utils.rowdata.RowDataConverter;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.io.IOException;

public class L1OdsKafkaRecordSerializationSchema implements KafkaRecordSerializationSchema<RowData> {
    final RowType rowType;
    RowDataConverter converter;
    final String msgKafkaTopic;

    public L1OdsKafkaRecordSerializationSchema(RowType rowType, String msgKafkaTopic) {
        this.rowType = rowType;
        this.msgKafkaTopic = msgKafkaTopic;
    }

    void initDataConverter() {
        if (converter == null) {
            converter = new RowDataConverter(rowType, true);
        }
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(RowData rowData, KafkaSinkContext kafkaSinkContext, Long aLong) {
        initDataConverter();
        try {
            // 这里没必要对他进行包装，尽可能快的把数据传到后面就行。这里只给L1 ODS做的特殊开发
            return new ProducerRecord<>(msgKafkaTopic, converter.serializeRowDataToBytes(rowData));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}