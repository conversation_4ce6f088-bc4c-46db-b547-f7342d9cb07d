package com.tencent.andata.dct.dts.consumer;

import com.google.common.collect.ImmutableList;
import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.dct.dts.consumer.map.RowData2RowMap;
import com.tencent.andata.dct.dts.consumer.sink.HbaseSink;
import com.tencent.andata.utils.ConcurrentIcebergTableLoader;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.andata.writer.deserialize.schema.AvroMessageDeserializationSchema;
import com.tencent.andata.writer.message.utils.RowDataMessageConverter;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Preconditions;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Properties;

public class TicketFlow2Hbase {

    public static final Logger LOG = LoggerFactory.getLogger(TicketFlow2Hbase.class);
    // 数据总线配置
    public static String SourceKafkaPropGroup = "mq.kafka.data_bus";
    public static String odsDatabaseKey = "ods_database";
    public static String consumerTablesKey = "ods_table_pk";

    /***
     * main 同步工单知识库数据到ansearch
     * @param args .
     * @throws Exception .
     */
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        StreamExecutionEnvironment env = flinkEnv.env();
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        RainbowUtils rainbowUtils = new RainbowUtils(PropertyUtils.loadProperties("env.properties"));
        // 配置kafka数据源
        DataStreamSource<MessageRowData> kafkaSource = env.fromSource(
                KafkaSource.<MessageRowData>builder()
                        .setProperties(getDTSKafkaProp(rainbowUtils))
                        .setTopics(rainbowUtils.getStringValue(SourceKafkaPropGroup, "TOPICS"))
                        // 第一次从最开始读取，后续从上一次提交的Commit读取
                        .setStartingOffsets(OffsetsInitializer.latest())
                        .setValueOnlyDeserializer(getDeserializationSchema(rainbowUtils))
                        .build(),
                WatermarkStrategy.noWatermarks(),
                "tc-andon databus source"
        ).setParallelism(1);

        String database = rainbowUtils.getStringValue("conf", odsDatabaseKey);
        // 工单入hbase
        RowData2RowMap incidentMap = new RowData2RowMap(
                new TableIdentifier(DatabaseEnum.ICEBERG, database, "ods_incidents")
        );
        SingleOutputStreamOperator<Row> incidentDs = kafkaSource
                .process(incidentMap)
                .map(new MapFunction<Row, Row>() {
                    @Override
                    public Row map(Row row) throws Exception {
                        LOG.info("工单数据：{}", row);
                        return row;
                    }
                }).returns(incidentMap.rowTypeInfo);
        // 写入hbase
        new HbaseSink(
                rainbowUtils.getStringValue("sink.hbase.incident", "HTBIName"),
                "f_hbase_incident",
                ImmutableList.of("CAST(`tenant_id` as STRING)", "CAST(`incident_id` as STRING)")
        ).sink(flinkEnv, tEnv.fromDataStream(incidentDs));
        // 工单流水入hbase
        RowData2RowMap flowMap = new RowData2RowMap(
                new TableIdentifier(DatabaseEnum.ICEBERG, database, "ods_incident_operations")
        );
        SingleOutputStreamOperator<Row> flowDs = kafkaSource
                .process(flowMap)
                .map(new MapFunction<Row, Row>() {
                    @Override
                    public Row map(Row row) throws Exception {
                        LOG.info("流水数据：{}", row);
                        return row;
                    }
                }).returns(flowMap.rowTypeInfo);
        // 写入hbase
        new HbaseSink(
                rainbowUtils.getStringValue("sink.hbase.operation", "HTBIName"),
                "f_hbase_operation",
                ImmutableList
                        .of(
                                "CAST(`tenant_id` as STRING)",
                                "CAST(`incident_id` as STRING)",
                                "CAST(`operation_id` as STRING)"
                        )
        ).sink(flinkEnv, tEnv.fromDataStream(flowDs));

        flinkEnv.stmtSet().execute();
        env.disableOperatorChaining();
        env.enableCheckpointing(5 * 1000, CheckpointingMode.AT_LEAST_ONCE);
        env.execute("andon ticket binlog extractor to hbase");
    }


    /**
     * 从Rainbow中获取DTS Sink Kafka配置
     *
     * @param rainbowUtils .
     * @return .
     */
    public static Properties getDTSKafkaProp(RainbowUtils rainbowUtils) {
        String dtsKafkaBrokers = rainbowUtils.getStringValue(SourceKafkaPropGroup, "BROKERS");
        String dtsKafkaTopic = rainbowUtils.getStringValue(SourceKafkaPropGroup, "TOPICS");
        String dtsKafkaGroup = rainbowUtils.getStringValue(SourceKafkaPropGroup, "HBASE_SINK_CONSUMER_GROUP");

        Preconditions.checkNotNull(dtsKafkaBrokers);
        Preconditions.checkNotNull(dtsKafkaTopic);
        Preconditions.checkNotNull(dtsKafkaGroup);
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, dtsKafkaBrokers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, dtsKafkaGroup);
        props.put(ConsumerConfig.CLIENT_ID_CONFIG, dtsKafkaGroup + "_" + Instant.now().getEpochSecond());
        // 定时监听分区变化
        props.put("partition.discovery.interval.ms", "1800000");
        // 在CK时Commit数据
        props.put("commit.offsets.on.checkpoint", "true");
        return props;
    }

    /***
     * 构造反序列化器
     * @return .
     * @throws InterruptedException .
     * @param rainbowUtils
     */
    private static AvroMessageDeserializationSchema getDeserializationSchema(RainbowUtils rainbowUtils)
            throws InterruptedException, JsonProcessingException {
        HashMap<String, InfraConfManager.DstTBLInfo> patternMap = new HashMap<>();

        String odsDatabase = rainbowUtils.getStringValue("conf", odsDatabaseKey);
        ObjectNode jsonValue = rainbowUtils.getJSONValue("conf", consumerTablesKey);
        Iterator<String> fieldNames = jsonValue.fieldNames();
        while (fieldNames.hasNext()) {
            String name = fieldNames.next();
            // 目标表信息
            InfraConfManager.DstTBLInfo dstTBLInfo = new InfraConfManager.DstTBLInfo();
            dstTBLInfo.dstTableName = "ods_" + name;
            dstTBLInfo.dstDatabase = odsDatabase;
            ArrayList<String> eqFields = new ArrayList<>();
            JsonNode eqFieldsJson = jsonValue.get(name);
            for (JsonNode node : eqFieldsJson) {
                eqFields.add(node.asText());
            }
            dstTBLInfo.eqFields = eqFields;
            System.out.println(String.format("patternMap put info: %s, %s", name, dstTBLInfo));
            patternMap.put(name, dstTBLInfo);
        }

        // 构造patternTableMap
        ConcurrentIcebergTableLoader loader = ConcurrentIcebergTableLoader.fromHiveDB(odsDatabase);
        patternMap.forEach((key, value) -> loader.addTable(key, value.dstTableName));

        loader.load().toTuple3List().forEach(value -> {
            InfraConfManager.DstTBLInfo l2OdsInfo = patternMap.get(value.f0);
            l2OdsInfo.schema = new Tuple3<>(value.f1, value.f2, IcebergCatalogReader.getTableRowType(value.f1));
            patternMap.put(value.f0, l2OdsInfo);
        });

        // 构造反序列化器
        AvroMessageDeserializationSchema deserializationSchema = new AvroMessageDeserializationSchema(true);
        patternMap.forEach((key, v) -> deserializationSchema.addPatternAndConverter(
                        key,
                        new RowDataMessageConverter(
                                v.schema.f2,
                                Boolean.parseBoolean(rainbowUtils
                                        .getStringValue(SourceKafkaPropGroup, "PayloadCompressed")),
                                v.dstDatabase
                        )
                )
        );
        return deserializationSchema;
    }


}
