package com.tencent.andata.dct.dts.consumer;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.dct.dts.consumer.convert.AnsearchConvert;
import com.tencent.andata.dct.dts.consumer.convert.IncidentConvert;
import com.tencent.andata.dct.dts.consumer.convert.KnowledgeConvert;
import com.tencent.andata.dct.dts.consumer.map.AnSearchConvertMap;
import com.tencent.andata.dct.dts.consumer.sink.HttpSink;
import com.tencent.andata.utils.ConcurrentIcebergTableLoader;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.writer.deserialize.schema.AvroMessageDeserializationSchema;
import com.tencent.andata.writer.message.utils.RowDataMessageConverter;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.flink.util.Preconditions;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Properties;

public class TicketCDC2AnSearch {

    public static final Logger LOG = LoggerFactory.getLogger(TicketCDC2AnSearch.class);

    public static String SourceKafkaPropGroup = "mq.kafka.data_bus";

    public static String odsDatabaseKey = "ods_database";
    public static String consumerTablesKey = "ods_table_pk";
    // http 相关
    public static String ansearchHttp = "sink.ansearch.http";


    /**
     * 从Rainbow中获取DTS Sink Kafka配置
     *
     * @param rainbowUtils .
     * @return .
     */
    public static Properties getDTSKafkaProp(RainbowUtils rainbowUtils) {
        String dtsKafkaBrokers = rainbowUtils.getStringValue(SourceKafkaPropGroup, "BROKERS");
        String dtsKafkaTopic = rainbowUtils.getStringValue(SourceKafkaPropGroup, "TOPICS");
        String dtsKafkaGroup = rainbowUtils.getStringValue(SourceKafkaPropGroup, "TICKET_CDC_CONSUMER_GROUP");

        Preconditions.checkNotNull(dtsKafkaBrokers);
        Preconditions.checkNotNull(dtsKafkaTopic);
        Preconditions.checkNotNull(dtsKafkaGroup);
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, dtsKafkaBrokers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, dtsKafkaGroup);
        props.put(ConsumerConfig.CLIENT_ID_CONFIG, dtsKafkaGroup + "_" + Instant.now().getEpochSecond());
        // 定时监听分区变化
        props.put("partition.discovery.interval.ms", "1800000");
        // 在CK时Commit数据
        props.put("commit.offsets.on.checkpoint", "true");
        return props;
    }

    /***
     * 构造反序列化器
     * @return .
     * @throws InterruptedException .
     * @param rainbowUtils
     */
    public static AvroMessageDeserializationSchema getDeserializationSchema(RainbowUtils rainbowUtils)
            throws InterruptedException, JsonProcessingException {
        HashMap<String, InfraConfManager.DstTBLInfo> patternMap = new HashMap<>();

        String odsDatabase = rainbowUtils.getStringValue("conf", odsDatabaseKey);
        ObjectNode jsonValue = rainbowUtils.getJSONValue("conf", consumerTablesKey);
        Iterator<String> fieldNames = jsonValue.fieldNames();
        while (fieldNames.hasNext()) {
            String name = fieldNames.next();
            // 目标表信息
            InfraConfManager.DstTBLInfo dstTBLInfo = new InfraConfManager.DstTBLInfo();
            dstTBLInfo.dstTableName = "ods_" + name;
            dstTBLInfo.dstDatabase = odsDatabase;
            ArrayList<String> eqFields = new ArrayList<>();
            JsonNode eqFieldsJson = jsonValue.get(name);
            for (JsonNode node : eqFieldsJson) {
                eqFields.add(node.asText());
            }
            dstTBLInfo.eqFields = eqFields;
            System.out.println(String.format("patternMap put info: %s, %s", name, dstTBLInfo));
            patternMap.put(name, dstTBLInfo);
        }

        // 构造patternTableMap
        ConcurrentIcebergTableLoader loader = ConcurrentIcebergTableLoader.fromHiveDB(odsDatabase);
        patternMap.forEach((key, value) -> loader.addTable(key, value.dstTableName));

        loader.load().toTuple3List().forEach(value -> {
            InfraConfManager.DstTBLInfo l2OdsInfo = patternMap.get(value.f0);
            l2OdsInfo.schema = new Tuple3<>(value.f1, value.f2, IcebergCatalogReader.getTableRowType(value.f1));
            patternMap.put(value.f0, l2OdsInfo);
        });

        // 构造反序列化器
        AvroMessageDeserializationSchema deserializationSchema = new AvroMessageDeserializationSchema(true);
        patternMap.forEach((key, v) -> deserializationSchema.addPatternAndConverter(
                        key,
                        new RowDataMessageConverter(
                                v.schema.f2,
                                Boolean.parseBoolean(rainbowUtils
                                        .getStringValue(SourceKafkaPropGroup, "PayloadCompressed")),
                                v.dstDatabase
                        )
                )
        );
        return deserializationSchema;
    }


    /***
     * main 同步工单知识库数据到ansearch
     * @param args .
     * @throws Exception .
     */
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        RainbowUtils rainbowUtils = new RainbowUtils(PropertyUtils.loadProperties("env.properties"));
        // 获取Kafka配置
        Properties dtsKafkaProp = getDTSKafkaProp(rainbowUtils);

        KafkaSource<MessageRowData> source = KafkaSource.<MessageRowData>builder()
                .setProperties(dtsKafkaProp)
                .setTopics(rainbowUtils.getStringValue(SourceKafkaPropGroup, "TOPICS"))
                // 第一次从最开始读取，后续从上一次提交的Commit读取
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(getDeserializationSchema(rainbowUtils))
                .build();

        DataStreamSource<MessageRowData> kafkaSource = env.fromSource(
                source,
                WatermarkStrategy.noWatermarks(),
                "dts kafka source"
        ).setParallelism(1);

        HashMap<String, AnsearchConvert> convertHashMap = new HashMap<>();
        convertHashMap.put("incidents", new IncidentConvert());
        convertHashMap.put("t_knowledge", new KnowledgeConvert());
        kafkaSource.process(new ProcessFunction<MessageRowData, MessageRowData>() {
            @Override
            public void processElement(
                    MessageRowData messageRowData,
                    ProcessFunction<MessageRowData, MessageRowData>.Context context,
                    Collector<MessageRowData> collector) throws Exception {
                if (messageRowData == null) {
                    return;
                }
                LOG.info("process ticket cdc {}", messageRowData);
                if (messageRowData.getRowKind().equals(RowKind.UPDATE_BEFORE)) {
                    return;
                }
                collector.collect(messageRowData);
            }
        }).map(new AnSearchConvertMap(convertHashMap))
                .filter(body -> body != null)
                .addSink(new HttpSink(
                        rainbowUtils.getStringValue(ansearchHttp, "url"),
                        rainbowUtils.getStringValue(ansearchHttp, "user"),
                        rainbowUtils.getStringValue(ansearchHttp, "user_key"),
                        rainbowUtils.getStringValue(ansearchHttp, "dataset"),
                        Integer.valueOf(rainbowUtils.getStringValue(ansearchHttp, "data_type")),
                        Integer.valueOf(rainbowUtils.getStringValue(ansearchHttp, "batch_size"))
                ));

        env.disableOperatorChaining();
        env.enableCheckpointing(5 * 1000, CheckpointingMode.AT_LEAST_ONCE);
        env.execute("andon ticket binlog extractor");
    }
}
