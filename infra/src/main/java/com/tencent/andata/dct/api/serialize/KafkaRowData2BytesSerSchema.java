package com.tencent.andata.dct.api.serialize;

import com.tencent.andata.utils.rowdata.RowDataConverter;
import java.io.IOException;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.kafka.clients.producer.ProducerRecord;

public class KafkaRowData2BytesSerSchema implements KafkaSerializationSchema<RowData> {
    final String msgKafkaTopic;
    final RowType rowType;
    RowDataConverter converter;

    public KafkaRowData2BytesSerSchema(RowType rowType, String msgKafkaTopic) {
        this.rowType = rowType;
        this.msgKafkaTopic = msgKafkaTopic;
    }

    void initDataConverter() {
        if (converter == null) {
            converter = new RowDataConverter(rowType, true);
        }
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(RowData element, Long timestamp) {
        this.initDataConverter();
        try {
            // 这里没必要对他进行包装，尽可能快的把数据传到后面就行。这里只给L1 ODS做的特殊开发
            return new ProducerRecord<>(msgKafkaTopic, converter.serializeRowDataToBytes(element));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}