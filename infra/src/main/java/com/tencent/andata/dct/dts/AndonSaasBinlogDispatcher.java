package com.tencent.andata.dct.dts;


import com.tencent.andata.dct.dts.function.KafkaDataDecodeProcessFunction;
import com.tencent.andata.dct.dts.function.MessageEncodeFunction;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.dct.dts.deserializer.KafkaDataWithMetaDeserializer;
import com.tencent.andata.dct.dts.utils.IncidentUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.formats.avro.AvroSerializationSchema;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Collector;
import org.apache.flink.util.Preconditions;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.common.config.SaslConfigs;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Properties;
import java.util.Random;


public class AndonSaasBinlogDispatcher {
    public static String DTSKafkaPropGroup = "cdc.kafka.dts";
    public static String SinkKafkaPropGroup = "mq.kafka.data_bus";

    /**
     * 从Rainbow中获取DTS Kafka配置
     *
     * @param rainbowUtils
     * @return
     */
    public static Properties getDTSKafkaProp(RainbowUtils rainbowUtils) {
        String dtsKafkaBrokers = rainbowUtils.getStringValue(DTSKafkaPropGroup, "BROKERS");
        String dtsKafkaTopic = rainbowUtils.getStringValue(DTSKafkaPropGroup, "TOPICS");
        String dtsKafkaGroup = rainbowUtils.getStringValue(DTSKafkaPropGroup, "CONSUMER_GROUP");
        String dtsKafkaUser = rainbowUtils.getStringValue(DTSKafkaPropGroup, "USER");
        String dtsKafkaPassword = rainbowUtils.getStringValue(DTSKafkaPropGroup, "PASSWORD");
        Preconditions.checkNotNull(dtsKafkaBrokers);
        Preconditions.checkNotNull(dtsKafkaTopic);
        Preconditions.checkNotNull(dtsKafkaGroup);
        Preconditions.checkNotNull(dtsKafkaUser);
        Preconditions.checkState(!dtsKafkaUser.equals(""));
        Preconditions.checkNotNull(dtsKafkaPassword);
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, dtsKafkaBrokers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, dtsKafkaGroup);
        props.put(ConsumerConfig.CLIENT_ID_CONFIG, dtsKafkaGroup + "_" + Instant.now().getEpochSecond());
        props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
        props.put(SaslConfigs.SASL_MECHANISM, "SCRAM-SHA-512");
        props.put(SaslConfigs.SASL_JAAS_CONFIG,
                "org.apache.kafka.common.security.scram.ScramLoginModule required "
                        + "  username=\"" + dtsKafkaUser + "\""
                        + "  password=\"" + dtsKafkaPassword + "\";");
        // 定时监听分区变化
        props.put("partition.discovery.interval.ms", "1800000");
        // 在CK时Commit数据
        props.put("commit.offsets.on.checkpoint", "true");
        return props;
    }

    /**
     * main
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        RainbowUtils rainbowUtils = new RainbowUtils(PropertyUtils.loadProperties("env.properties"));
        // 获取Kafka配置
        Properties dtsKafkaProp = getDTSKafkaProp(rainbowUtils);
        // 获取表RowType信息用于下游序列化
        IncidentUtils incidentUtils = new IncidentUtils(rainbowUtils);
        HashMap<String, RowType> tableRowTypeMap = new HashMap<>();
        tableRowTypeMap.put("incidents", incidentUtils.incidentsRowType());
        tableRowTypeMap.put("incident_operations", incidentUtils.incidentOperationRowType());
        tableRowTypeMap.put("c_ola_record", incidentUtils.olaRecordRowType());
        tableRowTypeMap.put("t_knowledge", incidentUtils.knowledgeRowType());
        // 获取Data字节数组和元数据信息
        KafkaSource<Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]>> binlogKafkaSource =
                KafkaSource.<Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]>>builder()
                        .setProperties(dtsKafkaProp)
                        .setTopics(rainbowUtils.getStringValue(DTSKafkaPropGroup, "TOPICS"))
                        // 第一次从最开始读取，后续从上一次提交的Commit读取
                        .setStartingOffsets(OffsetsInitializer.latest())
                        .setDeserializer(KafkaRecordDeserializationSchema.of(
                                new KafkaDataWithMetaDeserializer()
                        ))
                        .build();
        // Source -> Stream
        DataStreamSource<Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]>> kafkaPartitionDataStream =
                env.fromSource(
                        binlogKafkaSource,
                        WatermarkStrategy.noWatermarks(),
                        "kafka source"
                ).setParallelism(1);
        // 这里根据分区做Keyby是为了当Event被切片的时候保证能连续地获取一条Event的数据
        SingleOutputStreamOperator<MessageRowData> rowDataStream = kafkaPartitionDataStream
                .keyBy(a -> a.f0.partition)
                .process(new KafkaDataDecodeProcessFunction(tableRowTypeMap))
                .setParallelism(1)
                .flatMap(
                        new FlatMapFunction<ArrayList<MessageRowData>, MessageRowData>() {
                            @Override
                            public void flatMap(
                                    ArrayList<MessageRowData> rows, Collector<MessageRowData> collector
                            ) throws Exception {
                                for (MessageRowData row : rows) {
                                    collector.collect(row);
                                }
                            }
                        }
                );

        // 将数据封装成Message
        SingleOutputStreamOperator<Message> messageDataStream = rowDataStream.map(
                new MessageEncodeFunction(tableRowTypeMap)
        );
        // TODO: Flink 1.15有Kafka Sink，但1.13则没有。在master上注释掉这个，保证代码能合并进去
        // 将RowData序列化并封装成Message通过Avro序列化发送Kafka
        KafkaSink<Message> sink = KafkaSink.<Message>builder()
                .setKafkaProducerConfig(
                        new Properties() {{
                            // 精准一次需要降低Producer的超时时间，默认是1h
                            // 超时时间要大于CK时间
                            put("transaction.timeout.ms", 15 * 60 * 1000);
                        }}
                )
                .setBootstrapServers(rainbowUtils.getStringValue(SinkKafkaPropGroup, "BROKERS"))
                .setRecordSerializer(
                        KafkaRecordSerializationSchema.builder()
                                .setTopic(rainbowUtils.getStringValue(SinkKafkaPropGroup, "TOPICS"))
                                .setValueSerializationSchema(
                                        // 将Message序列化成Avro格式发送
                                        AvroSerializationSchema.forSpecific(Message.class)
                                )
                                .build()
                )
                // 两阶段提交保证精准一次
                .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
                .setTransactionalIdPrefix("AndonSaasBinlogDispatcher transaction prefix" + new Random().nextInt(100000))
                .build();
        messageDataStream.sinkTo(sink).setParallelism(1);
        // CK 5S = 消息最高延迟
        env.enableCheckpointing(5 * 1000, CheckpointingMode.EXACTLY_ONCE);
        env.disableOperatorChaining();
        env.execute("andon saas binlog extractor");
    }

}