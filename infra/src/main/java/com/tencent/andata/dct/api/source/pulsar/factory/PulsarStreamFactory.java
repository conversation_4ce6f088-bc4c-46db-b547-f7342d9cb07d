package com.tencent.andata.dct.api.source.pulsar.factory;

import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.dct.api.source.pulsar.builder.PulsarStreamBuilder;
import lombok.Builder;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonRowDataDeserializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.RowType;

import java.util.ArrayList;
import java.util.Optional;

@Builder
public class PulsarStreamFactory {
    private StreamExecutionEnvironment env;
    private ArrayList<DBusConf.PulsarConf> pulsarConfigList;
    private RowType convertType;
    private Integer parallelism;

    /***
     * 从 pulsar 获取数据
     * @return ds
     * @throws Exception .
     */
    public DataStream<RowData> getPulsarSourceStreamFromConf() throws Exception {
        JsonRowDataDeserializationSchema jsonSchemaDeserializer =
                new JsonRowDataDeserializationSchema(
                        convertType, InternalTypeInfo.of(convertType), false, true, TimestampFormat.SQL);

        // Pulsar Source 列表， 可以消费多个Pulsar
        ArrayList<DataStream<RowData>> pulsarSourceList = new ArrayList<>();
        // 通过配置生成多个Pulsar Source流
        for (DBusConf.PulsarConf pulsarConf : pulsarConfigList) {
            String topic = pulsarConf.topic;
            String url = pulsarConf.entryPoint;
            String token = pulsarConf.token;
            String subscriptionName = pulsarConf.subscriptionName;
            pulsarSourceList.add(new PulsarStreamBuilder()
                    .setUrl(url)
                    .setToken(token)
                    .setTopic(topic)
                    .setSubscriptionName(subscriptionName)
                    .setEnv(env)
                    .setDeserializationSchema(jsonSchemaDeserializer)
                    .setParallelism(parallelism)
                    .build());
        }
        // 通过Reduce将所有Pulsar Source 流合并成一个
        Optional<DataStream<RowData>> pulsarOpt = pulsarSourceList.stream().reduce(DataStream::union);
        assert pulsarOpt.isPresent();
        return pulsarOpt.get();
    }
}