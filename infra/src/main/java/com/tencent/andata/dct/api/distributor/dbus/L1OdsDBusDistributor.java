package com.tencent.andata.dct.api.distributor.dbus;

import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.conf.manager.struct.MQDistributeConf;
import com.tencent.andata.dct.api.serialize.L1OdsKafkaRecordSerializationSchema;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.operator.map.RowDataSplitCleanMap;

import java.util.Properties;
import java.util.Random;

import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.kafka.clients.producer.ProducerConfig;

public class L1OdsDBusDistributor extends DBusDistributor<RowData> {
    RowType rowType;

    protected L1OdsDBusDistributor(MQDistributeConf dConf, RowType rowType, DataStream<RowData> ds) {
        super(dConf, ds);
        this.rowType = rowType;
    }

    public static class Builder extends DBusDistributor.Builder<RowData> {
        RowType rowType;

        public Builder setL1OdsRowType(RowType rowType) {
            this.rowType = rowType;
            return this;
        }

        @Override
        public DBusDistributor<RowData> build() {
            return new L1OdsDBusDistributor(dConf, rowType, ds);
        }
    }

    @Override
    public void injectSinkToDS(Properties kafkaProperties) {
        // 构造KafkaSink
        // 精准一次需要降低Producer的超时时间，默认是1h
        // 超时时间要大于CK时间
        KafkaSink<RowData> sink = KafkaSink.<RowData>builder()
                .setKafkaProducerConfig(
                        kafkaProperties
                )
                .setBootstrapServers(dConf.dBusConf.dBusMQConf.entryPoint)
                .setRecordSerializer(
                        new L1OdsKafkaRecordSerializationSchema(rowType, dConf.dBusConf.dBusMQConf.topic)
                )
                // 两阶段提交保证精准一次
                .setTransactionalIdPrefix(
                        String.format("AndataApiExtractorToL1OdsTxId.%d", new Random().nextInt(100000))
                )
                .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
                .build();

        // TODO: L1 ODS 必须使用Compress，要不会出现单条数据过大的问题
        ds
                .map(new RowDataSplitCleanMap(CDCUtils.convertRowTypeToFieldGetterTupleList(rowType)))
                .sinkTo(sink)
                .uid(getUid())
                .name(getUid())
                .setParallelism(1);
    }

    public static Builder builder() {
        return new Builder();
    }

    public static DBusDistributor.Builder<RowData> buildFromInfraConf(InfraConf infraConf) {
        return new Builder()
                .setL1OdsRowType(infraConf.nrtConf.getL1OdsRowType())
                .setDistributeConf(infraConf.l1OdsMQDistributeConf);
    }
}