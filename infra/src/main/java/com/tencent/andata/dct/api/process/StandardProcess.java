package com.tencent.andata.dct.api.process;

import com.tencent.andata.utils.DateFormatUtils;
import com.tencent.andata.utils.JSONUtils;

import java.io.Serializable;
import java.util.ArrayList;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.table.data.RowData;

// 需要序列化才能在Flink的算子中调用
public class StandardProcess implements Serializable {

    private JSONUtils jsonUtils;

    public StandardProcess() {
        jsonUtils = new JSONUtils();
    }

    public ArrayList<ObjectNode> handle(RowData rowData) throws Exception {
        // 将Message里的数据格式化成Json列表
        String value = rowData.getString(5).toString();
        ArrayList<ObjectNode> messageValueList = null;
        messageValueList = jsonUtils.getJSONObjectArrayByString(value);
        for (ObjectNode valueObj : messageValueList) {
            // 加上创建时间
            valueObj.put(
                    "ods_create_time", DateFormatUtils.cstTimestampToUTCString(System.currentTimeMillis()));
        }
        return messageValueList;
    }
}