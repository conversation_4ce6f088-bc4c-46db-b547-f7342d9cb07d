package com.tencent.andata.dct.dts.consumer.convert;

import com.tencent.andata.dct.dts.consumer.model.KnowledgeInfo;
import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class KnowledgeConvert implements AnsearchConvert {

    private ObjectMapper objectMapper = new ObjectMapper();
    private SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public HashMap<String, Object> convertAnSearchReqBodyJson(
            MessageRowData rowData) throws JsonProcessingException, ParseException {
        KnowledgeInfo knowledgeInfo = KnowledgeInfo.constructFromRowData(rowData);
        HashMap<String, Object> data = objectMapper.convertValue(
                knowledgeInfo,
                new TypeReference<HashMap<String, Object>>() {
                }
        );
        data.put("source_name", "knowledge");
//        Long createTime;
//        String createAt = knowledgeInfo.getCreateAt();
//        if (StringUtils.isNotEmpty(createAt)
//                && !createAt.startsWith("0000-")) {
//            createTime = Timestamp.valueOf(createAt).getTime() / 1000;
//        } else {
//            createTime = 0L;
//        }
        HashMap<String, Object> reqBody = new HashMap<String, Object>() {{
            put("id", "angpt-knowledge-" + String.valueOf(knowledgeInfo.getId()));
            put("doc", knowledgeInfo.getTitle() + "\n" + knowledgeInfo.getContent());
            put("deleted", knowledgeInfo.getIsDeleted());
            put("create_time", format.parse(knowledgeInfo.getCreateAt()).getTime() / 1000);
            put("fields", data);
        }};
        return reqBody;
    }
}
