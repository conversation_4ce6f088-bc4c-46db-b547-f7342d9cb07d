package com.tencent.andata.dct.dts.consumer;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.dct.dts.consumer.map.RowData2RowMap;
import com.tencent.andata.dct.dts.consumer.map.TCAndonTicketWindowProcess;
import com.tencent.andata.dct.dts.consumer.map.TicketFlowGroupProcess;
import com.tencent.andata.dct.dts.consumer.map.GetTicketInfoProcess;
import com.tencent.andata.dct.dts.consumer.sink.HttpSink;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.ConcurrentIcebergTableLoader;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.andata.utils.udf.AsyncScanHbase;
import com.tencent.andata.writer.deserialize.schema.AvroMessageDeserializationSchema;
import com.tencent.andata.writer.message.utils.RowDataMessageConverter;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.flink.streaming.api.windowing.assigners.ProcessingTimeSessionWindows;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.Row;
import org.apache.flink.util.Preconditions;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.Properties;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashMap;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;

public class TicketCDC2AnSearchV2 {

    public static final Logger LOG = LoggerFactory.getLogger(TicketCDC2AnSearchV2.class);

    public static String SourceKafkaPropGroup = "mq.kafka.data_bus";

    public static String odsDatabaseKey = "ods_database";
    public static String consumerTablesKey = "ods_table_pk";
    // http 相关
    public static String ansearchHttp = "sink.ansearch.http";

    /***
     * main 同步工单知识库数据到ansearch
     * @param args .
     * @throws Exception .
     */
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        RainbowUtils rainbowUtils = new RainbowUtils(PropertyUtils.loadProperties("env.properties"));
        // 工单流
        String database = rainbowUtils.getStringValue("conf", odsDatabaseKey);
        // 读kafka数据
        DataStreamSource<MessageRowData> kafkaSource = env.fromSource(
                KafkaSource.<MessageRowData>builder()
                        .setProperties(getDTSKafkaProp(rainbowUtils))
                        .setTopics(rainbowUtils.getStringValue(SourceKafkaPropGroup, "TOPICS"))
                        // 第一次从最开始读取，后续从上一次提交的Commit读取
                        .setStartingOffsets(OffsetsInitializer.latest())
                        .setValueOnlyDeserializer(getDeserializationSchema(rainbowUtils))
                        .build(),
                WatermarkStrategy.noWatermarks(),
                "tc-andon flow data source"
        ).setParallelism(1);
        RowData2RowMap incidentMap = new RowData2RowMap(
                new TableIdentifier(DatabaseEnum.ICEBERG, database, "ods_incidents")
        );
        SingleOutputStreamOperator<Row> incidentDs = kafkaSource
                .process(incidentMap)
                .returns(incidentMap.rowTypeInfo)
                .union(ticketFlowDs2TicketInfoDs(kafkaSource, rainbowUtils, database,
                        incidentMap.rowTypeInfo, incidentMap.rowType))
                // 新增字段
                .map(new MapFunction<Row, Row>() {
                    @Override
                    public Row map(Row row) throws Exception {
                        Row newRow = Row.withNames();
                        Set<String> fieldNames = row.getFieldNames(true);
                        for (String fieldName : fieldNames) {
                            newRow.setField(fieldName, row.getField(fieldName));
                        }
                        // 新增字段
                        newRow.setField("rowkey", row.getField("tenant_id") + "-" + row.getField("incident_id"));
                        newRow.setField("operation_data", "");
                        return newRow;
                    }
                })
                .keyBy(row -> row.getField("rowkey").toString())
                // 这里可以开会话窗口
                .window(ProcessingTimeSessionWindows.withGap(Time.seconds(5)))
                // 这个窗口内去捞hbase流水然后聚合下发
                .process(new TCAndonTicketWindowProcess("update_time"));
        // 异步从hbase捞工单流水并聚合, 然后写入ansearch
        AsyncDataStream
                .unorderedWait(
                        incidentDs.map(r -> new Tuple3<>(r, "rowkey", "operation_data"))
                                .returns(new TypeHint<Tuple3<Row, String, String>>() {
                                }),
                        constructHbaseScanOp(rainbowUtils, "sink.hbase.operation"),
                        2 * 60 * 1000L, TimeUnit.MILLISECONDS, 100)
                // 工单内外部回复流水聚合
                .process(new TicketFlowGroupProcess())
                .addSink(new HttpSink(
                        rainbowUtils.getStringValue(ansearchHttp, "flow_url"),
                        rainbowUtils.getStringValue(ansearchHttp, "user"),
                        rainbowUtils.getStringValue(ansearchHttp, "user_key"),
                        rainbowUtils.getStringValue(ansearchHttp, "flow_dataset"),
                        Integer.valueOf(rainbowUtils.getStringValue(ansearchHttp, "flow_data_type")),
                        Integer.valueOf(rainbowUtils.getStringValue(ansearchHttp, "batch_size"))
                ));
        env.enableCheckpointing(5 * 1000, CheckpointingMode.AT_LEAST_ONCE);
        env.execute("andon ticket binlog extractor");
    }


    /***
     * 将接收到的流水转换为工单
     * @param kafkaSource .
     * @param rainbowUtils .
     * @param database .
     * @param rowTypeInfo .
     * @param rowType .
     * @return .
     * @throws TableNotExistException .
     */
    public static DataStream<Row> ticketFlowDs2TicketInfoDs(
            DataStreamSource<MessageRowData> kafkaSource,
            RainbowUtils rainbowUtils,
            String database,
            TypeInformation<Row> rowTypeInfo, RowType rowType) throws TableNotExistException {
        // 工单流水流
        RowData2RowMap operationsMap = new RowData2RowMap(
                new TableIdentifier(DatabaseEnum.ICEBERG, database, "ods_incident_operations")
        );
        SingleOutputStreamOperator<Row> flowDs = kafkaSource
                .process(operationsMap)
                .returns(operationsMap.rowTypeInfo)
                .map(new MapFunction<Row, Row>() {
                    @Override
                    public Row map(Row row) throws Exception {
                        Row newRow = Row.withNames();
                        Set<String> fieldNames = row.getFieldNames(true);
                        for (String fieldName : fieldNames) {
                            newRow.setField(fieldName, row.getField(fieldName));
                        }
                        // 新增字段
                        newRow.setField("rowkey", row.getField("tenant_id") + "-" + row.getField("incident_id"));
                        newRow.setField("ticket_info", "");
                        return newRow;
                    }
                })
                .keyBy(row -> row.getField("rowkey").toString())
                .window(TumblingProcessingTimeWindows.of(Time.seconds(2)))
                .process(new TCAndonTicketWindowProcess("operate_time"));
        // 异步从hbase捞工单数据
        SingleOutputStreamOperator<Row> ticketInfoDs = AsyncDataStream
                .unorderedWait(
                        flowDs.map(r -> new Tuple3<>(r, "rowkey", "ticket_info"))
                                .returns(new TypeHint<Tuple3<Row, String, String>>() {
                                }),
                        constructHbaseScanOp(rainbowUtils, "sink.hbase.incident"),
                        2 * 60 * 1000L, TimeUnit.MILLISECONDS, 100)
                .process(new GetTicketInfoProcess(rowType))
                .returns(rowTypeInfo);
        return ticketInfoDs;
    }

    public static RichAsyncFunction<Tuple3<Row, String, String>, Row> constructHbaseScanOp(
            RainbowUtils rainbowUtils, String groupPath) {
        // 从hbase捞工单流水 异步算子
        String zkQuorum = rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_QUORUM");
        String zkNodeParent = rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_ZNODE_PARENT");

        return AsyncScanHbase.builder()
                .hbaseZookeeperQuorum(zkQuorum)
                .zookeeperZnodeParent(zkNodeParent)
                .hTableName(rainbowUtils.getStringValue(groupPath, "HTBIName"))
                .columnFamily("cf")
                .qualifier("data")
                .build();
    }

    /**
     * 从Rainbow中获取DTS Sink Kafka配置
     *
     * @param rainbowUtils .
     * @return .
     */
    public static Properties getDTSKafkaProp(RainbowUtils rainbowUtils) {
        String dtsKafkaBrokers = rainbowUtils.getStringValue(SourceKafkaPropGroup, "BROKERS");
        String dtsKafkaTopic = rainbowUtils.getStringValue(SourceKafkaPropGroup, "TOPICS");
        String dtsKafkaGroup = rainbowUtils.getStringValue(SourceKafkaPropGroup, "TICKET_CDC_CONSUMER_GROUP");

        Preconditions.checkNotNull(dtsKafkaBrokers);
        Preconditions.checkNotNull(dtsKafkaTopic);
        Preconditions.checkNotNull(dtsKafkaGroup);
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, dtsKafkaBrokers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, dtsKafkaGroup);
        props.put(ConsumerConfig.CLIENT_ID_CONFIG, dtsKafkaGroup + "_" + Instant.now().getEpochSecond());
        // 定时监听分区变化
        props.put("partition.discovery.interval.ms", "1800000");
        // 在CK时Commit数据
        props.put("commit.offsets.on.checkpoint", "true");
        return props;
    }

    /***
     * 构造反序列化器
     * @return .
     * @throws InterruptedException .
     * @param rainbowUtils
     */
    private static AvroMessageDeserializationSchema getDeserializationSchema(RainbowUtils rainbowUtils)
            throws InterruptedException, JsonProcessingException {
        HashMap<String, InfraConfManager.DstTBLInfo> patternMap = new HashMap<>();

        String odsDatabase = rainbowUtils.getStringValue("conf", odsDatabaseKey);
        ObjectNode jsonValue = rainbowUtils.getJSONValue("conf", consumerTablesKey);
        Iterator<String> fieldNames = jsonValue.fieldNames();
        while (fieldNames.hasNext()) {
            String name = fieldNames.next();
            // 目标表信息
            InfraConfManager.DstTBLInfo dstTBLInfo = new InfraConfManager.DstTBLInfo();
            dstTBLInfo.dstTableName = "ods_" + name;
            dstTBLInfo.dstDatabase = odsDatabase;
            ArrayList<String> eqFields = new ArrayList<>();
            JsonNode eqFieldsJson = jsonValue.get(name);
            for (JsonNode node : eqFieldsJson) {
                eqFields.add(node.asText());
            }
            dstTBLInfo.eqFields = eqFields;
            System.out.println(String.format("patternMap put info: %s, %s", name, dstTBLInfo));
            patternMap.put(name, dstTBLInfo);
        }

        // 构造patternTableMap
        ConcurrentIcebergTableLoader loader = ConcurrentIcebergTableLoader.fromHiveDB(odsDatabase);
        patternMap.forEach((key, value) -> loader.addTable(key, value.dstTableName));

        loader.load().toTuple3List().forEach(value -> {
            InfraConfManager.DstTBLInfo l2OdsInfo = patternMap.get(value.f0);
            l2OdsInfo.schema = new Tuple3<>(value.f1, value.f2, IcebergCatalogReader.getTableRowType(value.f1));
            patternMap.put(value.f0, l2OdsInfo);
        });

        // 构造反序列化器
        AvroMessageDeserializationSchema deserializationSchema = new AvroMessageDeserializationSchema(true);
        patternMap.forEach((key, v) -> deserializationSchema.addPatternAndConverter(
                        key,
                        new RowDataMessageConverter(
                                v.schema.f2,
                                Boolean.parseBoolean(rainbowUtils
                                        .getStringValue(SourceKafkaPropGroup, "PayloadCompressed")),
                                v.dstDatabase
                        )
                )
        );
        return deserializationSchema;
    }
}
