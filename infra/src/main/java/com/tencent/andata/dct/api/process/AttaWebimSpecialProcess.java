package com.tencent.andata.dct.api.process;

import com.tencent.andata.utils.DateFormatUtils;
import com.tencent.andata.utils.JSONUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.TimestampData;

public class AttaWebimSpecialProcess extends BaseSpecialProcess {

    public AttaWebimSpecialProcess() {
        jsonUtils = new JSONUtils();
    }

    // ODS数据落库前的特殊处理
    @Override
    public ObjectNode handle(ObjectNode messageValue, RowData rowData) {
        // 展平操作
        ObjectNode result = jsonUtils.flatten(messageValue);
        // 获取消息队列时间
        TimestampData timeData = rowData.getTimestamp(3, 6);
        result.put("ftime", DateFormatUtils.timestampToString(timeData.toTimestamp().getTime()));
        return result;
    }
}