package com.tencent.andata.dct.dts.function;

import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.struct.avro.message.MessageType;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.RowDataConverter;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericRecord;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.formats.avro.AvroSerializationSchema;
import org.apache.flink.formats.avro.RowDataToAvroConverters;
import org.apache.flink.formats.avro.typeutils.AvroSchemaConverter;
import org.apache.flink.table.types.logical.RowType;

import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Map;

/**
 * 将Tuple2数据封装成Message
 */
public class MessageEncodeFunction implements MapFunction<MessageRowData, Message> {
    // Avro Schema映射
    private HashMap<String, Schema> tableAvroSchemaMap;
    // Record序列化器映射
    private HashMap<String, AvroSerializationSchema<GenericRecord>> tableSerializationSchemaMap;
    // RowData Converter映射
    private HashMap<String, RowDataToAvroConverters.RowDataToAvroConverter> tableConverterMap;
    // RowData Encoder映射
    private HashMap<String, RowDataConverter> encoderHashMap;

    /**
     * encode
     * @param tableRowTypeMap
     */
    public MessageEncodeFunction(HashMap<String, RowType> tableRowTypeMap) {
        tableAvroSchemaMap = new HashMap<>();
        tableSerializationSchemaMap = new HashMap<>();
        tableConverterMap = new HashMap<>();
        encoderHashMap = new HashMap<>();
        for (Map.Entry<String, RowType> entry : tableRowTypeMap.entrySet()) {
            String tableName = entry.getKey();
            RowType rowType = entry.getValue();
            Schema schema = AvroSchemaConverter.convertToSchema(rowType);
            tableAvroSchemaMap.put(
                    tableName,
                    schema
            );
            tableSerializationSchemaMap.put(
                    tableName,
                    AvroSerializationSchema.forGeneric(schema)
            );
            tableConverterMap.put(
                    tableName,
                    RowDataToAvroConverters.createConverter(rowType)
            );
            encoderHashMap.put(
                    tableName,
                    new RowDataConverter(rowType)
            );
        }
    }

    /**
     * 序列化流程 RowData -> Record -> Byte[] -> Message
     *
     * @param rowData
     * @throws Exception
     */
    @Override
    public Message map(MessageRowData rowData) throws Exception {
        String tableName = rowData.getAttr().getSrcTable().getTableName();
        // 根据TableName获取RowData的Encoder
        RowDataConverter rowDataConverter = encoderHashMap.get(tableName);
        byte[] bytesData = rowDataConverter.serializeRowDataToBytes(rowData);
        // 这里序列化RowData只会序列化数据，不会序列化Attr，因此TableName还是需要放在Message里的
        // 封装Message
        return new Message(
                tableName,
                System.currentTimeMillis(),
                ByteBuffer.wrap(bytesData),
                MessageType.ROW_DATA
        );
    }

}