package com.tencent.andata.dct.dts.consumer.convert;

import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;

import java.io.Serializable;
import java.text.ParseException;
import java.util.HashMap;

public interface AnsearchConvert extends Serializable {

    HashMap<String, Object> convertAnSearchReqBodyJson(
            MessageRowData rowData) throws JsonProcessingException, ParseException;
}
