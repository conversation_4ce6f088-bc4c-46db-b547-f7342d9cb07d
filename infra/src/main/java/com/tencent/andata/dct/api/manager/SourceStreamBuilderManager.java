package com.tencent.andata.dct.api.manager;

import com.google.common.base.Preconditions;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.dct.api.source.kafka.factory.KafkaStreamFactory;


import lombok.Builder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.data.RowData;

import java.io.Serializable;

@Builder
public class SourceStreamBuilderManager implements Serializable {

    InfraConf infraConf;
    private StreamExecutionEnvironment env;
    private final int kafkaParallelism = 2;

    public void checkNotNull() {
        Preconditions.checkNotNull(env);
    }

    /***
     * stream union
     * @return ds
     * @throws Exception .
     */
    public DataStream<RowData> unionStreams() throws Exception {
        checkNotNull();
        // Kafka Source streams
        // TODO: 这里后面可以也一起使用InfraUtils来进行构建
        DataStream<RowData> kafkaSource = KafkaStreamFactory.builder()
                .env(env)
                .kafkaConfigList(infraConf.reportPlatformConf.kafkaConfList)
                .convertType(infraConf.nrtConf.getL1OdsRowType())
                .parallelism(kafkaParallelism)
                .build()
                .getKafkaSourceStreamFromConf();
        return kafkaSource;
    }
}