package com.tencent.andata.dct.dts.deserializer;

import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.connectors.kafka.KafkaDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.Iterator;

public class KafkaDataWithMetaDeserializer
        implements KafkaDeserializationSchema<Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]>> {
    private static final Logger logger = LoggerFactory.getLogger(KafkaDataWithMetaDeserializer.class);
    private static final String partitionSeqKey = "ps";
    private static final String ShardIdKey = "ShardId";

    public KafkaDataWithMetaDeserializer() {
    }

    @Override
    public void open(DeserializationSchema.InitializationContext context) throws Exception {
        KafkaDeserializationSchema.super.open(context);
    }

    @Override
    public boolean isEndOfStream(Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]> o) {
        return false;
    }


    @Override
    public Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]> deserialize(
            ConsumerRecord<byte[], byte[]> record
    ) throws Exception {
        KafkaMetadata kafkaMetadata = new KafkaMetadata(
                record.partition(),
                getPartitionSeq(record),
                getShardId(record)
        );
        return new Tuple2<>(kafkaMetadata, record.value());
    }

    @Override
    public void deserialize(
            ConsumerRecord<byte[], byte[]> message,
            Collector<Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]>> out
    ) throws Exception {
        KafkaDeserializationSchema.super.deserialize(message, out);
    }

    @Override
    public TypeInformation<Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]>> getProducedType() {
        return TypeInformation.of(new TypeHint<Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]>>() {
        });
    }

    public class KafkaMetadata implements Serializable {
        public Integer partition;
        public long partitionSeq;
        public String sharedId;

        public KafkaMetadata(Integer partition, long partitionSeq, String sharedId) {
            this.partition = partition;
            this.partitionSeq = partitionSeq;
            this.sharedId = sharedId;
        }
    }

    /**
     * 获取消息所在分区的唯一ID值，这个值在同一个分区里是单调递增的
     *
     * @param msg Kafka消息
     * @return
     */
    private long getPartitionSeq(ConsumerRecord<byte[], byte[]> msg) {
        Iterator<Header> it = msg.headers().headers(partitionSeqKey).iterator();
        if (it.hasNext()) {
            Header h = it.next();
            return Long.parseUnsignedLong(new String(h.value()));
        }
        throw new IllegalStateException(partitionSeqKey + " does not exists");
    }

    /**
     * 获取TDSQL的分片ID
     *
     * @param msg Kafka消息
     * @return
     */
    private static String getShardId(ConsumerRecord<byte[], byte[]> msg) {
        Iterator<Header> it = msg.headers().headers(ShardIdKey).iterator();
        if (it.hasNext()) {
            Header h = it.next();
            return new String(h.value());
        }
        throw new IllegalStateException(ShardIdKey + " does not exists");
    }

}