package com.tencent.andata.dct.api.distributor.iceberg;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.dispatcher.cdc.sink.IcebergSinkActuator;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.cdc.conf.builder.WriteTypeEnum;
import com.tencent.andata.utils.operator.map.RowDataSplitCleanMap;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.data.RowData;
import org.apache.flink.util.OutputTag;
import org.apache.flink.util.Preconditions;

import java.util.HashMap;
import org.apache.iceberg.Table;
import org.apache.iceberg.flink.TableLoader;

/**
 * Iceberg分发入库
 * 只负责数据清洗+分发入库功能
 */
public class IcebergDistributorV2 {
    // 这里Tuple3中第一个String是outputKey，不是tableName!!!
    public HashMap<String, InfraConfManager.DstTBLInfo> patternTableMap;
    public SingleOutputStreamOperator<RowData> dispatcherDataStream;

    /***
     * 表数据分发
     */
    public void distribute() {
        patternTableMap.forEach((pattern, dstTBLInfo) -> {
            // 根据Iceberg Table获取DB和TableName
            boolean hasPKFields = !dstTBLInfo.eqFields.isEmpty();
            // 根据table获取入库模式
            WriteTypeEnum writeTypeEnum = hasPKFields ? WriteTypeEnum.UPSERT : WriteTypeEnum.APPEND;
            // Sink流
            final SingleOutputStreamOperator<RowData> sinkStream = dispatcherDataStream
                    // 获取侧流
                    .getSideOutput(new OutputTag<RowData>(pattern) {
                    })
                    // TODO: 数据清洗这里后面塞到最前面，这样可以减少Task数量
                    .map(
                            new RowDataSplitCleanMap(
                                    CDCUtils.convertRowTypeToFieldGetterTupleList(dstTBLInfo.schema.f2)
                            )
                    );
            Table tbl = dstTBLInfo.schema.f0;
            TableLoader tblLoader = dstTBLInfo.schema.f1;
            new IcebergSinkActuator(
                    tbl,
                    tblLoader,
                    writeTypeEnum,
                    // TODO：待改造
                    tbl.spec().isPartitioned() ? 3 : 1,
                    dstTBLInfo.eqFields
            ).sink(sinkStream, null);
        });
    }

    public static IcebergDistributorBuilder builder() {
        return new IcebergDistributorBuilder();
    }

    /**
     * Builder
     */
    public static class IcebergDistributorBuilder {
        public HashMap<String, InfraConfManager.DstTBLInfo> patternTableMap;
        public SingleOutputStreamOperator<RowData> dispatcherDataStream;
        public String dstDatabase;


        public IcebergDistributorBuilder setDstDatabase(String dstDatabase) {
            this.dstDatabase = dstDatabase;
            return this;
        }

        public IcebergDistributorBuilder setPatternTableMap(
                HashMap<String, InfraConfManager.DstTBLInfo> patternTableMap
        ) {
            this.patternTableMap = patternTableMap;
            return this;
        }

        public IcebergDistributorBuilder setDispatcherDataStream(
                SingleOutputStreamOperator<RowData> dispatcherDataStream
        ) {
            this.dispatcherDataStream = dispatcherDataStream;
            return this;
        }

        public IcebergDistributorV2 build() throws InterruptedException {
            Preconditions.checkNotNull(dispatcherDataStream);
            Preconditions.checkNotNull(dstDatabase);
            IcebergDistributorV2 icebergDistributor = new IcebergDistributorV2();
            icebergDistributor.patternTableMap = patternTableMap;
            icebergDistributor.dispatcherDataStream = dispatcherDataStream;
            return icebergDistributor;
        }
    }
}