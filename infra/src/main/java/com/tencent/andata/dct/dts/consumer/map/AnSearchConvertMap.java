package com.tencent.andata.dct.dts.consumer.map;

import com.tencent.andata.dct.dts.consumer.convert.AnsearchConvert;
import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.flink.api.common.functions.MapFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

public class AnSearchConvertMap implements MapFunction<MessageRowData, HashMap<String, Object>> {

    private final HashMap<String, AnsearchConvert> map;
    private final Logger log = LoggerFactory.getLogger(AnSearchConvertMap.class);

    public AnSearchConvertMap(HashMap<String, AnsearchConvert> map) {
        this.map = map;
    }

    @Override
    public HashMap<String, Object> map(MessageRowData rowData) throws Exception {
        String tableName = rowData.getAttr().getSrc().getTableName();
        AnsearchConvert convert = map.get(tableName);
        if (convert == null) {
            log.error("table {} has no convert", tableName);
            return null;
        }
        HashMap<String, Object> reqBody = convert.convertAnSearchReqBodyJson(rowData);
        return reqBody;
    }
}
