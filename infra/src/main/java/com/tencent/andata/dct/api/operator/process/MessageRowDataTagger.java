package com.tencent.andata.dct.api.operator.process;

import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import java.util.List;
import org.apache.flink.table.data.RowData;

import java.util.ArrayList;

public class MessageRowDataTagger extends BaseInjectMapProcessFunction<MessageRowData, RowData> {

    /**
     * 将MessageRowData根据元数据中的table信息分发侧流入库
     *
     * @param tableKeyList
     */
    public MessageRowDataTagger(List<String> tableKeyList) {
        super(tableKeyList);
    }

    /**
     * tableName为key值
     *
     * @param in Input
     * @return tableName
     */
    @Override
    protected String getKey(MessageRowData in) {
        MessageRowDataAttr attr = in.getAttr();
        return attr.getSrcTable().getTableName();
    }

    @Override
    protected RowData getValue(MessageRowData in) {
        return in;
    }

}