package com.tencent.andata.dct.api.source.pulsar.source;

import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.state.CheckpointListener;

import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.table.data.RowData;
import org.apache.pulsar.client.api.AuthenticationFactory;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.MessageId;
import org.apache.pulsar.client.api.PulsarClient;
import org.apache.pulsar.client.api.PulsarClientException;
import org.apache.pulsar.client.api.SubscriptionInitialPosition;
import org.apache.pulsar.client.api.SubscriptionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

// https://iwiki.woa.com/pages/viewpage.action?pageId=613587661 内部使用Pulsar
public class BasePulsarSource implements SourceFunction<RowData>, CheckpointListener {

    protected static final Logger LOG = LoggerFactory.getLogger(BasePulsarSource.class);
    protected static Consumer<byte[]> pulsarConsumer;

    protected String pulsarServiceUrl;
    protected String pulsarTopic;
    protected String pulsarToken;
    protected String pulsarSubscriptionName;
    protected MessageId currentOffset;
    protected DeserializationSchema<RowData> deserializer;
    protected volatile boolean running = false;

    public BasePulsarSource(DeserializationSchema<RowData> deserializer) {
        this.deserializer = deserializer;
    }

    @Override
    public void notifyCheckpointComplete(long l) throws Exception {
        if (currentOffset != null) {
            // 解决第一次Checkpoint没数据导致的报错
            pulsarConsumer.acknowledgeCumulative(currentOffset);
        }
    }

    public BasePulsarSource setPulsarToken(String pulsarToken) {
        this.pulsarToken = pulsarToken;
        return this;
    }

    public BasePulsarSource setPulsarTopic(String pulsarTopic) {
        this.pulsarTopic = pulsarTopic;
        return this;
    }

    public BasePulsarSource setPulsarServiceURL(String pulsarServiceURL) {
        pulsarServiceUrl = pulsarServiceURL;
        return this;
    }

    public BasePulsarSource setPulsarSubscriptionName(String pulsarSubscriptionName) {
        this.pulsarSubscriptionName = pulsarSubscriptionName;
        return this;
    }

    protected Consumer<byte[]> buildPulsarConsumer() throws PulsarClientException {

        return PulsarClient.builder()
                // ip:port 替换成路由ID，位于【集群管理】接入点列表
                .serviceUrl(pulsarServiceUrl)
                // 替换成角色密钥，位于【角色管理】页面
                .authentication(AuthenticationFactory.token(pulsarToken))
                .build()
                .newConsumer()
                // topic完整路径，格式为persistent://集群（租户）ID/命名空间/Topic名称，从【Topic管理】处复制
                .topic(pulsarTopic)
                // 需要在控制台Topic详情页创建好一个订阅，此处填写订阅名
                .subscriptionName(pulsarSubscriptionName)
                // 声明消费模式为灾备模式
                .subscriptionType(SubscriptionType.Failover)
                // 配置从最早开始消费，否则可能会消费不到历史消息
                .subscriptionInitialPosition(SubscriptionInitialPosition.Earliest)
                .subscribe();
    }

    /**
     * 数据处理
     */
    @Override
    public void run(SourceContext<RowData> sourceContext) throws IOException {
        final Object lock = sourceContext.getCheckpointLock();

        running = true;

        pulsarConsumer = buildPulsarConsumer();

        LOG.info("Pulsar base Consumer Running.");

        while (running) {
            Message<byte[]> msg = pulsarConsumer.receive(3, TimeUnit.SECONDS);
            if (msg != null) {
                // TODO 如果在Source处进行清洗，可能会导致性能问题。后续单独拆多个Map来做这个操作进而提升性能（如果有性能瓶颈的话）
                byte[] bytes =
                        new String(msg.getValue(), StandardCharsets.UTF_8)
                                .replaceAll("[\\u0000-\\u001f]|\\\\r|\\\\n|\\p{C}", "")
                                .getBytes(StandardCharsets.UTF_8);

                RowData rowData;
                // 如果下游消费失败，是不是需要做nack？
                try {
                    rowData = deserializer.deserialize(bytes);
                } catch (IOException ignored) {
                    rowData = null;
                }

                // TODO 如果返回为null，那么代表反序列化失败，数据需要入异常数据表
                if (rowData != null) {
                    sourceContext.collect(rowData);
                    synchronized (lock) {
                        // 获取当前Offset，等待 Checkpoint 时提交
                        currentOffset = msg.getMessageId();
                    }
                }
            }
        }
        try {
            Thread.sleep(5000);
        } catch (InterruptedException ignored) {
            return;
        }

        LOG.info("Pulsar base Consumer Exit.");
    }

    @Override
    public void cancel() {
        LOG.info("Pulsar base Consumer receive exit signal, set flag.");
        running = false;
    }
}