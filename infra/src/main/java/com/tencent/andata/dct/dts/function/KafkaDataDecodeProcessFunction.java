package com.tencent.andata.dct.dts.function;

import com.tencent.andata.dct.dts.deserializer.KafkaDataWithMetaDeserializer;
import com.tencent.andata.struct.protobuf.dts.SubscribeDataProto;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import com.tencent.andata.utils.struct.DatabaseEnum;

import java.io.ByteArrayOutputStream;

import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.Double;
import java.lang.Float;
import java.lang.Long;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 因为在一条Kafka消息中可能存不下一个Entry，所以可能会拆成多个kafka消息
 * 通过index，和total去判断当前Kafka消息是否是被切断的数据以及所在原数据中的index
 * 通过根据Partition进行Keyby，保证被切开的数据能在算子中被连续消费处理
 * 通过KeyedState来保存之前切片的数据，当最后一个切片的数据到来后，将数据组合成Entry再进行处理
 */
public class KafkaDataDecodeProcessFunction
        extends KeyedProcessFunction<Integer, Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]>,
        ArrayList<MessageRowData>> {
    private static final Logger logger = LoggerFactory.getLogger(KafkaDataWithMetaDeserializer.class);
    // TDSQL 分片对应的上一个消息的id
    private transient MapState<String, Long> shardLastSeqMap;
    // TDSQL 分片对应的数据，如果数据进行切片了的话，则数据缓存在这里
    private transient MapState<String, byte[]> shardMsgMap;
    // 表RowType映射，用于将数据转成RowData
    private final HashMap<String, RowType> tableRowTypeMap;

    public KafkaDataDecodeProcessFunction(HashMap<String, RowType> tableRowTypeMap) {
        this.tableRowTypeMap = tableRowTypeMap;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        MapStateDescriptor<String, byte[]> shardMsgMapDescriptor = new MapStateDescriptor<String, byte[]>(
                "shardMsgMap",
                String.class,
                byte[].class
        );
        MapStateDescriptor<String, Long> shardLastSeqMapDescriptor = new MapStateDescriptor<String, Long>(
                "shardLastSeqMap",
                String.class,
                Long.class
        );

        this.shardLastSeqMap = getRuntimeContext().getMapState(shardLastSeqMapDescriptor);
        this.shardMsgMap = getRuntimeContext().getMapState(shardMsgMapDescriptor);
    }

    @Override
    public void processElement(
            Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]> kafkaMetadataTuple2,
            KeyedProcessFunction<
                    Integer,
                    Tuple2<KafkaDataWithMetaDeserializer.KafkaMetadata, byte[]>,
                    ArrayList<MessageRowData>
                    >.Context context,
            Collector<ArrayList<MessageRowData>> collector
    ) throws Exception {
        final byte[] bytesData = kafkaMetadataTuple2.f1;
        final KafkaDataWithMetaDeserializer.KafkaMetadata kafkaMetadata = kafkaMetadataTuple2.f0;
        long ps = kafkaMetadata.partitionSeq;
        logger.info(String.format("partitionSeq:%s\n", ps));
        String shardId = kafkaMetadata.sharedId;
        logger.info(String.format("shardId:%s\n", shardId));
        Long shardLastSeq = shardLastSeqMap.get(shardId);
        // 第一条消息来的时候
        if (shardLastSeq == null || ps == 1) {
            shardLastSeqMap.put(shardId, (long) 1);
        } else if (ps <= shardLastSeq) {
            // 因为消息ID(ps)是单调递增的，所以如果下一条数据的ID小于之前的最大ID的话，则表示数据重复了，扔掉。
            logger.info(String.format("duplicated message: %s:%s:%d\n", shardId, kafkaMetadata.partition, ps));
            return;
        } else if (ps != shardLastSeq + 1 && shardLastSeq != 0) {
            // 数据不连续则报错
            throw new IllegalStateException(
                    "the partition seq discontinuous, last: " + shardLastSeq + ", current: " + ps);
        }
        // 更新分片对应的消息Seq位置
        shardLastSeqMap.put(shardId, ps);

        SubscribeDataProto.Envelope envelope = SubscribeDataProto.Envelope.parseFrom(bytesData);
        if (1 != envelope.getVersion()) {
            throw new IllegalStateException(String.format("unsupported version: %d", envelope.getVersion()));
        }
        // 一条Kafka数据是完整的数据
        ByteArrayOutputStream completeMsg = new ByteArrayOutputStream();
        // 一个BinlogEvent被切片成多个Kafka消息，用于临时缓存该条Kafka数据
        ByteArrayOutputStream middleMsg;

        if (1 == envelope.getTotal()) {
            // 总共只有一个包则表示消息没有被切片，直接处理
            completeMsg = new ByteArrayOutputStream();
            envelope.getData().writeTo(completeMsg);
        } else {
            // 消息被切片
            // 先拿取到该分片下的目前所获取的byte数据
            byte[] shardMsg = shardMsgMap.get(shardId);
            middleMsg = new ByteArrayOutputStream();
            if (null != shardMsg) {
                // 如果不是第一个切片的话，就把之前的切片数据放到OutPut中
                middleMsg.write(shardMsg);
            }
            // 将本次的数据写入OutPut中
            envelope.getData().writeTo(middleMsg);
            // 将Byte数据更新进State
            shardMsgMap.put(shardId, middleMsg.toByteArray());
        }
        logger.info(String.format("index:%d, total: %d\n", envelope.getIndex(), envelope.getTotal()));
        // 不是最后一个切片的话直接跳过
        if (envelope.getIndex() < envelope.getTotal() - 1) {
            return;
        }

        SubscribeDataProto.Entries entries;
        if (1 == envelope.getTotal()) {
            // 没有切片的情况，直接对数据进行解析
            entries = SubscribeDataProto.Entries.parseFrom(completeMsg.toByteArray());
        } else {
            // 获取分片的之前累计缓存的数据进行解析
            entries = SubscribeDataProto.Entries.parseFrom(shardMsgMap.get(shardId));
            // 清除缓存
            shardMsgMap.remove(shardId);
        }
        ArrayList<MessageRowData> rows = new ArrayList<MessageRowData>();
        // 打包成RowData发送下游
        for (SubscribeDataProto.Entry entry : entries.getItemsList()) {
            // 只解析DML数据 & 数据源是指定的表
            if (entry.getHeader().getMessageType().equals(SubscribeDataProto.MessageType.DML)
                    && this.tableRowTypeMap.containsKey(entry.getHeader().getTableName())) {
                SubscribeDataProto.DMLEvent dmlEvent = entry.getEvent().getDmlEvent();
                ArrayList<MessageRowData> rowFromDmlEvent = getRowDataFromDmlEvent(
                        dmlEvent,
                        entry.getHeader().getTableName()
                );
                rows.addAll(rowFromDmlEvent);
            }
        }
        if (!rows.isEmpty()) {
            collector.collect(rows);
        }
    }

    /**
     * 解析DMLEvent
     *
     * @param event DMLEvent
     * @param table 表名
     * @return
     */
    private ArrayList<MessageRowData> getRowDataFromDmlEvent(
            SubscribeDataProto.DMLEvent event,
            String table
    ) throws Exception {
        ArrayList<MessageRowData> rows = new ArrayList<MessageRowData>();
        switch (event.getDmlEventType()) {
            case INSERT:
                // 一个DML事件可能涉及多行数据变更
                for (SubscribeDataProto.RowChange rowChange : event.getRowsList()) {
                    Row row = Row.withNames(RowKind.INSERT);
                    // 遍历列信息获取数据
                    for (int i = 0; i < event.getColumnsCount(); i++) {
                        // 获取字段名
                        String name = event.getColumns(i).getName();
                        // 获取DML变更后的字段值
                        SubscribeDataProto.Data newColumn = rowChange.getNewColumns(i);
                        row.setField(name, transData(newColumn));
                    }
                    rows.add(transformRowToRowData(row, table));
                }
                return rows;
            case DELETE:
                // 一个DML事件可能涉及多行数据变更
                for (SubscribeDataProto.RowChange rowChange : event.getRowsList()) {
                    Row row = Row.withNames(RowKind.DELETE);
                    // 遍历列信息获取数据
                    for (int i = 0; i < event.getColumnsCount(); i++) {
                        // 获取字段名
                        String name = event.getColumns(i).getName();
                        // 获取DML变更前的字段值
                        SubscribeDataProto.Data oldColumn = rowChange.getOldColumns(i);
                        row.setField(name, transData(oldColumn));
                    }
                    rows.add(transformRowToRowData(row, table));
                }
                return rows;
            case UPDATE:
                // 一个DML事件可能涉及多行数据变更
                for (SubscribeDataProto.RowChange rowChange : event.getRowsList()) {
                    Row beforeRow = Row.withNames(RowKind.UPDATE_BEFORE);
                    Row afterRow = Row.withNames(RowKind.UPDATE_AFTER);
                    // 遍历列信息获取数据
                    for (int i = 0; i < event.getColumnsCount(); i++) {
                        // 获取字段名
                        String name = event.getColumns(i).getName();
                        // 获取DML变更前的字段值
                        SubscribeDataProto.Data olaColumn = rowChange.getOldColumns(i);
                        beforeRow.setField(name, transData(olaColumn));
                        // 获取DML变更后的字段值
                        SubscribeDataProto.Data newColumn = rowChange.getNewColumns(i);
                        afterRow.setField(name, transData(newColumn));
                    }
                    rows.add(transformRowToRowData(beforeRow, table));
                    rows.add(transformRowToRowData(afterRow, table));
                }
                return rows;
            case UNRECOGNIZED:
            default:
                return null;
        }
    }

    /**
     * 解析PB中的数据
     *
     * @param data Data
     * @return
     */
    private Object transData(SubscribeDataProto.Data data) throws Exception {
        switch (data.getDataType()) {
            case INT8:
            case UINT8:
            case INT16:
            case UINT16:
            case INT32:
                return Integer.valueOf(data.getSv());
            case INT64:
            case UINT32:
            case UINT64:
                return Long.parseLong(data.getSv());
            case FLOAT32:
                return Float.valueOf(data.getSv());
            case FLOAT64:
                return Double.valueOf(data.getSv());
            case DECIMAL:
                // 需要返回StringData，因为后续要转成RowData
                return StringData.fromString(data.getSv());
            case STRING:
            case BYTES:
                // json字段是二进制数据，需要转成字符串
                return StringData.fromBytes(data.getBv().toByteArray());
            case NA:
            case NIL:
                return null;
            default:
                throw new IllegalStateException("unsupported data type");
        }
    }

    /**
     * Row -> RowData 根据字段值进行映射生成RowData，保证RowData值顺序和RowType一致
     *
     * @param row   row
     * @param table 表名
     * @return RowData
     */
    private MessageRowData transformRowToRowData(Row row, String table) {
        MessageRowData rowData = new MessageRowData(row.getKind(), row.getArity());
        MessageRowDataAttr attr = new MessageRowDataAttr();
        attr.setSrcTable(
                new TableIdentifier(
                        DatabaseEnum.MYSQL,
                        "",
                        table
                )
        );
        rowData.setAttr(attr);
        RowType rowType = tableRowTypeMap.get(table);
        for (int i = 0; i < rowType.getFields().size(); i++) {
            // 获取字段
            RowType.RowField rowField = rowType.getFields().get(i);
            // 通过字段名在Row中获取数据
            rowData.setField(i, row.getField(rowField.getName()));
        }
        return rowData;
    }
}