package com.tencent.andata.dct.api.distributor.dbus;

import com.tencent.andata.conf.manager.struct.MQDistributeConf;
import java.util.Properties;
import org.apache.flink.streaming.api.datastream.DataStream;

public abstract class DBusDistributor<T> {
    protected final DataStream<T> ds;

    protected final MQDistributeConf dConf;

    protected DBusDistributor(MQDistributeConf dConf, DataStream<T> ds) {
        this.dConf = dConf;
        this.ds = ds;
    }

    public void distribute() {
        // TODO: EXACTLY_ONCE会因为Kafka版本过低而出现
        //  org.apache.kafka.common.errors.UnsupportedVersionException: Attempted to write a non-default producerId at
        //  version 0 这个报错，暂时使用AT-LEAST ONCE
        Properties properties = new Properties();
        // TODO: 多层嵌套代表数据引用层级过深，要重新抽象数据
        properties.setProperty("bootstrap.servers", dConf.dBusConf.dBusMQConf.entryPoint);
        properties.setProperty("transaction.timeout.ms", "600000");
        properties.setProperty("max.request.size", "8388608");
        this.injectSinkToDS(properties);
    }

    public String getUid() {
        return String.format("kafka-sink-%s", dConf.dBusConf);
    }

    public abstract void injectSinkToDS(Properties kafkaProperties) throws RuntimeException;

    public abstract static class Builder<T> {
        MQDistributeConf dConf;
        DataStream<T> ds;

        public Builder<T> setDs(DataStream<T> ds) {
            this.ds = ds;
            return this;
        }

        public Builder<T> setDistributeConf(MQDistributeConf dConf) {
            this.dConf = dConf;
            return this;
        }

        public abstract DBusDistributor<T> build();
    }
}