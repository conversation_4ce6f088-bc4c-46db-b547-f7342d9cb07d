package com.tencent.andata.dct.dts.consumer.sink;

import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class HttpSink extends RichSinkFunction<HashMap<String, Object>> implements CheckpointedFunction {
    private ObjectMapper objectMapper = new ObjectMapper();
    private List<HashMap<String, Object>> bufferedData = new ArrayList<>();
    private int count = 0;
    private static final Logger log = LoggerFactory.getLogger(HttpSink.class);
    private final String url;
    private final String user;
    private final String userKey;
    private final String dataSet;
    private final Integer dataType;
    private final Integer batchSize;

    public HttpSink(String url,
                    String user,
                    String userKey,
                    String dataSet,
                    Integer dataType,
                    Integer batchSize) {
        this.url = url;
        this.user = user;
        this.userKey = userKey;
        this.dataSet = dataSet;
        this.dataType = dataType;
        this.batchSize = batchSize;
    }

    @Override
    public void invoke(HashMap<String, Object> value, Context context) throws Exception {
        count++;
        bufferedData.add(value);
        if (count == batchSize) {
            log.info("send request size {}", bufferedData.size());
            sendRequest(bufferedData);
            bufferedData.clear();
            count = 0;
        }
    }

    private void sendRequest(
            List<HashMap<String, Object>> datas) throws JsonProcessingException {
        // 创建 HTTP POST 请求
        HttpPost httpPost = new HttpPost(this.url);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("user", this.user);
        httpPost.setHeader("user_key", this.userKey);

        HashMap<String, Object> reqBody = new HashMap<String, Object>() {{
            put("dataset", dataSet);
            put("data_type", dataType);
            put("data", datas);
        }};

        String value = objectMapper.writeValueAsString(reqBody);
        log.info("send post req: {}", value);
        // 设置请求体
        StringEntity entity = new StringEntity(value, "utf-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);

        // 发送请求
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            CloseableHttpResponse response = httpClient.execute(httpPost);
            log.info("response: {}", response.toString());
            String content = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.info("response data: {}", content);
        } catch (Exception e) {
            log.error("add data error {}", e.toString());
        }
    }

    @Override
    public void snapshotState(FunctionSnapshotContext functionSnapshotContext) throws Exception {
        if (bufferedData.size() != 0) {
            log.info("send request size {}", bufferedData.size());
            sendRequest(bufferedData);
            bufferedData.clear();
            count = 0;
        }
    }

    @Override
    public void initializeState(FunctionInitializationContext functionInitializationContext) throws Exception {

    }

}
