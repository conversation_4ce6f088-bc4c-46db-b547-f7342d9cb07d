package com.tencent.andata.dct.api.source.kafka.factory;

import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.dct.api.source.kafka.builder.KafkaSourceStreamBuilder;
import com.tencent.andata.dct.api.source.kafka.deserializer.FlinkKafkaDeserializationSchema;

import lombok.Builder;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonRowDataDeserializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Collector;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Optional;

@Builder
public class KafkaStreamFactory {
    private StreamExecutionEnvironment env;
    private ArrayList<DBusConf.KafkaConf> kafkaConfigList;
    private RowType convertType;
    private Integer parallelism;

    /***
     * 根据kafkasource配置生成stream
     * @return .
     * @throws Exception
     */
    public DataStream<RowData> getKafkaSourceStreamFromConf() throws Exception {

        final JsonRowDataDeserializationSchema ODS_DISTRIBUTION_TABLE_DESERIALIZER =
                new JsonRowDataDeserializationSchema(
                        convertType, InternalTypeInfo.of(convertType), false, true, TimestampFormat.SQL);

        // Kafka Source 列表， 可以消费多个Kafka
        ArrayList<DataStream<String>> kafkaSourceList = new ArrayList<>();
        for (DBusConf.KafkaConf kafkaConf : kafkaConfigList) {
            String topic = kafkaConf.topic;
            String address = kafkaConf.entryPoint;
            String groupID = kafkaConf.consumerGroup;

            DataStream<String> source = new KafkaSourceStreamBuilder()
                    .setAddress(address)
                    .setGroupID(groupID)
                    .setTopic(topic)
                    .setEnv(env)
                    .setDeserializationSchema(new FlinkKafkaDeserializationSchema())
                    .setParallelism(parallelism)
                    .build();
            kafkaSourceList.add(source);
        }
        // 通过Reduce将所有Kafka Source 流合并成一个
        Optional<DataStream<String>> opt = kafkaSourceList.stream().reduce(DataStream::union);
        assert opt.isPresent();
        // 所有Kafka数据都到一个流里面了，只用对这个流进行处理就行
        return opt.get().flatMap(
                new FlatMapFunction<String, RowData>() {
                    @Override
                    public void flatMap(String str, Collector<RowData> collector) throws Exception {
                        RowData record =
                                ODS_DISTRIBUTION_TABLE_DESERIALIZER.deserialize(
                                        str.getBytes(StandardCharsets.UTF_8));
                        collector.collect(record);
                    }
                });
    }
}