package com.tencent.andata.dct.dts.consumer.map;

import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.rowdata.MessageRowData;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.data.conversion.RowRowConverter;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;

import java.util.List;

public class RowData2RowMap extends ProcessFunction<MessageRowData, Row> {

    private RowRowConverter rowRowConverter;
    private String tableName;
    public TypeInformation<Row> rowTypeInfo;
    public RowType rowType;

    /***
     * RowData2RowMap
     * @param tableIdentifier .
     * @throws TableNotExistException .
     */
    public RowData2RowMap(TableIdentifier tableIdentifier) throws TableNotExistException {
        this.tableName = StringUtils.stripStart(tableIdentifier.getTableName(), "ods_");
        // 定义 JSON 数据的解析规则
        IcebergCatalogReader reader = new IcebergCatalogReader();
        DataType dataType = IcebergCatalogReader.getDataType(
                reader.getTableInstance(
                        tableIdentifier.getDbName(),
                        tableIdentifier.getTableName()
                ));
        // 创建RowData到Row的转换器
        rowRowConverter = RowRowConverter.create(dataType);
        // type info
        RowType rowType = reader
                .getTableRowType(tableIdentifier.getDbName(), tableIdentifier.getTableName());
        this.rowType = rowType;
        int fieldCount = rowType.getFieldCount();
        TypeInformation[] types = new TypeInformation[fieldCount];
        String[] fieldNames = new String[fieldCount];
        List<String> fields = rowType.getFieldNames();
        for (int i = 0; i < fieldCount; i++) {
            LogicalType typeAt = rowType.getTypeAt(i);
            Class<?> type = typeAt.getDefaultConversion();
            types[i] = TypeInformation.of(type);
            fieldNames[i] = fields.get(i);
            System.out.println(String.format("filedName: %s , type: %s", fieldNames[i], types[i]));
        }
        rowTypeInfo = new RowTypeInfo(types, fieldNames);
    }


    @Override
    public void processElement(
            MessageRowData rowData,
            ProcessFunction<MessageRowData, Row>.Context context,
            Collector<Row> collector) throws Exception {
        RowKind rowKind = rowData.getRowKind();
        if (rowKind == RowKind.DELETE || rowKind == RowKind.UPDATE_BEFORE) {
            return;
        }
        if (rowKind == RowKind.UPDATE_AFTER) {
            rowData.setRowKind(RowKind.INSERT);
        }
        TableIdentifier src = rowData.getAttr().getSrc();
        String tableName = src.getTableName();
        if (this.tableName.equals(tableName)) {
            Row row = rowRowConverter.toExternal(rowData);
            collector.collect(row);
        }
    }
}