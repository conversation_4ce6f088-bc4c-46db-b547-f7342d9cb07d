package com.tencent.andata.dct.api.source.kafka.builder;

import com.google.common.base.Preconditions;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.flink.streaming.connectors.kafka.KafkaDeserializationSchema;

import java.io.Serializable;
import java.util.Properties;

public class KafkaSourceStreamBuilder implements Serializable {

    private String address;
    private String groupID;
    private String topic;
    private KafkaDeserializationSchema<String> deserializationSchema;
    private StreamExecutionEnvironment env;
    private Integer parallelism;

    public KafkaSourceStreamBuilder setParallelism(Integer parallelism) {
        this.parallelism = parallelism;
        return this;
    }

    public KafkaSourceStreamBuilder setAddress(String address) {
        this.address = address;
        return this;
    }

    public KafkaSourceStreamBuilder setGroupID(String groupID) {
        this.groupID = groupID;
        return this;
    }

    public KafkaSourceStreamBuilder setTopic(String topic) {
        this.topic = topic;
        return this;
    }

    public KafkaSourceStreamBuilder setEnv(StreamExecutionEnvironment env) {
        this.env = env;
        return this;
    }

    public KafkaSourceStreamBuilder setDeserializationSchema(KafkaDeserializationSchema<String> deserializationSchema) {
        this.deserializationSchema = deserializationSchema;
        return this;
    }

    public DataStream<String> build() throws Exception {
        checkNotNull();
        String uid = String.format("kafka-source-%s-%s-%s", address, topic, groupID);
        return env.addSource(buildNewKafkaSource())
                .uid(uid)
                .name(uid)
                .setParallelism(parallelism);
    }

    private FlinkKafkaConsumerBase<String> buildNewKafkaSource() {
        // Kafka 的配置
        Properties kafkaProperties = new Properties();
        kafkaProperties.setProperty("bootstrap.servers", address);
        kafkaProperties.setProperty("group.id", groupID);
        kafkaProperties.setProperty("isolation.level", "read_committed");

        FlinkKafkaConsumer<String> retConsumer = new FlinkKafkaConsumer<>(
                topic,
                deserializationSchema, kafkaProperties
        );
        return retConsumer
                .setStartFromGroupOffsets()
                .setCommitOffsetsOnCheckpoints(true);
    }

    public void checkNotNull() {
        Preconditions.checkNotNull(address);
        Preconditions.checkNotNull(groupID);
        Preconditions.checkNotNull(topic);
        Preconditions.checkNotNull(env);
        Preconditions.checkNotNull(deserializationSchema);
        Preconditions.checkNotNull(parallelism);
    }
}