package com.tencent.andata.dct.api.operator.map;

import com.tencent.andata.dct.api.process.AttaWebimSpecialProcess;
import com.tencent.andata.dct.api.process.BaseSpecialProcess;
import com.tencent.andata.dct.api.process.StandardProcess;
import com.tencent.andata.dct.api.process.exceptions.TableNameNotExistedException;
import com.tencent.andata.dct.api.model.AbnormalRecord;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.runtime.source.coordinator.SourceCoordinator;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.operators.collect.CollectSinkOperatorCoordinator;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.HashMap;

public class Convert2TableNameDataProcess implements FlatMapFunction<RowData, Tuple2<String, String>> {

    public String exceptionRecordTableName;
    // 标准数据处理
    private StandardProcess standardProcess = new StandardProcess();
    // 需要特殊处理的Table
    private HashMap<String, BaseSpecialProcess> specialProcessMap = new HashMap<>();

    public Convert2TableNameDataProcess(String exceptionRecordTableName) {
        this.exceptionRecordTableName = exceptionRecordTableName;
        // Webim数据需要展平并且添加额外字段
        specialProcessMap.put("ods_im_service_account_backend_data", new AttaWebimSpecialProcess());
        specialProcessMap.put("ods_im_online_customer_service_backend_data", new AttaWebimSpecialProcess());
        specialProcessMap.put("ods_chatops_event_log", new AttaWebimSpecialProcess());
    }

    @Override
    public void flatMap(RowData rowData, Collector<Tuple2<String, String>> collector) {
        StringData tableNameData = rowData.getString(0);
        ArrayList<Tuple2<String, String>> passValueList = new ArrayList<>();

        String tableName = tableNameData != null
                ? tableNameData.toString()
                : null;
        try {
            // 根据表名获取特殊处理类
            if (tableName == null) {
                throw new TableNameNotExistedException();
            }
            BaseSpecialProcess specialProcess = specialProcessMap.get(tableName);
            for (ObjectNode valueObj : standardProcess.handle(rowData)) {
                boolean isSpecialProcess = specialProcess != null;
                passValueList.add(new Tuple2<>(tableName, (
                        isSpecialProcess
                                ? specialProcess.handle(valueObj, rowData)
                                : valueObj
                ).toString()));
            }
        } catch (Exception e) {
            // 处理出错则生成异常数据
            AbnormalRecord abnormalRecord =
                    new AbnormalRecord(rowData, e.toString(), "ODS", tableName);
            passValueList.clear();
            passValueList.add(
                    new Tuple2<>(
                            this.exceptionRecordTableName,
                            abnormalRecord.toObjectNode().toString()));
        }
        passValueList.forEach(collector::collect);
    }
}