package com.tencent.andata.dct.dts.consumer.model;


import com.tencent.andata.dct.dts.consumer.constant.IncidentStatus;
import com.tencent.andata.utils.rowdata.MessageRowData;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.flink.types.RowKind;

@Data
@Builder
public class IncidentInfo {
    @JsonProperty(value = "id")
    private Long id;
    @JsonProperty(value = "tenant_id")
    private Long tenantId;
    @JsonProperty(value = "process_id")
    private String processId;
    @JsonProperty(value = "incident_id")
    private String incidentId;
    private String title;
    private String content;
    private Integer status;
    private Integer priority;
    @JsonProperty(value = "category_id")
    private Integer categoryId;
    @JsonProperty(value = "current_operator")
    private String currentOperator;
    @JsonProperty(value = "service_rate")
    private Integer serviceRate;
    @JsonProperty(value = "service_comment")
    private String serviceComment;
    private String solution;
    private String progress;
    @JsonProperty(value = "customer_id")
    private String customerId;
    private String operators;
    @JsonProperty(value = "staff_group_id")
    private Integer staffGroupId;
    @JsonProperty(value = "is_deleted")
    private Integer isDeleted;
    @JsonProperty(value = "close_time")
    private Long closeTime;
    private String closer;
    @JsonProperty(value = "close_type")
    private Integer closeType;
    private String source;
    private String creator;
    private String updater;
    @JsonProperty(value = "create_time")
    private Long createTime;
    @JsonProperty(value = "update_time")
    private Long updateTime;
    @JsonProperty(value = "should_assign_queue")
    private Integer shouldAssignQueue;
    @JsonProperty(value = "fact_assign_queue")
    private Integer factAssignQueue;
    private String agent;
    @JsonProperty(value = "conversation_id")
    private String conversationId;
    @JsonProperty(value = "extern_update_time")
    private Long externUpdateTime;
    private String tags;
    @JsonProperty(value = "ext_fields")
    private String extFields;
    @JsonProperty(value = "current_assign_time")
    private Long currentAssignTime;

    /***
     * constructFromRowData 根据 rowdata 构建 IncidentInfo
     * @param messageRowData .
     * @return .
     */
    public static IncidentInfo constructFromRowData(MessageRowData messageRowData) {
        int status = messageRowData.getInt(6);
        if (status != IncidentStatus.CLOSEDORDER) {
            return null;
        }
        int isDeleted = 0;
        if (messageRowData.getRowKind().equals(RowKind.DELETE)) {
            isDeleted = 1;
        } else {
            isDeleted = messageRowData.getInt(17);
        }
        return IncidentInfo.builder()
                .id(messageRowData.getLong(0))
                .tenantId(messageRowData.getLong(1))
                .processId(messageRowData.getString(2).toString())
                .incidentId(messageRowData.getString(3).toString())
                .title(messageRowData.getString(4).toString())
                .content(messageRowData.getString(5).toString())
                .status(status)
                .priority(messageRowData.getInt(7))
                .categoryId(messageRowData.getInt(8))
                .currentOperator(messageRowData.getString(9).toString())
                .serviceRate(messageRowData.getInt(10))
                .serviceComment(messageRowData.getString(11).toString())
                .solution(messageRowData.getString(12).toString())
                .progress(messageRowData.getString(13).toString())
                .customerId(messageRowData.getString(14).toString())
                .operators(messageRowData.getString(15).toString())
                .staffGroupId(messageRowData.getInt(16))
                .isDeleted(isDeleted)
                .closeTime(messageRowData.getLong(18))
                .closer(messageRowData.getString(19).toString())
                .closeType(messageRowData.getInt(20))
                .source(messageRowData.getString(21).toString())
                .creator(messageRowData.getString(22).toString())
                .updater(messageRowData.getString(23).toString())
                .createTime(messageRowData.getLong(24))
                .updateTime(messageRowData.getLong(25))
                .shouldAssignQueue(messageRowData.getInt(26))
                .factAssignQueue(messageRowData.getInt(27))
                .agent(messageRowData.getString(28).toString())
                .conversationId(messageRowData.getString(29).toString())
                .externUpdateTime(messageRowData.getLong(30))
                .tags(messageRowData.getField(31) != null ? messageRowData.getString(31).toString() : "")
                .extFields(messageRowData.getField(32) != null ? messageRowData.getString(32).toString() : "")
                .currentAssignTime(messageRowData.getLong(33))
                .build();
    }
}
