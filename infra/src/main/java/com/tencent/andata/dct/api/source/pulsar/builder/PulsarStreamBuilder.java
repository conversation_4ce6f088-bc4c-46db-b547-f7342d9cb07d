package com.tencent.andata.dct.api.source.pulsar.builder;

import com.google.common.base.Preconditions;
import com.tencent.andata.dct.api.source.pulsar.source.BasePulsarSource;
import com.tencent.andata.dct.api.source.pulsar.source.ColdDataStoragePulsarSource;

import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.data.RowData;

import java.io.IOException;
import java.io.Serializable;

public class PulsarStreamBuilder implements Serializable {
    private String url;
    private String subscriptionName;
    private String topic;
    private DeserializationSchema<RowData> deserializationSchema;
    private StreamExecutionEnvironment env;
    private String token;
    private Integer parallelism;

    public PulsarStreamBuilder setParallelism(Integer parallelism) {
        this.parallelism = parallelism;
        return this;
    }

    public PulsarStreamBuilder setTopic(String topic) {
        this.topic = topic;
        return this;
    }

    public PulsarStreamBuilder setUrl(String url) {
        this.url = url;
        return this;
    }

    public PulsarStreamBuilder setToken(String token) {
        this.token = token;
        return this;
    }

    public PulsarStreamBuilder setEnv(StreamExecutionEnvironment env) {
        this.env = env;
        return this;
    }

    public PulsarStreamBuilder setSubscriptionName(String subscriptionName) {
        this.subscriptionName = subscriptionName;
        return this;
    }

    public PulsarStreamBuilder setDeserializationSchema(DeserializationSchema<RowData> deserializationSchema) {
        this.deserializationSchema = deserializationSchema;
        return this;
    }


    public DataStream<RowData> build() throws Exception {
        checkNotNull();
        String uid = String.format("pulsar-source-%s-%s-%s", url, topic, subscriptionName);
        return env.addSource(buildNewPulsarSource(url, token, topic, subscriptionName))
                .uid(uid)
                .name(uid)
                .setParallelism(parallelism);
    }

    private BasePulsarSource buildNewPulsarSource(String url, String token, String topic, String subscriptionName)
            throws IOException {
        return new ColdDataStoragePulsarSource(deserializationSchema)
                .setPulsarServiceURL(url)
                .setPulsarToken(token)
                .setPulsarTopic(topic)
                .setPulsarSubscriptionName(subscriptionName);
    }

    public void checkNotNull() {
        Preconditions.checkNotNull(url);
        Preconditions.checkNotNull(subscriptionName);
        Preconditions.checkNotNull(topic);
        Preconditions.checkNotNull(env);
        Preconditions.checkNotNull(deserializationSchema);
        Preconditions.checkNotNull(token);
        Preconditions.checkNotNull(parallelism);
    }
}