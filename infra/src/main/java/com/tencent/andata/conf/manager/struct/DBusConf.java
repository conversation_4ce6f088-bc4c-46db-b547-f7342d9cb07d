package com.tencent.andata.conf.manager.struct;

import java.util.Properties;

/**
 * 数据总线配置，包含写入、读取数据总线的全部信息
 */
public class DBusConf {
    public static class KafkaConf {
        public String topic;
        public String entryPoint;
        public String consumerGroup;

        public Properties toProperties() {
            Properties kafkaProperties = new Properties();
            kafkaProperties.setProperty("bootstrap.servers", entryPoint);
            kafkaProperties.setProperty("group.id", consumerGroup);
            kafkaProperties.setProperty("isolation.level", "read_committed");
            return kafkaProperties;
        }
    }

    public static class PulsarConf {
        public String topic;
        public String entryPoint;
        public String subscriptionName;
        public String token;
    }

    public KafkaConf dBusMQConf;
    // 正文是否被包装（是否使用Avro包装头）
    // 暂时还没有被使用，需要NRT & L1 ODS二合一之后才行
    public boolean payloadWrapped = false;
    // 正文是否压缩（是否使用lz4压缩）
    public boolean payloadCompressed = false;

    @Override
    public String toString() {
        return String.format("%s-%s", dBusMQConf.entryPoint, dBusMQConf.topic);
    }
}