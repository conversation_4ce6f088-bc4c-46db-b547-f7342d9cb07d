package com.tencent.andata.conf.manager.extractor;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.NRTConf;
import com.tencent.andata.conf.manager.struct.OLAPConf;
import com.tencent.andata.conf.manager.struct.ReportPlatformConf;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Arrays;

/**
 * 上报平台接入的数据的DataBus Extractor
 */
public class ExternalDBusRainbowExtractor implements BaseExtractor {

    private static final String KAFKA_SOURCE_GROUP = "source.mq.kafka";
    private static final String ODS_ICEBERG_SINK_GROUP = "sink.iceberg.ods";
    private static final String ODS_ICEBERG_PK_GROUP = "sink.iceberg.pks";
    private static final String L2_ODS_DATA_BUS_KAFKA_GROUP = "mq.kafka.l2_data_bus";
    private static final String L1_ODS_DATA_BUS_KAFKA_GROUP = "mq.kafka.l1_ods";
    private static final String APP_CONF_GROUP = "conf";

    RainbowUtils rainbowUtils;
    String dstDatabase;
    String l1OdsTableName;

    public ExternalDBusRainbowExtractor(RainbowUtils rainbowUtils) {
        this.rainbowUtils = rainbowUtils;
        dstDatabase = rainbowUtils.getStringValue(APP_CONF_GROUP, "ods_database");
        l1OdsTableName = rainbowUtils.getStringValue(APP_CONF_GROUP, "distribution_table_name");
    }

    @Override
    public HashMap<String, InfraConfManager.DstTBLInfo> extractPatternMap() throws Exception {
        // 从 rainbow 导出
        HashMap<String, InfraConfManager.DstTBLInfo> retHM = new HashMap<>();
        ArrayList<String> odsModelList = rainbowUtils.getKeyListByGroup(ODS_ICEBERG_SINK_GROUP);
        for (String modelKey : odsModelList) {
            ObjectNode tableNameMap = rainbowUtils.getJSONValue(ODS_ICEBERG_SINK_GROUP, modelKey);
            Iterator<Map.Entry<String, JsonNode>> it = tableNameMap.fields();
            while (it.hasNext()) {
                Map.Entry<String, JsonNode> entry = it.next();
                InfraConfManager.DstTBLInfo dstTBLInfo = new InfraConfManager.DstTBLInfo();
                dstTBLInfo.dstDatabase = dstDatabase;
                dstTBLInfo.dstTableName = entry.getValue().asText();
                dstTBLInfo.eqFields = this.extractL2OdsTbPk(dstTBLInfo.dstTableName);
                retHM.put(entry.getKey(), dstTBLInfo);
            }
        }
        return retHM;
    }

    private ArrayList<ObjectNode> getMQConfJsonList(String sourceGroup) throws JsonProcessingException {
        ArrayList<ObjectNode> configList = new ArrayList<>();

        ArrayList<String> mqConfKeyList = rainbowUtils.getKeyListByGroup(sourceGroup);
        for (String confKey : mqConfKeyList) {
            ObjectNode mqConf = rainbowUtils.getJSONValue(sourceGroup, confKey);
            if (mqConf == null) {
                continue;
            }
            configList.add(mqConf);
        }

        return configList;
    }

    @Override
    public ReportPlatformConf extractReportPlatformConf() throws Exception {
        ReportPlatformConf retConf = new ReportPlatformConf();
        retConf.platformEntryPoint = rainbowUtils.getStringValue(APP_CONF_GROUP, "table_map");//待确定怎么修改

        getMQConfJsonList(KAFKA_SOURCE_GROUP).forEach(c -> {
                    DBusConf.KafkaConf kConf = new DBusConf.KafkaConf();
                    kConf.topic = c.get("topic").asText();
                    kConf.entryPoint = c.get("address").asText();
                    kConf.consumerGroup = c.get("group_id").asText();

                    retConf.kafkaConfList.add(kConf);
                }
        );
        return retConf;
    }

    public ArrayList<String> extractL2OdsTbPk(String tableName) {
        ArrayList<String> pks = new ArrayList<>();
        String value = rainbowUtils.getStringValue(ODS_ICEBERG_PK_GROUP, tableName);
        if (StringUtils.isEmpty(value)) {
            return pks;
        }
        String[] split = value.split(";");
        pks.addAll(Arrays.asList(split));
        return pks;
    }

    private DBusConf extractDBusConf(String group) {
        DBusConf dBusConf = new DBusConf();
        dBusConf.dBusMQConf = new DBusConf.KafkaConf();
        dBusConf.dBusMQConf.topic = rainbowUtils.getStringValue(group, "TOPICS");
        dBusConf.dBusMQConf.entryPoint = rainbowUtils.getStringValue(group, "ENTRYPOINT");
        dBusConf.dBusMQConf.consumerGroup = rainbowUtils.getStringValue(group, "NRT_CONSUMER_GROUP");
        return dBusConf;
    }

    @Override
    public DBusConf extractL1OdsDBusConf() throws Exception {
        return extractDBusConf(L1_ODS_DATA_BUS_KAFKA_GROUP);
    }

    @Override
    public DBusConf extractL2OdsDBusConf() throws Exception {
        return extractDBusConf(L2_ODS_DATA_BUS_KAFKA_GROUP);
    }

    @Override
    public NRTConf extractNRTConf() throws Exception {
        return new NRTConf(dstDatabase, l1OdsTableName);
    }

    @Override
    public OLAPConf extractOLAPConf() throws Exception {
        return null;
    }

    @Override
    public DBusConf extractInternalDBusConf() throws Exception {
        return null;
    }

    public String extractReportPlatformURL() {
        return rainbowUtils.getStringValue(APP_CONF_GROUP, "table_map");
    }
}