package com.tencent.andata.conf.manager.struct;

import com.tencent.andata.conf.manager.InfraConfManager;
import java.util.HashMap;

/**
 * 近实时分发配置
 * 包含 pattern -> iceberg表 映射、iceberg表主键等表信息
 * 同时也包含需要访问的DBus
 * TODO: 后面再整这个，先做DBus。整这个要搞IceDistributor很麻烦
 */
public class MQDistributeConf {
    public DBusConf dBusConf;
    public HashMap<String, InfraConfManager.DstTBLInfo> patternTableMap = new HashMap<>(); // pattern -> l2 ods 映射

    public MQDistributeConf(DBusConf dBusConf) {
        this.dBusConf = dBusConf;
    }

    public void addPatternTableInfo(String pattern, InfraConfManager.DstTBLInfo dstTBLInfo) {
        this.patternTableMap.put(pattern, dstTBLInfo);
    }

    @Override
    public String toString() {
        return "MQDistributeConf{" +
                "dBusConf=" + dBusConf +
                ", patternTableMap=" + patternTableMap +
                '}';
    }
}