package com.tencent.andata.conf.manager.extractor;

import com.tencent.andata.conf.manager.InfraConfManager.DstTBLInfo;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.NRTConf;
import com.tencent.andata.conf.manager.struct.OLAPConf;
import com.tencent.andata.conf.manager.struct.ReportPlatformConf;

import java.util.HashMap;

// TODO：这块抽象有问题，如果全局可以访问，是不是用Map来存更合理点，也更自由点。但用Map来存的坏处就是没办法确认使用是不是非法了
// TODO: 这块要把这个配置弄成全局可以访问的单例模式。类似Redux，但这块是不是自己做，要调研下开源组件
public interface BaseExtractor {
    public abstract HashMap<String, DstTBLInfo> extractPatternMap() throws Exception;

    public abstract DBusConf extractL1OdsDBusConf() throws Exception;

    public abstract DBusConf extractL2OdsDBusConf() throws Exception;

    public abstract NRTConf extractNRTConf() throws Exception;

    public abstract ReportPlatformConf extractReportPlatformConf() throws Exception;

    public abstract OLAPConf extractOLAPConf() throws Exception;

    public abstract DBusConf extractInternalDBusConf() throws Exception;

}