package com.tencent.andata.conf.manager;

import com.tencent.andata.conf.manager.extractor.BaseExtractor;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.conf.manager.struct.MQDistributeConf;
import com.tencent.andata.utils.ConcurrentIcebergTableLoader;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.table.types.logical.RowType;
import org.apache.iceberg.Table;
import org.apache.iceberg.flink.TableLoader;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;

public class InfraConfManager {
    public static class DstTBLInfo implements Serializable {
        public String dstDatabase;
        public String dstTableName;
        public ArrayList<String> eqFields;
        public Tuple3<Table, TableLoader, RowType> schema;

        @Override
        public String toString() {
            return "DstTBLInfo{" +
                    "dstDatabase='" + dstDatabase + '\'' +
                    ", dstTableName='" + dstTableName + '\'' +
                    ", eqFields=" + eqFields +
                    ", schema=" + schema +
                    '}';
        }
    }

    BaseExtractor extractor;

    public InfraConfManager(BaseExtractor extractor) {
        this.extractor = extractor;
    }

    /**
     * 获取Infra分发配置
     *
     * @return 配置信息
     * @throws Exception
     */
    public InfraConf getInfraConf() throws Exception {
        // TODO：既然这块要做成通用的样子，那么就代表所有的配置都会塞进这里。
        // TODO: 所以这块最好改成Lazy Load，要不每次都得全量读取，没什么必要。
        InfraConf retConf = new InfraConf();
        // 近实时分发配置
        retConf.nrtConf = this.extractor.extractNRTConf();
        // L1 ODS Distribute
        retConf.l1OdsMQDistributeConf = new MQDistributeConf(this.extractor.extractL1OdsDBusConf());
        retConf.l1OdsMQDistributeConf.addPatternTableInfo(".*", retConf.nrtConf.getL1OdsTBLInfo());

        // L2 ODS Distribute
        retConf.l2OdsMQDistributeConf = new MQDistributeConf(this.extractor.extractL2OdsDBusConf());

        // 这里使用Rainbow生成 接入平台Extractor，获取接入平台的Table Map
        // TODO :: 后面以接入平台数据为准
        String odsDatabase = retConf.nrtConf.odsDatabase;

        // 在这里并发读取表结构
        try {
            HashMap<String, DstTBLInfo> patternMap = this.extractor.extractPatternMap();

            ConcurrentIcebergTableLoader loader = ConcurrentIcebergTableLoader.fromHiveDB(odsDatabase);

            patternMap.forEach((key, value) -> loader.addTable(key, value.dstTableName));
            // TODO: 这里后面跟Rainbow PK配置组合下，补充pk信息。后面切到接入平台可以彻底解决这个问题
            loader.load().toTuple3List().forEach(value -> {
                DstTBLInfo l2OdsInfo = patternMap.get(value.f0);
                l2OdsInfo.schema = new Tuple3<>(value.f1, value.f2, IcebergCatalogReader.getTableRowType(value.f1));
                retConf.l2OdsMQDistributeConf.addPatternTableInfo(value.f0, l2OdsInfo);
            });
        } catch (InterruptedException e) {
            // change to runtime exception, throw to upper
            throw new RuntimeException(e);
        }
        retConf.reportPlatformConf = this.extractor.extractReportPlatformConf();
        return retConf;
    }

    public InfraConf getInternalInfraConf() throws Exception{
        InfraConf retConf = new InfraConf();
        // 近实时分发配置
        retConf.nrtConf = this.extractor.extractNRTConf();
        // Dbus 配置
        retConf.internalDBusDistributedConf = new MQDistributeConf(this.extractor.extractInternalDBusConf());

        String odsDatabase = retConf.nrtConf.odsDatabase;

        // 在这里并发读取表结构
        try {
            HashMap<String, DstTBLInfo> patternMap = this.extractor.extractPatternMap();
            System.out.println(patternMap);
            ConcurrentIcebergTableLoader loader = ConcurrentIcebergTableLoader.fromHiveDB(odsDatabase);
            // 添加需要加载的Table
            patternMap.forEach((key, value) -> loader.addTable(key, value.dstTableName));
            // 并发加载
            loader.load().toTuple3List().forEach(value -> {
                DstTBLInfo l2OdsInfo = patternMap.get(value.f0);
                final Table iceTable = value.f1;
                // TODO Check format version
                System.out.println(
                        String.format("properties: %s", iceTable.properties())
                );
                // 将Table Schema信息添加到配置中，下游处理
                l2OdsInfo.schema = new Tuple3<>(iceTable, value.f2, IcebergCatalogReader.getTableRowType(value.f1));
                retConf.internalDBusDistributedConf.addPatternTableInfo(value.f0, l2OdsInfo);
            });
        } catch (InterruptedException e) {
            // change to runtime exception, throw to upper
            throw new RuntimeException(e);
        }
        return retConf;
    }
}