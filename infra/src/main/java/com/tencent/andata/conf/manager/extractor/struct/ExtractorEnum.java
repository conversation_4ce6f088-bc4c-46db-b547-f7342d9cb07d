package com.tencent.andata.conf.manager.extractor.struct;

public enum ExtractorEnum {
    RAINBOW {
        @Override
        public String toString() {
            return "rainbow";
        }
    },
    REPORTPLATFORM {
        @Override
        public String toString() {
            return "report_platform";
        }
    },
    TCANDON_RAINBOW {
        @Override
        public String toString() {
            return "tcandon_rainbow";
        }
    },
    ANDON_COMBINE {
        @Override
        public String toString() {
            return "andon_combine";
        }
    }
}