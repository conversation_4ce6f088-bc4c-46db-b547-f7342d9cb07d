package com.tencent.andata.conf.manager.extractor.factory;

import com.tencent.andata.conf.manager.extractor.ExternalCombineExtractor;
import com.tencent.andata.conf.manager.extractor.BaseExtractor;
import com.tencent.andata.conf.manager.extractor.TCAndonRainbowExtractor;
import com.tencent.andata.conf.manager.extractor.struct.ExtractorEnum;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Properties;

public class ExtractorFactory {

    private static final String EXTRACTOR_TYPE = "EXTRACTOR_TYPE";
    protected static final Logger LOG = LoggerFactory.getLogger(ExtractorFactory.class);

    /***
     * 从 properties 文件初始化 Extractor
     * @param name fileName
     * @return Extractor
     * @throws IOException .
     */
    public static BaseExtractor fromProperties(String name) throws IOException {
        Properties properties = PropertyUtils.loadProperties(name);
        String type = properties.getProperty(EXTRACTOR_TYPE);
        try {
            switch (ExtractorEnum.valueOf(type)) {
                case TCANDON_RAINBOW:
                    return new TCAndonRainbowExtractor(new RainbowUtils(properties));
                case RAINBOW:
                case REPORTPLATFORM:
                case ANDON_COMBINE:
                default:
                    return new ExternalCombineExtractor(new RainbowUtils(properties));
            }
        } catch (NullPointerException e) {
            LOG.warn("the extractor type in properties is null, return the AndonCombineExtractor");
            return new ExternalCombineExtractor(new RainbowUtils(properties));
        }

    }
}
