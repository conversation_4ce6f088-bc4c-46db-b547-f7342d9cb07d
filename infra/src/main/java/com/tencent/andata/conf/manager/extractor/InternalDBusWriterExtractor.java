package com.tencent.andata.conf.manager.extractor;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.NRTConf;
import com.tencent.andata.conf.manager.struct.OLAPConf;
import com.tencent.andata.conf.manager.struct.ReportPlatformConf;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.util.Preconditions;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;

/**
 * 内部数据总线Writer Extractor
 */
public class InternalDBusWriterExtractor implements BaseExtractor {
    public RainbowUtils rainbowUtils;
    public String dstDatabase;
    public static String DATA_BUS_GROUP = "mq.kafka.internal_data_bus";
    public static String APP_CONF_GROUP = "conf";

    public InternalDBusWriterExtractor(RainbowUtils rainbowUtils) {
        this.rainbowUtils = rainbowUtils;
        dstDatabase = rainbowUtils.getStringValue(APP_CONF_GROUP, "ods_database");
    }

    /**
     * 从Rainbow拉取表映射信息
     *
     * @return tableMap
     * @throws Exception ex
     */
    @Override
    public HashMap<String, InfraConfManager.DstTBLInfo> extractPatternMap() throws Exception {
        final HashMap<String, InfraConfManager.DstTBLInfo> res = new HashMap<String, InfraConfManager.DstTBLInfo>();
        // 获取Table映射
        final ArrayList<ObjectNode> internalTableMaps = rainbowUtils.getJSONArrayValue(APP_CONF_GROUP, "internal_table_map");
        for (ObjectNode item : internalTableMaps) {
            System.out.println(item.toString());
            // 上报数据中的表标识
            final String subscribe = item.get("subscribe").asText();
            // Iceberg Table Name
            final String tableName = item.get("tableName").asText();
            // 构造TableInfo
            final InfraConfManager.DstTBLInfo dstTBLInfo = new InfraConfManager.DstTBLInfo();
            dstTBLInfo.dstDatabase = dstDatabase;
            dstTBLInfo.dstTableName = tableName;
            dstTBLInfo.eqFields = new ArrayList<String>();
            final JsonNode pkJsonNode = item.get("pk");
            if (pkJsonNode.isArray()) {
                // 遍历PK
                final Iterator<JsonNode> pkElements = pkJsonNode.elements();
                while (pkElements.hasNext()) {
                    dstTBLInfo.eqFields.add(pkElements.next().asText());
                }
            }

            res.put(subscribe, dstTBLInfo);
        }
        return res;
    }

    public static void main(String[] args) throws Exception {
        final RainbowUtils rainbowUtils1 = new RainbowUtils(
                PropertyUtils.loadProperties("env.properties")
        );
        final InternalDBusWriterExtractor internalDBusWriterExtractor = new InternalDBusWriterExtractor(rainbowUtils1);
        System.out.println(internalDBusWriterExtractor.extractPatternMap());
    }

    /**
     * 从Rainbow获取Kafka 信息
     *
     * @return dbus
     * @throws Exception ex
     */
    @Override
    public DBusConf extractInternalDBusConf() throws Exception {
        final DBusConf dBusConf = new DBusConf();
        dBusConf.dBusMQConf = new DBusConf.KafkaConf();
        dBusConf.dBusMQConf.topic = rainbowUtils.getStringValue(DATA_BUS_GROUP, "TOPICS");
        dBusConf.dBusMQConf.consumerGroup = rainbowUtils.getStringValue(DATA_BUS_GROUP, "CONSUMER_GROUP");
        dBusConf.dBusMQConf.entryPoint = rainbowUtils.getStringValue(DATA_BUS_GROUP, "ENTRYPOINT");
        // 内部数据总线在用Avro封装了
        dBusConf.payloadWrapped = true;
        return dBusConf;
    }

    /**
     * 写入的数据库
     *
     * @return conf
     * @throws Exception ex
     */
    @Override
    public NRTConf extractNRTConf() throws Exception {
        return new NRTConf(dstDatabase);
    }

    @Override
    public DBusConf extractL1OdsDBusConf() throws Exception {
        return null;
    }

    @Override
    public DBusConf extractL2OdsDBusConf() throws Exception {
        return null;
    }


    @Override
    public ReportPlatformConf extractReportPlatformConf() throws Exception {
        return null;
    }

    @Override
    public OLAPConf extractOLAPConf() throws Exception {
        return null;
    }
}
