package com.tencent.andata.conf.manager.extractor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.tencent.andata.conf.manager.InfraConfManager.DstTBLInfo;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.NRTConf;
import com.tencent.andata.conf.manager.struct.OLAPConf;
import com.tencent.andata.conf.manager.struct.ReportPlatformConf;
import org.apache.commons.lang.NotImplementedException;


import java.util.HashMap;
import java.util.Iterator;

public class ReportPlatformExtractor implements BaseExtractor {
    String platformEntryPoint;
    String dstDatabase;

    public ReportPlatformExtractor(String platformEntryPoint, String dstDatabase) {
        this.platformEntryPoint = platformEntryPoint;
        this.dstDatabase = dstDatabase;
    }

    @Override
    public HashMap<String, DstTBLInfo> extractPatternMap() throws Exception {
        HashMap<String, DstTBLInfo> retHM = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode resultData = objectMapper.readValue(platformEntryPoint, ObjectNode.class);
        Iterator<String> fieldNames = resultData.fieldNames();
        while (fieldNames.hasNext()) {
            String field = fieldNames.next();
            DstTBLInfo dstTBLInfo = new DstTBLInfo();
            dstTBLInfo.dstDatabase = dstDatabase;
            dstTBLInfo.dstTableName = resultData.get(field).asText();
            retHM.put(field, dstTBLInfo);
        }
        return retHM;
    }

    @Override
    public ReportPlatformConf extractReportPlatformConf() throws Exception {
        throw new NotImplementedException();
    }

    @Override
    public OLAPConf extractOLAPConf() throws Exception {
        return null;
    }

    @Override
    public DBusConf extractInternalDBusConf() throws Exception {
        return null;
    }

    @Override
    public DBusConf extractL1OdsDBusConf() throws Exception {
        throw new NotImplementedException();
    }

    @Override
    public DBusConf extractL2OdsDBusConf() throws Exception {
        throw new NotImplementedException();
    }

    @Override
    public NRTConf extractNRTConf() throws Exception {
        return null;
    }
}