package com.tencent.andata.conf.manager;

import com.tencent.andata.conf.manager.struct.DBusConf;
import org.apache.flink.api.common.serialization.AbstractDeserializationSchema;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import java.util.Properties;

public class InfraConfUtils<T> {
    @Deprecated
    public FlinkKafkaConsumerBase<T> buildKafkaConsumerFromDBusConf(DBusConf.KafkaConf kafkaConf,
                                                                    AbstractDeserializationSchema<T> desSchema) {
        return new FlinkKafkaConsumer<>(
                kafkaConf.topic,
                desSchema,
                kafkaConf.toProperties()
        ).setStartFromGroupOffsets().setCommitOffsetsOnCheckpoints(true);
    }

    /**
     * Flink 1.15 Kafka Consumer
     *
     * @param kafkaConf
     * @param desSchema
     * @return
     */
    public KafkaSource<T> buildKafkaConsumerFromDbusConf1_15(DBusConf.KafkaConf kafkaConf,
                                                             AbstractDeserializationSchema<T> desSchema) {
        return
                KafkaSource.<T>builder()
                        .setBootstrapServers(kafkaConf.entryPoint)
                        .setTopics(kafkaConf.topic)
                        // 从消费组提交offset开始消费，不存在则从最新的消费
                        .setStartingOffsets(OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setGroupId(kafkaConf.consumerGroup)
                        .setValueOnlyDeserializer(desSchema)
                        .setProperties(
                                new Properties() {{
                                    // 在CK时Commit数据
                                    setProperty("commit.offsets.on.checkpoint", "true");
                                    setProperty("enable.auto.commit", "false");
                                    // 只读取Kafka中已经Committed的数据
//                                    setProperty(ConsumerConfig.ISOLATION_LEVEL_CONFIG, "read_committed");
                                    setProperty("fetch.max.wait.ms", "10000");
                                }}
                        )
                        .build();
    }
}