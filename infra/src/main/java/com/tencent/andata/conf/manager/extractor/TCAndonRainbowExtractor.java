package com.tencent.andata.conf.manager.extractor;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.NRTConf;
import com.tencent.andata.conf.manager.struct.ReportPlatformConf;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;

public class TCAndonRainbowExtractor extends ExternalDBusRainbowExtractor {

    private static final String DATABUS_SOURCE_GROUP = "mq.kafka.data_bus";

    public TCAndonRainbowExtractor(RainbowUtils rainbowUtils) {
        super(rainbowUtils);
    }

    @Override
    public NRTConf extractNRTConf() throws Exception {
        return new NRTConf(dstDatabase);
    }

    @Override
    public ReportPlatformConf extractReportPlatformConf() throws Exception {
        return null;
    }

    @Override
    public DBusConf extractL1OdsDBusConf() throws Exception {
        return null;
    }

    @Override
    public DBusConf extractL2OdsDBusConf() throws Exception {
        DBusConf dBusConf = new DBusConf();
        dBusConf.dBusMQConf = new DBusConf.KafkaConf();
        dBusConf.dBusMQConf.topic = rainbowUtils.getStringValue(DATABUS_SOURCE_GROUP, "TOPICS");
        dBusConf.dBusMQConf.entryPoint = rainbowUtils.getStringValue(DATABUS_SOURCE_GROUP, "BROKERS");
        dBusConf.dBusMQConf.consumerGroup = rainbowUtils.getStringValue(DATABUS_SOURCE_GROUP, "NRT_CONSUMER_GROUP");
        String payloadCompressed = rainbowUtils.getStringValue(DATABUS_SOURCE_GROUP, "PayloadCompressed");
        if (StringUtils.isNotEmpty(payloadCompressed)) {
            dBusConf.payloadCompressed = Boolean.parseBoolean(payloadCompressed);
        }
        return dBusConf;
    }

    @Override
    public HashMap<String, InfraConfManager.DstTBLInfo> extractPatternMap() throws Exception {
        return super.extractPatternMap();
    }
}
