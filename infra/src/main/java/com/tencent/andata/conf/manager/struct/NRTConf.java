package com.tencent.andata.conf.manager.struct;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.table.types.logical.RowType;
import org.apache.iceberg.Table;
import org.apache.iceberg.flink.TableLoader;

public class NRTConf {
    public String l1OdsTableName;
    public String odsDatabase;
    public Tuple3<Table, TableLoader, RowType> l1OdsTpl3;

    public NRTConf(String l1OdsDatabase) {
        this.odsDatabase = l1OdsDatabase;
    }

    public NRTConf(String l1OdsDatabase, String l1OdsTableName) {
        this.odsDatabase = l1OdsDatabase;
        this.l1OdsTableName = l1OdsTableName;
        TableLoader l1OdsIceTableLoader =
                new IcebergCatalogReader().getTableLoaderInstance(l1OdsDatabase, l1OdsTableName);
        Table l1OdsIceTable = l1OdsIceTableLoader.loadTable();
        RowType l1OdsRowType = IcebergCatalogReader.getTableRowType(l1OdsIceTable);
        this.l1OdsTpl3 = new Tuple3<>(l1OdsIceTable, l1OdsIceTableLoader, l1OdsRowType);
    }

    public InfraConfManager.DstTBLInfo getL1OdsTBLInfo() {
        InfraConfManager.DstTBLInfo l1OdsInfo = new InfraConfManager.DstTBLInfo();
        l1OdsInfo.dstDatabase = odsDatabase;
        l1OdsInfo.dstTableName = l1OdsTableName;
        l1OdsInfo.schema = l1OdsTpl3;
        return l1OdsInfo;
    }

    public RowType getL1OdsRowType() {
        return l1OdsTpl3.f2;
    }

    @Override
    public String toString() {
        return "NRTConf{" +
                "l1OdsTableName='" + l1OdsTableName + '\'' +
                ", odsDatabase='" + odsDatabase + '\'' +
                ", l1OdsTpl3=" + l1OdsTpl3 +
                '}';
    }
}