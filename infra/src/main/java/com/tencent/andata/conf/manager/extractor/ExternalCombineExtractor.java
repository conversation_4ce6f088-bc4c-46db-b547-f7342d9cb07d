package com.tencent.andata.conf.manager.extractor;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.NRTConf;
import com.tencent.andata.conf.manager.struct.OLAPConf;
import com.tencent.andata.conf.manager.struct.ReportPlatformConf;
import com.tencent.andata.utils.RainbowUtils;

import java.util.HashMap;

/**
 * 上报平台接入的数据配置的Extractor
 * Dbus 数据由RainbowExtractor获取
 * TableMap 通过上报平台Extractor调接口获取
 */
public class ExternalCombineExtractor implements BaseExtractor {

    private ExternalDBusRainbowExtractor rainbowExtractor;
    private ReportPlatformExtractor reportPlatformExtractor;

    public ExternalCombineExtractor(RainbowUtils rainbowUtils) {
        this.rainbowExtractor = new ExternalDBusRainbowExtractor(rainbowUtils);
        this.reportPlatformExtractor = new ReportPlatformExtractor(
                this.rainbowExtractor.extractReportPlatformURL(),
                this.rainbowExtractor.dstDatabase
        );
    }

    @Override
    public HashMap<String, InfraConfManager.DstTBLInfo> extractPatternMap() throws Exception {
        HashMap<String, InfraConfManager.DstTBLInfo> patternMap = reportPlatformExtractor.extractPatternMap();
        patternMap.values().forEach((v) -> {
            v.eqFields = rainbowExtractor.extractL2OdsTbPk(v.dstTableName);
        });
        return patternMap;
    }

    @Override
    public DBusConf extractL1OdsDBusConf() throws Exception {
        return rainbowExtractor.extractL1OdsDBusConf();
    }

    @Override
    public DBusConf extractL2OdsDBusConf() throws Exception {
        return rainbowExtractor.extractL2OdsDBusConf();
    }

    @Override
    public NRTConf extractNRTConf() throws Exception {
        return rainbowExtractor.extractNRTConf();
    }

    @Override
    public ReportPlatformConf extractReportPlatformConf() throws Exception {
        return rainbowExtractor.extractReportPlatformConf();
    }

    @Override
    public OLAPConf extractOLAPConf() throws Exception {
        return null;
    }

    @Override
    public DBusConf extractInternalDBusConf() throws Exception {
        return null;
    }

}