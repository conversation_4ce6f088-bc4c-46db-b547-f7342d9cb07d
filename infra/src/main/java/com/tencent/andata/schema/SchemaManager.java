package com.tencent.andata.schema;


import com.google.common.base.Preconditions;
import com.tencent.andata.dispatcher.cdc.task.SyncTask;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.dispatcher.cdc.struct.TaskParams;
import com.tencent.andata.utils.cdc.conf.input.InputTaskParams;
import com.tencent.andata.utils.proxy.OptHiveCatalogProxy;
import java.util.List;
import org.apache.iceberg.hive.optimized.OptimizedHiveCatalog;

public class SchemaManager {

    /**
     * main
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        Preconditions.checkState(args.length > 0);

        OptHiveCatalogProxy hiveCatalog = new OptHiveCatalogProxy(
                (OptimizedHiveCatalog) new IcebergCatalogReader().getCatalog()
        );
        List<TaskParams> taskParamsList = TaskParams
                .fromInputTaskParams(
                        InputTaskParams.fromJson(args[0])
                        // TODO：这个后面替换成Conf类，保证不会跨层解析底层数据
                );
        SyncTask syncTask = SyncTask.fromParams(taskParamsList);
        // 通过TaskList聚合生成DBMonitorTaskList
//        ArrayList<MonitorTask> monitorTasks = MonitorTask.buildListFromTaskParams(taskParamsList);
//        // 检查对应的表，确定是否存在；不存在则建表，存在则忽略
//        monitorTasks.forEach(monitorTask -> {
//            ArrayList<TaskParams> taskParams = monitorTask.tableTaskList;
//            taskParams.forEach(
//                    consumer(param -> {
//                                org.apache.iceberg.catalog.TableIdentifier iceTableIdentifier = param
//                                        .sinkTask
//                                        .table
//                                        .toIcebergTableIdentifier();
//
//                                Schema rdbmsSchema = CDCUtils.getRdbmsSchema(
//                                        param.databaseConf,
//                                        param.dbTableName,
//                                        param.databaseType,
//                                        param.jdbcConf
//                                );
//                                if (!hiveCatalog.tableExists(iceTableIdentifier)) {
//                                    org.apache.iceberg.Schema iceSchema = TableUtils.convertRowTypeToIcebergSchema(
//                                            TableUtils.convertFlinkSchemaToRowType(rdbmsSchema, false)
//                                    );
//                                    PartitionSpec partitionSpec = null;
//
//                                    if (param.sinkTask.partitionSpec != null) {
//                                        partitionSpec = param.sinkTask.partitionSpec.buildIcebergPartition(iceSchema);
//                                    }
//
//                                    hiveCatalog.createIceTableIfNotExistsUsingIceSchema(
//                                            iceTableIdentifier,
//                                            iceSchema,
//                                            Arrays.asList(param.sinkTask.primaryKey),
//                                            partitionSpec
//                                    );
//                                }
//                            }
//                    )
//            );
//        });
    }
}
