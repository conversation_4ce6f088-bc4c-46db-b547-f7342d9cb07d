package com.tencent.andata.writer;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.conf.manager.struct.MQDistributeConf;
import com.tencent.andata.dct.api.distributor.iceberg.IcebergDistributorV2;
import com.tencent.andata.dct.api.operator.process.MessageRowDataTagger;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.writer.operator.source.builder.KafkaDBusSourceBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.util.ArrayList;


public class NRTWriter {

    /**
     * 主函数，近实时分发器，主要从接入平台拉取schemaName跟对应tableName的映射关系
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        // TODO: TCAndon那里需要改造这里，现在只能满足内部版本的需求
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        InfraConf infraConf = new InfraConfManager(
                ExtractorFactory.fromProperties("env.properties")
        ).getInfraConf();
        addTaskToEnv(env, infraConf);
        setCKConf(env);
        env.execute("NRTWriter");
    }

    public static void setCKConf(StreamExecutionEnvironment env) {
        env.enableCheckpointing(1800 * 1000);
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setMinPauseBetweenCheckpoints(60 * 1000L);
        checkpointConfig.setMaxConcurrentCheckpoints(2);
        checkpointConfig.setCheckpointTimeout(180 * 1000L);
        checkpointConfig.setTolerableCheckpointFailureNumber(10);
    }

    /**
     * add task
     *
     * @param env
     * @throws Exception
     */
    public static void addTaskToEnv(StreamExecutionEnvironment env, InfraConf infraConf) throws Exception {
        MQDistributeConf l2OdsMQDistributeConf = infraConf.l2OdsMQDistributeConf;
        // TODO: 这里还没抽出到Rainbow，先暂时这么做
        l2OdsMQDistributeConf.dBusConf.payloadWrapped = true;

        // KafkaSource
        DataStream<MessageRowData> srcDataStream = KafkaDBusSourceBuilder
                .getInstance()
                .setFlinkEnv(env)
                .setMQDistributeConf(l2OdsMQDistributeConf)
                .build();
        // 这里只是分发，而没有同步DDL的相关逻辑。Schema同步相关逻辑移动到单独的SchemaManager里
        IcebergDistributorV2.builder()
                .setDstDatabase(infraConf.nrtConf.odsDatabase)
                .setPatternTableMap(infraConf.l2OdsMQDistributeConf.patternTableMap)
                // 将数据按照DstTable打标
                .setDispatcherDataStream(
                        srcDataStream.process(
                                new MessageRowDataTagger(
                                        new ArrayList<>(infraConf.l2OdsMQDistributeConf.patternTableMap.keySet())
                                )
                        )
                )
                .build()
                .distribute();
    }
}