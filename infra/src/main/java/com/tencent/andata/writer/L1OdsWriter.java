package com.tencent.andata.writer;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.InfraConfUtils;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.writer.deserialize.schema.BinaryRowDataDeserializationSchema;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.flink.table.data.RowData;
import org.apache.iceberg.DistributionMode;
import org.apache.iceberg.flink.sink.FlinkSink;

public class L1OdsWriter {
    /**
     * 一层ODS Writer
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        InfraConf infraConf = new InfraConfManager(
                ExtractorFactory.fromProperties("env.properties")
        ).getInfraConf();

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        DataStream<RowData> ds = env
                .fromSource(
                        buildL1OdsConsumerSourceFunc1_15(infraConf),
                        WatermarkStrategy.noWatermarks(),
                        getUid(infraConf.l1OdsMQDistributeConf.dBusConf)
                )
                .setParallelism(2);

        FlinkSink.forRowData(ds)
                .table(infraConf.nrtConf.l1OdsTpl3.f0)
                .tableLoader(infraConf.nrtConf.l1OdsTpl3.f1)
                .distributionMode(DistributionMode.HASH)
                .writeParallelism(6)
                .append();


        setCheckpointConf(env);

        env.execute();
    }

    public static String getUid(DBusConf dBusConf) {
        return String.format("kafka-source-%s::%s", dBusConf, dBusConf.dBusMQConf.consumerGroup);
    }

    @Deprecated
    public static FlinkKafkaConsumerBase<RowData> buildL1OdsConsumerSourceFunc(InfraConf infraConf) {
        BinaryRowDataDeserializationSchema deserializationSchema =
                new BinaryRowDataDeserializationSchema(infraConf.nrtConf.getL1OdsRowType());

        return new InfraConfUtils<RowData>()
                .buildKafkaConsumerFromDBusConf(
                        infraConf.l1OdsMQDistributeConf.dBusConf.dBusMQConf,
                        deserializationSchema
                );

    }

    public static KafkaSource<RowData> buildL1OdsConsumerSourceFunc1_15(InfraConf infraConf) {
        BinaryRowDataDeserializationSchema deserializationSchema =
                new BinaryRowDataDeserializationSchema(infraConf.nrtConf.getL1OdsRowType());

        return new InfraConfUtils<RowData>()
                .buildKafkaConsumerFromDbusConf1_15(
                        infraConf.l1OdsMQDistributeConf.dBusConf.dBusMQConf,
                        deserializationSchema
                );
    }

    public static void setCheckpointConf(StreamExecutionEnvironment env) {
        // 一小时Checkpoint一次，尽可能减少L1 Ods的小文件 & 分区数量
        CheckpointConfig checkpointConfig = env.enableCheckpointing(3600 * 1000).getCheckpointConfig();

        checkpointConfig.setMinPauseBetweenCheckpoints(60 * 1000L);
        checkpointConfig.setMaxConcurrentCheckpoints(2);
        checkpointConfig.setCheckpointTimeout(180 * 1000L);
        checkpointConfig.setTolerableCheckpointFailureNumber(10);

    }
}