package com.tencent.andata.writer.operator.source.builder;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.InfraConfUtils;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.conf.manager.struct.MQDistributeConf;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.writer.deserialize.schema.AvroMessageDeserializationSchema;
import com.tencent.andata.writer.message.utils.RowDataMessageConverter;
import java.util.HashMap;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

public class KafkaDBusSourceBuilder {
    DBusConf dBusConf;
    StreamExecutionEnvironment env;

    HashMap<String, InfraConfManager.DstTBLInfo> patternTableMap;

    public KafkaDBusSourceBuilder setMQDistributeConf(MQDistributeConf mqDistributeConf) {
        this.dBusConf = mqDistributeConf.dBusConf;
        this.patternTableMap = mqDistributeConf.patternTableMap;
        return this;
    }

    public KafkaDBusSourceBuilder setFlinkEnv(StreamExecutionEnvironment env) {
        this.env = env;
        return this;
    }

    String getUid() {
        return String.format("kafka-source-%s::%s", dBusConf, dBusConf.dBusMQConf.consumerGroup);
    }

    /**
     * build
     *
     * @return
     */
    public DataStream<MessageRowData> build() {
        AvroMessageDeserializationSchema deserializationSchema = new AvroMessageDeserializationSchema();

        patternTableMap.forEach((key, v) -> deserializationSchema.addPatternAndConverter(
                        key,
                        new RowDataMessageConverter(
                                v.schema.f2,
                                dBusConf.payloadCompressed,
                                v.dstDatabase
                        )
                )
        );
        return env
                .fromSource(
                        new InfraConfUtils<MessageRowData>()
                                .buildKafkaConsumerFromDbusConf1_15(dBusConf.dBusMQConf, deserializationSchema)
                        ,
                        WatermarkStrategy.noWatermarks(),
                        getUid()
                )
                .uid(getUid())
                .name(getUid())
                .setParallelism(2);
    }

    public static KafkaDBusSourceBuilder getInstance() {
        return new KafkaDBusSourceBuilder();
    }
}