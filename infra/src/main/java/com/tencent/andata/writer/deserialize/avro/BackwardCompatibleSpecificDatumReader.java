package com.tencent.andata.writer.deserialize.avro;

import java.io.EOFException;
import java.io.IOException;

import org.apache.avro.Conversion;
import org.apache.avro.Schema;
import org.apache.avro.io.ResolvingDecoder;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificRecordBase;

public class BackwardCompatibleSpecificDatumReader<T> extends SpecificDatumReader<T> {
    public BackwardCompatibleSpecificDatumReader(Class<T> c) {
        super(c);
    }

    private Object extractFieldDefaultValue(Schema.Field field) {
        // 这个函数主要是为了处理Enum这个特殊类型。
        Schema.Type type = field.schema().getType();
        switch (type) {
            case ENUM:
                // 1.9之后更改了Enum Default
                return this.createEnum(field.schema().getEnumDefault(), field.schema());
            default:
                // 后向兼容
                return field.defaultVal();
        }
    }

    @Override
    protected void readField(
            Object record, Schema.Field field, Object oldDatum, ResolvingDecoder in, Object state
    ) throws IOException {
        try {

            if (record instanceof SpecificRecordBase) {
                Conversion<?> conversion = ((SpecificRecordBase) record).getConversion(field.pos());
                Object datum;
                if (conversion != null) {
                    datum = this.readWithConversion(
                            oldDatum, field.schema(), field.schema().getLogicalType(), conversion, in
                    );
                } else {
                    datum = this.readWithoutConversion(oldDatum, field.schema(), in);
                }
                this.getData().setField(record, field.name(), field.pos(), datum);
            } else {
                super.readField(record, field, oldDatum, in, state);
            }
        } catch (EOFException e) {
            // 当新的Schema添加了字段，旧的数据中没有，就会抛出EOFException异常。新添加的字段，都需要添加Default默认值
            // 出现异常后，会取 schema 中的默认值来做填充
            this.getData().setField(record, field.name(), field.pos(), extractFieldDefaultValue(field));
        }
    }
}