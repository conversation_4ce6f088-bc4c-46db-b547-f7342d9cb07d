package com.tencent.andata.writer.message.utils;

import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import com.tencent.andata.utils.rowdata.RowDataConverter;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.andata.struct.avro.message.Message;
import java.io.IOException;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;

public class RowDataMessageConverter extends RowDataConverter {
    String dstDatabase;

    public RowDataMessageConverter(RowType rowType) {
        super(rowType);
    }

    public RowDataMessageConverter(RowType rowType, boolean compress, String dstDatabase) {
        super(rowType, compress);
        this.dstDatabase = dstDatabase;
    }

    /**
     * 将对应的Avro数据转换成对应的MessageRowData（JSR -> Object)
     *
     * @param message
     * @return
     */
    public RowData convertMessageToRowData(Message message) throws IOException {
        return deserializeBytesToGenericRowData(message.getData().array());
    }

    public MessageRowData convertMessageToMessageRowData(Message message) throws IOException {
        RowData rowData = convertMessageToRowData(message);
        MessageRowDataAttr attr = new MessageRowDataAttr();
        attr.setSrc(new TableIdentifier(DatabaseEnum.ICEBERG, dstDatabase, message.getSchemaName().toString()));
        // TODO: 填充对应的内容，需要根据Message中的pattern来动态生成
        return MessageRowData.convertFromRowData(rowData, attr, rowType);
    }
}