package com.tencent.andata.writer.deserialize.schema;

import com.tencent.andata.writer.message.utils.RowDataMessageConverter;
import java.io.IOException;
import org.apache.flink.api.common.serialization.AbstractDeserializationSchema;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;

public class BinaryRowDataDeserializationSchema extends AbstractDeserializationSchema<RowData> {
    RowType rowType;
    RowDataMessageConverter converter;

    public BinaryRowDataDeserializationSchema(RowType rowType) {
        super();
        this.rowType = rowType;
    }

    @Override
    public RowData deserialize(byte[] bytes) throws IOException {
        if (converter == null) {
            converter = new RowDataMessageConverter(rowType);
        }
        return converter.deserializeBytesToGenericRowData(bytes);

    }
}