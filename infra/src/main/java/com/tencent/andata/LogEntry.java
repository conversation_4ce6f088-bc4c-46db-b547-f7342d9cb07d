package com.tencent.andata;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;


public class LogEntry implements Serializable {
    private String uin;
    private String sub_account_uin;
    private String request_id;
    private String report_time;
    private String method;

    @JsonProperty("is_success")
    private boolean is_success;

    private String message;
    private String value_of_primary_key;
    private String record_update_time;
    private String table_name;

    public LogEntry(String uin, String sub_account_uin, String request_id, String report_time, String method,
            boolean is_success, String message, String value_of_primary_key, String record_update_time,
            String table_name) {
        this.uin = uin;
        this.sub_account_uin = sub_account_uin;
        this.request_id = request_id;
        this.report_time = report_time;
        this.method = method;
        this.is_success = is_success;
        this.message = message;
        this.value_of_primary_key = value_of_primary_key;
        this.record_update_time = record_update_time;
        this.table_name = table_name;
    }

    public LogEntry() {
    }

    @Override
    public String toString() {
        return "LogEntry{" +
                "uin='" + uin + '\'' +
                ", sub_account_uin='" + sub_account_uin + '\'' +
                ", request_id='" + request_id + '\'' +
                ", report_time='" + report_time + '\'' +
                ", method='" + method + '\'' +
                ", is_success=" + is_success +
                ", message='" + message + '\'' +
                ", value_of_primary_key='" + value_of_primary_key + '\'' +
                ", record_update_time='" + record_update_time + '\'' +
                ", table_name='" + table_name + '\'' +
                '}';
    }

    public boolean isIs_success() {
        return is_success;
    }

    public void setIs_success(boolean is_success) {
        this.is_success = is_success;
    }

    public String getUin() {
        return uin;
    }

    public void setUin(String uin) {
        this.uin = uin;
    }

    public String getSub_account_uin() {
        return sub_account_uin;
    }

    public void setSub_account_uin(String sub_account_uin) {
        this.sub_account_uin = sub_account_uin;
    }

    public String getRequest_id() {
        return request_id;
    }

    public void setRequest_id(String request_id) {
        this.request_id = request_id;
    }

    public String getReport_time() {
        return report_time;
    }

    public void setReport_time(String report_time) {
        this.report_time = report_time;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getValue_of_primary_key() {
        return value_of_primary_key;
    }

    public void setValue_of_primary_key(String value_of_primary_key) {
        this.value_of_primary_key = value_of_primary_key;
    }

    public String getRecord_update_time() {
        return record_update_time;
    }

    public void setRecord_update_time(String record_update_time) {
        this.record_update_time = record_update_time;
    }

    public String getTable_name() {
        return table_name;
    }

    public void setTable_name(String table_name) {
        this.table_name = table_name;
    }
}