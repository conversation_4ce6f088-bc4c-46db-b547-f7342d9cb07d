package com.tencent.andata;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.utils.*;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Data;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.iceberg.flink.TableLoader;

import java.io.BufferedReader;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


import static com.tencent.andata.utils.TableUtils.insertIntoSql;

import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;


public class LogParser {



    public static void main(String[] args) throws Exception {
            FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
            final StreamTableEnvironment tEnv = flinkEnv.streamTEnv();

            flinkEnv.env().setRestartStrategy(RestartStrategies.failureRateRestart(
                    50, // max failures per interval
                    Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                    Time.of(10, TimeUnit.SECONDS) // delay
            ));

            flinkEnv.env().getConfig().disableForceKryo();
            flinkEnv.env().getConfig().enableObjectReuse();
            IcebergCatalogReader catalog = new IcebergCatalogReader();

            // tEnvConf
            Configuration configuration = new FlinkTableConf()
                    .setEnv(tEnv)
                    .setConf("pipeline.task-name-length", "25")
                    .setConf("table.exec.state.ttl", "259200000")
                    .setConf("table.exec.sink.upsert-materialize", "NONE")
                    .build();

            Properties properties = PropertyUtils.loadProperties("env.properties");
            RainbowUtils rainbowUtils = new RainbowUtils(properties);
            org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper objectMapper1 = new org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper();


            TableUtils.icebergTable2FlinkTable(
                    "andata_lk",
                    objectMapper1.readValue(""
                            + "[\n"
                            + "    {\n"
                            + "        \"icebergTable\":\"ods_andon_yunapi\",\n"
                            + "        \"fTable\":\"iceberg_sink_ods_andon_yunapi\",\n"
                            + "        \"primaryKey\":\"value_of_primary_key\"\n"
                            + "    }\n"
                            + "]", ArrayNode.class), tEnv, catalog
            );
            tEnv.from("iceberg_sink_ods_andon_yunapi").printSchema();

            String[] logFilePaths = {
                    "andata_api_server.log"
            };

            List<LogEntry> logEntries = new ArrayList<>();
            ObjectMapper objectMapper = new ObjectMapper();
            Pattern pattern = Pattern.compile("\\{.*?\\}");

            for (String logFilePath : logFilePaths) {
                parseLogFile(logFilePath, logEntries, objectMapper, pattern);
            }
            // 过滤 report_time 小于指定时间的日志条目
            String filterTime = "2024-11-08T14:12:07.854047914+08:00";
            ZonedDateTime filterDateTime = ZonedDateTime.parse(filterTime, DateTimeFormatter.ISO_ZONED_DATE_TIME);
            // 打印解析后的日志条目
            System.out.println(logEntries.size());
            List<LogEntry> odsAndonYunapi = logEntries.stream()
                    .filter(e -> e.getTable_name().equals("ods_andon_yunapi"))
                    .filter(e -> ZonedDateTime.parse(e.getReport_time(), DateTimeFormatter.ISO_ZONED_DATE_TIME)
                            .isBefore(filterDateTime))
                    .collect(Collectors.toList());

            DataStreamSource<LogEntry> logEntryDataStreamSource = flinkEnv.env().fromCollection(odsAndonYunapi);
            tEnv.createTemporaryView("log_entry_view", logEntryDataStreamSource);
            StatementSet stmtSet = flinkEnv.stmtSet();


            stmtSet.addInsertSql(insertIntoSql(
                    "log_entry_view",
                    "iceberg_sink_ods_andon_yunapi",
                    tEnv.from("iceberg_sink_ods_andon_yunapi"),
                    ICEBERG
            ));

            // execute the sql statements
            flinkEnv.stmtSet().execute();
            flinkEnv.env().execute("Flink Log Application");
    }

    private static void parseLogFile(String logFileName, List<LogEntry> logEntries, ObjectMapper objectMapper,
            Pattern pattern) {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        try (InputStream is = classLoader.getResourceAsStream(logFileName);
             BufferedReader br = new BufferedReader(new InputStreamReader(is))) {
            String line;
            while ((line = br.readLine()) != null) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    String json = matcher.group();
                    try {
                        LogEntry logEntry = objectMapper.readValue(json, LogEntry.class);
                        logEntries.add(logEntry);
                    } catch (Exception e) {
                        // 解析失败，打印错误信息并继续处理下一行
                        if (objectMapper.readTree(json).get("table_name").equals("ods_andon_yunapi")) {
                            System.err.println("Failed to parse JSON: " + json);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void setTabEnvConf(Configuration conf) {
        // 限制 taskName 的长度
        conf.setString("pipeline.task-name-length", "25");
        //开启微批模式
        conf.setString("table.exec.mini-batch.size", "5000");
        conf.setString("table.exec.mini-batch.enabled", "true");
        conf.setString("table.exec.mini-batch.allow-latency", "5 s");
        // 状态保留1天
        conf.setString("table.exec.state.ttl", "86400000");
        conf.setString("execution.runtime-mode", "streaming");
        conf.setString("execution.checkpointing.interval", "30s");
        conf.setString("table.exec.sink.not-null-enforcer", "DROP");
        conf.setString("table.exec.sink.upsert-materialize", "NONE");
        conf.setString("table.exec.legacy-cast-behaviour", "enabled");
    }
}