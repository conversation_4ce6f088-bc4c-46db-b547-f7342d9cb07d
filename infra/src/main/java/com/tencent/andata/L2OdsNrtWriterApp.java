package com.tencent.andata;

import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.InfraConf;
import com.tencent.andata.writer.NRTWriter;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;


public class L2OdsNrtWriterApp {
    public static void main(String[] args) throws Exception {

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        InfraConf infraConf = new InfraConfManager(
                ExtractorFactory.fromProperties("env.properties")
        ).getInfraConf();

        // Add NRTWriter Task
        NRTWriter.addTaskToEnv(env, infraConf);
        NRTWriter.setCKConf(env);
        env.execute("l2 ods nrt writer launcher");
    }
}
