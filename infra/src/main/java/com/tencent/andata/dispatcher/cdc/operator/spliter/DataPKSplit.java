package com.tencent.andata.dispatcher.cdc.operator.spliter;

import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import java.util.List;
import java.util.Random;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.data.RowData;

public class DataPKSplit implements KeySelector<MessageRowData, String> {
    // 这个对象需要知道所有数据的TableIdentifier + RowType + PKList
    List<RowData.FieldGetter> pkGetters;
    private transient Random rand;


    public DataPKSplit(List<RowData.FieldGetter> pkGetters) {
        this.pkGetters = pkGetters;
    }

    @Override
    public String getKey(MessageRowData rowData) {
        MessageRowDataAttr attr = rowData.getAttr();
        StringBuilder builder = new StringBuilder().append(attr.getSrcTable().getFullName());

        if (pkGetters != null) {
            // 有主键，那么按照主键进行分发
            pkGetters.forEach(getter -> builder.append(getter.getFieldOrNull(rowData)));
        } else {
            // 没有主键，那么就随机生成Key分发
            if (rand != null) {
                builder.append(rand.nextInt(10000));
            } else {
                rand = new Random();
            }
        }
        return builder.toString();
    }

    public static DataPKSplit fromFlinkSchema(Schema schema, List<String> primaryKeys) {
        List<RowData.FieldGetter> getters = TableUtils.getFieldGettersUsingFlinkSchemaAndFieldName(schema, primaryKeys);
        return new DataPKSplit(getters);
    }
}