package com.tencent.andata.dispatcher.cdc.operator.spliter;

import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.java.functions.KeySelector;

public class AI<PERSON>ataPKSplit implements KeySelector<MessageRowData, String> {
    public Map<TableIdentifier, DataPKSplit> tblSplitter = new HashMap<>();

    public void addGetter(TableIdentifier idf, DataPKSplit splitter) {
        this.tblSplitter.put(idf, splitter);
    }

    @Override
    public String getKey(MessageRowData messageRowData) throws Exception {
        MessageRowDataAttr attr = messageRowData.getAttr();
        return this.tblSplitter.get(attr.getSrcTable()).getKey(messageRowData);
    }

}