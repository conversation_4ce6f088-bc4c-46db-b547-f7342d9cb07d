package com.tencent.andata.dispatcher.cdc.sink;

import com.google.common.base.Preconditions;
import com.tencent.andata.conf.manager.InfraConfManager;
import com.tencent.andata.conf.manager.extractor.factory.ExtractorFactory;
import com.tencent.andata.conf.manager.struct.DBusConf;
import com.tencent.andata.dct.api.serialize.KafkaAvro2BytesValueSerSchema;
import com.tencent.andata.dispatcher.cdc.operator.spliter.AIODataPKSplit;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.struct.avro.message.MessageType;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.rowdata.RowDataConverter;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.utils.TableSchemaUtils;
import org.apache.iceberg.Table;
import org.apache.iceberg.flink.TableLoader;
import org.apache.kafka.clients.producer.ProducerConfig;

import java.io.Serializable;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Properties;
import java.util.Random;

import static com.tencent.andata.singularity.settings.Const.CDC_GROUP_PREFIX;

public class DatabusSinkActuator extends SinkActuator implements Serializable {

    private String topic;
    private String entryPoint;
    private RowType rowType;
    private String dbName;
    private String tableName;

    public DataStream<Message> transform(DataStream<RowData> ds, AIODataPKSplit splitter) {
        return ds.map(new MapFunction<RowData, Message>() {
            @Override
            public Message map(RowData rowData) throws Exception {
                // TODO：改成AIO模式
                RowDataConverter converter = new RowDataConverter(rowType, false);
                Message retMessage = new Message();
                // TODO: 构造Message这里正常应该也有个Builder的，但这块比较简单，就先这样
                retMessage.setData(ByteBuffer.wrap(converter.serializeRowDataToBytes(rowData)));
                retMessage.setMsgType(MessageType.ROW_DATA);
                retMessage.setSchemaName(tableName);
                retMessage.setProcTime(System.currentTimeMillis());
                return retMessage;
            }
        });
    }

    @Override
    public Schema getSinkSchema() {
        return TableUtils.convertRowType2FlinkSchema(rowType);
    }

    @Override
    public void sink(DataStream<RowData> ds, AIODataPKSplit splitter) {
        this.transform(ds, splitter)
                .sinkTo(
                        KafkaSink.<Message>builder()
                                .setKafkaProducerConfig(
                                        new Properties() {{
                                            put(ProducerConfig.TRANSACTION_TIMEOUT_CONFIG, 15 * 60 * 1000);
                                        }}
                                )
                                .setBootstrapServers(entryPoint)
                                .setRecordSerializer(
                                        KafkaRecordSerializationSchema.builder()
                                                .setTopic(topic)
                                                // 按照表名发送到kafka分区
                                                .setKeySerializationSchema(
                                                        new SerializationSchema<Message>() {
                                                            @Override
                                                            public byte[] serialize(Message message) {
                                                                return message
                                                                        .getSchemaName()
                                                                        .toString()
                                                                        .getBytes(StandardCharsets.UTF_8);
                                                            }
                                                        }
                                                )
                                                .setValueSerializationSchema(
                                                        new KafkaAvro2BytesValueSerSchema<>(Message.class)
                                                )
                                                .build()
                                )
                                // 事务支持
                                .setTransactionalIdPrefix(String.format("Rdms.%s.%s.ToDataBusTxId.%d",
                                        dbName,
                                        tableName,
                                        new Random().nextInt(100000)))
                                .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
                                .build()
                )
                .name(getUid())
                .uid(getUid())
                .setParallelism(1);
    }

    private String getUid() {
        return String.format("Rdms-%s-%s To kafka-sink-%s-%s", dbName, tableName, entryPoint, topic);
    }

    public static class Builder extends BaseSinkActuatorBuilder {
        private static DBusConf dBusConf;
        private TableIdentifier srcTableIdentifier;

        public BaseSinkActuatorBuilder setSrcTableIdentifier(TableIdentifier tableIdentifier) {
            this.srcTableIdentifier = tableIdentifier;
            return this;
        }

        static {
            try {
                // 初始化数据总线配置(只初始化一次)
                // TODO: 这块如果使用InfraConfManager的话，会导致启动的时候耗时过长
                dBusConf = new InfraConfManager(
                        ExtractorFactory.fromProperties("env.properties")
                ).getInfraConf().l2OdsMQDistributeConf.dBusConf;
            } catch (Exception ignored) {
                System.out.println("dbus conf init error");
            }
        }

        /***
         * build .
         * @return .
         * @throws RuntimeException .
         */
        public DatabusSinkActuator build() throws RuntimeException {
            Preconditions.checkNotNull(dBusConf);
            new DatabaseConf();
            RowType rowType;
            try {
//                Schema schema = CDCUtils.getRdbmsSchema(
//                        DatabaseConf.fromSettings(String.format(
//                                "%s.%s.%s",
//                                CDC_GROUP_PREFIX,
//                                tableIdentifier.getDatabaseEnum().toString(),
//                                tableIdentifier.getDbName()
//                        )),
//                        tableIdentifier.getTableName(),
//                        tableIdentifier.getDatabaseEnum(),
//                        this.properties
//                );
//                srcRowType = TableUtils.convertFlinkSchemaToRowType(schema, true);
                IcebergCatalogReader reader = new IcebergCatalogReader();
                rowType = reader.getTableRowType(
                        this.tableIdentifier.getDbName(), this.tableIdentifier.getTableName());
            } catch (TableNotExistException e) {
                throw new RuntimeException(e);
            }
            DatabusSinkActuator actuator = new DatabusSinkActuator();
            actuator.dbName = this.srcTableIdentifier.getDbName();
            actuator.tableName = this.srcTableIdentifier.getTableName();
            actuator.entryPoint = dBusConf.dBusMQConf.entryPoint;
            actuator.topic = dBusConf.dBusMQConf.topic;
            actuator.rowType = rowType;
            return actuator;
        }
    }
}