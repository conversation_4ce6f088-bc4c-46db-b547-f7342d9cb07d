package com.tencent.andata.dispatcher.cdc.struct;

import com.google.common.base.Preconditions;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.cdc.conf.builder.WriteTypeEnum;
import com.tencent.andata.utils.cdc.conf.input.InputTaskParams;
import com.tencent.andata.utils.sink.builder.PartitionSpecBuilder;
import com.tencent.andata.utils.sink.partition.PartitionSpec;
import com.tencent.andata.utils.struct.CDCStartMode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

public class TaskParams {
    // TODO: 这里入参的限制要写一下，最起码要规定那些字段可以为null，哪些不能
    // Data Info
    public List<String> primaryKeys;
    // SRC DB Info
    public TableIdentifier srcTableID;
    public Properties jdbcConf;
    // DST Info
    public TableIdentifier dstTableID;
    public PartitionSpec partitionSpec;
    // DST 也需要DB配置之类

    // CDC场景下，只有ChangeLog（All Append） / Upsert两种
    // 对于某些特定的Actuator，这个参数不生效。具体使用与否看具体实现的Actuator
    public WriteTypeEnum writeType;
    public CDCStartMode startMode;

    public static List<TaskParams> fromInputTaskParams(InputTaskParams[] params) throws Exception {
        List<TaskParams> retList = new ArrayList<>();

        for (InputTaskParams para : params) {
            retList.add(TaskParams.builder(para).build());
        }
        return retList;
    }

    public static Builder builder(InputTaskParams params) {
        return new Builder(params);
    }

    public static class Builder {
        private final InputTaskParams inputTaskParams;

        public Builder(InputTaskParams inputTaskParams) {
            this.inputTaskParams = inputTaskParams;
        }


        private static Properties extractConfFromStrings(String[] jdbcConf) {
            Properties properties = new Properties();
            if (jdbcConf != null) {
                int jdbcConfLength = jdbcConf.length;
                boolean isConfElementLengthEven = jdbcConfLength % 2 == 0;
                Preconditions.checkState(isConfElementLengthEven);
                for (int i = 1; i < jdbcConfLength; i *= 2) {
                    properties.setProperty(jdbcConf[i - 1], jdbcConf[i]);
                }
            }
            return properties;
        }

        /**
         * 构造器生成对应的任务
         */
        public TaskParams build() throws Exception {
            TaskParams params = new TaskParams();

            params.primaryKeys = Arrays.asList(inputTaskParams.primaryKeys);
            params.srcTableID = new TableIdentifier(
                    inputTaskParams.srcDBType,
                    inputTaskParams.srcDBName,
                    inputTaskParams.srcDBSchemaName,
                    inputTaskParams.srcDBTableName
            );

            params.jdbcConf = extractConfFromStrings(inputTaskParams.jdbcConf);

            params.dstTableID = new TableIdentifier(
                    inputTaskParams.dstDBType,
                    inputTaskParams.dstDBName,
                    "", // TODO: 这里的Input参数要改，现在不改主要是为了兼容旧的语法，后面发布的时候一起改了
                    inputTaskParams.dstTableName
            );
            params.partitionSpec = new PartitionSpecBuilder()
                    .setInputPartitionArgs(inputTaskParams.inputPartitionArgs)
                    .build();

            boolean isChangeLog = Boolean.parseBoolean(inputTaskParams.isChangeLog);
            params.writeType = isChangeLog ? WriteTypeEnum.CHANGE_LOG : WriteTypeEnum.UPSERT;
            params.startMode = StringUtils.isEmpty(inputTaskParams.startMode)
                    ? CDCStartMode.ALL : CDCStartMode.valueOf(inputTaskParams.startMode);

            return params;
        }
    }
}