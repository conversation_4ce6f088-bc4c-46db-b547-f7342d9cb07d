package com.tencent.andata.dispatcher.cdc.task;

import com.tencent.andata.dispatcher.cdc.sink.ActuatorFactory;
import com.tencent.andata.dispatcher.cdc.operator.spliter.AIODataPKSplit;
import com.tencent.andata.dispatcher.cdc.struct.TaskParams;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.cdc.conf.builder.WriteTypeEnum;
import com.tencent.andata.dispatcher.cdc.sink.SinkActuator;
import com.tencent.andata.utils.sink.partition.PartitionSpec;
import java.io.Serializable;
import java.util.List;
import java.util.Properties;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.data.RowData;
import org.apache.flink.util.OutputTag;

/**
 * SinkTask只做最简单的入库操作，其他操作在上游处理
 */

public class SinkTask implements Serializable {

    public TableIdentifier subscribeTable;
    public TableIdentifier dstTable;

    public List<String> primaryKeys;
    public PartitionSpec partitionSpec;
    public WriteTypeEnum writeType;
    public Properties actuatorProperties; //TODO: 后面考虑All In One，要不入参太多了
    public TaskParams rawParams; //TODO：对于需要二次修改的地方，暂时打破分层，让代码可以跑起来。后面再重构
    private SinkActuator actuator;


    public SinkTask(TaskParams params) {
        this.subscribeTable = params.srcTableID;
        this.dstTable = params.dstTableID;

        this.writeType = params.writeType;
        this.primaryKeys = params.primaryKeys;
        this.partitionSpec = params.partitionSpec;
        this.actuatorProperties = new Properties(params.jdbcConf);

        this.rawParams = params;
    }

    public void prepare() throws RuntimeException {
        // 调这个函数主要是为了Build Actuator。Factory将构建细节封装了
        // 这里按理应该需要抛出Exception的，毕竟Build的时候可能会涉及到访问其他系统（访问HIVE、访问RDBMS等）
        this.actuator = ActuatorFactory.getActuatorBuilder(this).build();
    }

    public Schema getSinkSchema() throws NullPointerException {
        // 这里跟Sink一样，都不调用Prepare，一定要先并发调用之后才开始调用
        return this.actuator.getSinkSchema();
    }

    public void sink(SingleOutputStreamOperator<RowData> ds, AIODataPKSplit splitter) throws NullPointerException {
        this.actuator.sink(ds.getSideOutput(
                // TODO：subscribe relation 需要使用 Subscribe Manager来进行控制订阅关系，现在是手工在 Producer/Consumer 两侧进行的控制
                new OutputTag<RowData>(this.subscribeTable.getFullName()) {
                }
        ), splitter);
    }

    @Override
    public String toString() {
        return "BaseSinkTask{"
                + "table=" + this.dstTable
                + ", primaryKey=" + primaryKeys.toString()
                + ", WriteType=" + writeType
                + ", partitionSpec=" + partitionSpec
                + '}';
    }
}