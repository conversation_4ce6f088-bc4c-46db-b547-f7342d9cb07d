package com.tencent.andata.dispatcher.cdc.sink;

import com.tencent.andata.dispatcher.cdc.operator.spliter.AIODataPKSplit;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.cdc.conf.builder.WriteTypeEnum;
import com.tencent.andata.utils.rowdata.MessageRowData;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.NotImplementedException;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.data.RowData;
import org.apache.iceberg.DistributionMode;
import org.apache.iceberg.Table;
import org.apache.iceberg.flink.FlinkSchemaUtil;
import org.apache.iceberg.flink.TableLoader;
import org.apache.iceberg.flink.sink.FlinkSink;

public class IcebergSinkActuator extends SinkActuator implements Serializable {
    private final Table table;
    private final TableLoader loader;
    private final WriteTypeEnum writeType;
    private final int parallelism;
    private final List<String> eqColumns;

    public IcebergSinkActuator(Table table,
                               TableLoader loader,
                               WriteTypeEnum writeType,
                               int parallelism,
                               List<String> eqColumns) {
        this.table = table;
        this.loader = loader;
        this.parallelism = parallelism;
        this.writeType = writeType;
        this.eqColumns = eqColumns;
    }

    public IcebergSinkActuator(Table table, TableLoader loader, int parallelism) {
        // Partial Init，固定部分参数。默认是Append模式。
        this(table, loader, WriteTypeEnum.APPEND, parallelism, new ArrayList<>());
    }

    @Override
    public Schema getSinkSchema() {
        return FlinkSchemaUtil.toSchema(this.table.schema()).toSchema();
    }

    public void sink(DataStream<RowData> ds, AIODataPKSplit aioDataPKSplit) {
        // TODO: 确认下 table.name 返回的值是不是table name
        Random rand = new Random();

        FlinkSink.Builder sinkBuilder = FlinkSink
                .forRowData(ds)
                .table(table)
                .tableLoader(loader)
                .writeParallelism(parallelism)
                .distributionMode(DistributionMode.HASH)
                .uidPrefix(String.format(
                        "iceberg.%s.%d",
                        table.name(),
                        rand.nextInt(100000)
                ));

        switch (writeType) {
            case UPSERT:
                // TODO: 如果eqColumn为空，会有问题。需要处理下
                sinkBuilder = sinkBuilder.equalityFieldColumns(eqColumns).upsert(true);
                break;
            case CHANGE_LOG:
            case APPEND:
                break;
            default:
                throw new NotImplementedException(String.format(
                        "Currently, IcebergSinkTask only support Append and UPSERT mode, %s is not supported",
                        writeType)
                );
        }
        sinkBuilder.append();
    }

    static class Builder extends BaseSinkActuatorBuilder {
        public static final int MAX_OPERATOR_PARALLELISM = 3;
        private static final IcebergCatalogReader icebergCatalogReader = IcebergCatalogReader.getInstance();

        @Override
        public IcebergSinkActuator build() {
            org.apache.iceberg.catalog.TableIdentifier tableIdentifier =
                    org.apache.iceberg.catalog.TableIdentifier.of(
                            this.tableIdentifier.getDbName(),
                            this.tableIdentifier.getTableName()
                    );
            // 这里先去掉Exist判断，因为这里是必须要存在对应表的，如果没有直接抛错误出来
            TableLoader loader = icebergCatalogReader.getTableLoaderInstance(tableIdentifier);
            Table table = loader.loadTable();

            boolean isTablePartitioned = table.spec().isPartitioned();

            return new IcebergSinkActuator(
                    table,
                    loader,
                    writeType,
                    isTablePartitioned ? MAX_OPERATOR_PARALLELISM : 1,
                    getEqualityColumns()
            );
        }

        public List<String> getEqualityColumns() {
            if (primaryKeys == null) {
                return null;
            }
            List<String> pkList = primaryKeys;
            if (partitionSpec == null) {
                return pkList;
            } else {
                // 如果有分区的话需要把分区字段也加上
                List<String> partitionFields = partitionSpec
                        .fields
                        .stream()
                        .map(f -> f.field)
                        .collect(Collectors.toList());
                // 需要去重，可能pk和分区键是同一个
                return Stream.of(pkList, partitionFields)
                        .flatMap(Collection::stream)
                        .distinct()
                        .collect(Collectors.toList());
            }
        }
    }
}