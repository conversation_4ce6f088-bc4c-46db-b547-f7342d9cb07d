package com.tencent.andata.dispatcher.cdc.sink;

import com.tencent.andata.dispatcher.cdc.task.SinkTask;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.commons.lang.NotImplementedException;

public class ActuatorFactory {
    public static BaseSinkActuatorBuilder getActuatorBuilder(SinkTask task) {
        BaseSinkActuatorBuilder builder;
        switch (task.dstTable.getDatabaseEnum()) {
            case ICEBERG:
                builder = new IcebergSinkActuator
                        .Builder()
                        .setTableIdentifier(task.dstTable);
                break;
            case ROCKS:
                builder = new SRSinkActuator
                        .Builder()
                        .setTableIdentifier(task.dstTable);
                break;
            case DATA_BUS:
                builder = new DatabusSinkActuator
                        .Builder()
                        .setSrcTableIdentifier(task.subscribeTable)
                        .setTableIdentifier(task.dstTable);
                break;
            default:
                throw new NotImplementedException("CDC only support iceberg sink task!");
        }
        return builder
                .setWriteType(task.writeType)
                .setPrimaryKeys(task.primaryKeys)
                .setPartitionSpec(task.partitionSpec)
                .setProperties(task.actuatorProperties);

    }
}