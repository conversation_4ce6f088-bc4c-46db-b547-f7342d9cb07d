package com.tencent.andata.dispatcher.kafka;

import com.tencent.andata.dct.api.source.kafka.builder.KafkaSourceStreamBuilder;
import com.tencent.andata.dct.api.source.kafka.deserializer.FlinkKafkaDeserializationSchema;
import com.tencent.andata.utils.DateFormatUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.JSONUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.operator.map.RowDataSplitCleanMap;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonRowDataDeserializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.TextNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Collector;
import org.apache.iceberg.BaseTable;
import org.apache.iceberg.DistributionMode;
import org.apache.iceberg.Table;
import org.apache.iceberg.TableMetadata;
import org.apache.iceberg.TableOperations;
import org.apache.iceberg.flink.TableLoader;
import org.apache.iceberg.flink.sink.FlinkSink;
import org.bouncycastle.util.Strings;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Optional;
import java.util.Properties;

/**
 * 风控群消息增量数据消费入库
 */
public class KafkaGroupMessageConsumer {

    public static JSONUtils jsonUtils = new JSONUtils();

    /**
     * 获取消息展示文本
     *
     * @param msgContent
     * @param msgType
     * @return
     * @throws JsonProcessingException
     */
    public static String getDisplayContent(String msgContent, String msgType) throws JsonProcessingException {
        try {
            StringBuilder content = new StringBuilder();
            ObjectNode msgData = jsonUtils.getJSONObjectNodeByString(msgContent);
            String text = parseMsg(msgData.get(msgType), msgType, content).toString();
            return text;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 解析消息
     *
     * @param statement
     * @param msgType
     * @param result
     * @return
     * @throws JsonProcessingException
     */
    public static StringBuilder parseMsg(JsonNode statement, String msgType, StringBuilder result)
            throws JsonProcessingException {
        HashMap<String, String> msgTransMap = new HashMap<String, String>() {{
            put("image", "[图片]");
            put("voice", "[语音]");
            put("video", "[视频]");
            put("file", "[文件]");
            put("chatrecord", "[聊天记录]");
        }};
        if (msgType.equals("text")) {
            if (statement instanceof TextNode) {
                result.append(jsonUtils.getJSONObjectNodeByString(statement.textValue()).get("content").asText());
            } else {
                result.append(statement.get("content").asText());
            }
        } else if (msgType.equals("mixed")) {
            JsonNode node = statement.get("item");
            Iterator<JsonNode> elements = node.elements();
            for (Iterator<JsonNode> it = elements; it.hasNext(); ) {
                ObjectNode item = (ObjectNode) it.next();
                result = parseMsg(item.get("content"), item.get("type").asText(), result);
            }
        } else {
            result.append(msgTransMap.getOrDefault(msgType, "[其他类型消息]"));
        }
        return result;
    }

    /**
     * 入口
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        String dwdGroupMessageEqFieldColumns = parameterTool.get("dwdGroupMessageEqFieldColumns");
        ArrayList<String> eqFieldColumns = new ArrayList<String>() {
        };
        eqFieldColumns.addAll(Arrays.asList(Strings.split(dwdGroupMessageEqFieldColumns, ',')));
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        // rainbow初始化
        RainbowUtils rainbow = new RainbowUtils(properties);
        // Catalog Reader
        IcebergCatalogReader icebergCatalogReader = new IcebergCatalogReader();
        // Flink Env
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        EnvironmentSettings settings = EnvironmentSettings
                .newInstance()
                .inStreamingMode()
                .build();
        StreamTableEnvironment tEnv = StreamTableEnvironment.create(env, settings);
        ArrayList<DataStream<String>> kafkaSourceList = new ArrayList<>();
        // Kafka Rainbow Group
        String groupMessageKafkaGroup = "smarty.public_opinion.kafka";
        ArrayList<String> groupMessageKafkaKeyList = new ArrayList<String>() {{
            add("private-cloud-dialog");
            add("qqgroup");
            add("knock");
            add("slack");
        }};
        // 读取多个Kafka
        String parallelism = parameterTool.get("parallelism", "1");
        for (String kafkaConfigKey : groupMessageKafkaKeyList) {
            ObjectNode mqConf = rainbow.getJSONValue(groupMessageKafkaGroup, kafkaConfigKey);
            if (mqConf == null) {
                System.out.printf("the config %s.%s is not exists.\n", groupMessageKafkaGroup, kafkaConfigKey);
                continue;
            }
            String topic = mqConf.get("topic").asText();
            String address = mqConf.get("address").asText();
            String groupID = mqConf.get("group_id").asText();
            String uid = String.format("kafka-source-%s-%s-%s", address, topic, groupID);
            // 这里使用底座的Kafka解析方法，后续会放在一起
            DataStream<String> ds = new KafkaSourceStreamBuilder()
                    .setAddress(address)
                    .setGroupID(groupID)
                    .setTopic(topic)
                    .setEnv(env)
                    .setDeserializationSchema(new FlinkKafkaDeserializationSchema())
                    .setParallelism(Integer.parseInt(parallelism))
                    .build();
            kafkaSourceList.add(ds);
        }
        Optional<DataStream<String>> opt = kafkaSourceList.stream().reduce(DataStream::union);
        assert opt.isPresent();
        // 群消息ODS表
        String icebergDatabase = parameterTool.get("icebergDatabase");
        String odsGroupMessageTableName = parameterTool.get("icebergODSGroupMessageTable");
        TableLoader odsGroupMessageTableLoader = icebergCatalogReader.getTableLoaderInstance(
                icebergDatabase,
                odsGroupMessageTableName
        );
        Table odsGroupMessageTable = odsGroupMessageTableLoader.loadTable();
        RowType groupMessageTableRowType = icebergCatalogReader.getTableRowType(odsGroupMessageTable);
        final JsonRowDataDeserializationSchema odsGroupMessageDeserializationSchema =
                new JsonRowDataDeserializationSchema(
                        groupMessageTableRowType,
                        InternalTypeInfo.of(groupMessageTableRowType),
                        false,
                        true,
                        TimestampFormat.SQL
                );

        // 所有Kafka数据都到一个流里面了，只用对这个流进行处理就行
        DataStream<RowData> odsGroupMessageDS = opt.get().flatMap(
                new FlatMapFunction<String, RowData>() {
                    @Override
                    public void flatMap(String s, Collector<RowData> collector) throws Exception {
                        RowData record =
                                odsGroupMessageDeserializationSchema.deserialize(
                                        s.getBytes(StandardCharsets.UTF_8));
                        // 替换这里数据的dst_table_name，后续和底座放在一起
                        GenericRowData genericRowData = (GenericRowData) record;
                        genericRowData.setField(0, StringData.fromString("ods_im_group_message_table"));
                        collector.collect(genericRowData);
                    }
                }
        ).map(
                new RowDataSplitCleanMap(
                        CDCUtils.convertRowTypeToFieldGetterTupleList(groupMessageTableRowType)
                )
        );
        // 群消息一层ODS入库
        FlinkSink.forRowData(odsGroupMessageDS)
                .table(odsGroupMessageTable)
                .tableLoader(odsGroupMessageTableLoader)
                .distributionMode(DistributionMode.HASH)
                .writeParallelism(Integer.parseInt(parallelism))
                .append();
        // ------------------------ DWD ------------------------------
        // DWD数据处理
        JSONUtils jsonUtils = new JSONUtils();
        String dwdGroupMessageTableName = parameterTool.get("icebergDWDGroupMessageTable");
        TableLoader dwdGroupMessageTableLoader = icebergCatalogReader.getTableLoaderInstance(
                icebergDatabase,
                dwdGroupMessageTableName
        );
        Table dwdGroupMessageTable = dwdGroupMessageTableLoader.loadTable();
        RowType dwdGroupMessageTableRowType = icebergCatalogReader.getTableRowType(dwdGroupMessageTable);
        final JsonRowDataDeserializationSchema dwdGroupMessageDeserializationSchema =
                new JsonRowDataDeserializationSchema(
                        dwdGroupMessageTableRowType,
                        InternalTypeInfo.of(dwdGroupMessageTableRowType),
                        false,
                        true,
                        TimestampFormat.SQL
                );
        SingleOutputStreamOperator<RowData> dwdGroupMessageDS = odsGroupMessageDS.flatMap(
                new FlatMapFunction<RowData, RowData>() {
                    @Override
                    public void flatMap(RowData rowData, Collector<RowData> collector) throws Exception {
                        try {
                            String message = rowData.getString(5).toString();
                            ObjectNode msgData = jsonUtils.getJSONObjectNodeByString(message);
                            String msgID = msgData.get("msg_id").asText();
                            String groupID = msgData.get("group_id").asText();
                            msgData.put(
                                    "pk",
                                    String.format("%s-%s", groupID, msgID)
                            );
                            long msgTimestamp = msgData.get("msg_time").asLong();
                            msgData.put(
                                    "msg_time",
                                    DateFormatUtils.cstTimestampToUTCString(msgTimestamp)
                            );
                            msgData.put(
                                    "display_content",
                                    getDisplayContent(
                                            msgData.get("content").toString(),
                                            msgData.get("msg_type").asText()
                                    )
                            );
                            System.out.println(msgData.toString());
                            collector.collect(dwdGroupMessageDeserializationSchema.deserialize(
                                    msgData.toString().getBytes(StandardCharsets.UTF_8)));
                        } catch (Exception e) {
                            // 处理报错的话则跳过
                            e.printStackTrace();
                        }
                    }
                });
        // 设置版本
        TableOperations upsertTableOperation = ((BaseTable) dwdGroupMessageTable).operations();
        TableMetadata upsertTableMetadata = upsertTableOperation.current();
        upsertTableOperation.commit(upsertTableMetadata, upsertTableMetadata.upgradeToFormatVersion(2));
        // Sink入库
        FlinkSink.forRowData(dwdGroupMessageDS)
                .table(dwdGroupMessageTable)
                .tableLoader(dwdGroupMessageTableLoader)
                .distributionMode(DistributionMode.HASH)
                .writeParallelism(Integer.parseInt(parallelism))
                .equalityFieldColumns(eqFieldColumns)
                .upsert(true)
                .append()
                .disableChaining();

        dwdGroupMessageDS.print("dwd");
        env.enableCheckpointing(60 * 1000);
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setMinPauseBetweenCheckpoints(45 * 1000L);
        checkpointConfig.setMaxConcurrentCheckpoints(1);
        checkpointConfig.setCheckpointTimeout(60 * 1000L);
        checkpointConfig.setTolerableCheckpointFailureNumber(10);
        env.execute("Group Message To Iceberg Table");
    }
}