package com.tencent.andata.dispatcher.cdc.task;

import com.tencent.andata.dispatcher.cdc.struct.TaskParams;
import com.tencent.andata.utils.rowdata.MessageRowData;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Collection;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.data.RowData;

public class SyncTask {
    public static final int MAX_PARALLELISM = 5;
    public final Set<MonitorTask> monitorTasks; // 数据源只有一个
    public final List<SinkTask> sinkTasks; // 目的地可能有多个，跟数据源是多对一的关系
    public final ETLTask aioETLTask;

    public SyncTask(Set<MonitorTask> monitorTasks,
                    List<SinkTask> sinkTasks,
                    ETLTask aioETLTask) {
        this.monitorTasks = monitorTasks;
        this.sinkTasks = sinkTasks;
        this.aioETLTask = aioETLTask;
    }

    public void sync(StreamExecutionEnvironment env) {
        // 将所有的SRC都初始化，Union成一个
        // TODO：后续改成并发，当前还好
        DataStream<MessageRowData> ds = monitorTasks
                .stream().map(mTask -> mTask.monitor(env)).reduce(DataStream::union).get();

        SingleOutputStreamOperator<RowData> dsWithTag =
                this.aioETLTask.transform(ds, SyncTask.MAX_PARALLELISM);

        sinkTasks.forEach(sinkTask -> sinkTask.sink(dsWithTag, this.aioETLTask.splitter));
    }

    /***
     * fromParams .
     * @param params .
     * @return .
     * @throws Exception .
     */
    public static SyncTask fromParams(List<TaskParams> params) throws Exception {
        List<SinkTask> sinkTasks = new ArrayList<>();
        Map<String, MonitorTask> monitorTaskMap = new HashMap<>();

        Map<MonitorTask, List<SinkTask>> relation = new HashMap<>();

        for (TaskParams param : params) {
            String dbName = param.srcTableID.getDbName();
            // GetOrCreate
            MonitorTask monitorTask = monitorTaskMap.get(dbName);
            SinkTask sinkTask = new SinkTask(param);
            if (monitorTask == null) {
                monitorTask = new MonitorTask(param);
                monitorTaskMap.put(dbName, monitorTask);
                relation.put(monitorTask, new ArrayList<>());
            }

            monitorTask.addTable(param);
            sinkTasks.add(sinkTask);
            relation.get(monitorTask).add(sinkTask); // 构建关联关系
        }

        System.out.println("start sink preparing....");

//        for (SinkTask sinkTask : sinkTasks) {
//            sinkTask.prepare();
//        }

        // 并发Prepare，提升初始化速度
        // 至于为什么并发Prepare，主要原因还是因为IceActuator需要从Hive捞Ice的Schema，性能比较差
        ThreadPoolExecutor threadPoolExecutor =
                new ThreadPoolExecutor(5, 10, 0L, TimeUnit.MILLISECONDS,
                        new LinkedBlockingQueue<>());

        List<? extends Future<?>> futures = sinkTasks
                .stream()
                .map(sinkTask -> threadPoolExecutor.submit(sinkTask::prepare))
                .collect(Collectors.toList());

        threadPoolExecutor.shutdown();

        futures.forEach(future -> {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        });

        if (!threadPoolExecutor.awaitTermination(5, TimeUnit.MINUTES)) {
            throw new RuntimeException("Table Loader init Exception");
        }

        System.out.println("sink preparing ends");
        Collection<MonitorTask> tasks = monitorTaskMap.values();

        return new SyncTask(new HashSet<>(tasks), sinkTasks, new ETLTask(relation));
    }
}