package com.tencent.andata.dispatcher.cdc.deserializer;

import static org.apache.flink.util.Preconditions.checkNotNull;

import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.operator.map.RowDataFieldSwapMap;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.ververica.cdc.debezium.DebeziumDeserializationSchema;
import com.ververica.cdc.debezium.table.DeserializationRuntimeConverter;
import com.ververica.cdc.debezium.table.DeserializationRuntimeConverterFactory;
import com.ververica.cdc.debezium.table.RowDataDebeziumDeserializeSchema;
import com.ververica.cdc.debezium.utils.TemporalConversions;
import io.debezium.data.Envelope;
import io.debezium.data.SpecialValueDecimal;
import io.debezium.data.VariableScaleDecimal;
import io.debezium.time.MicroTime;
import io.debezium.time.MicroTimestamp;
import io.debezium.time.NanoTime;
import io.debezium.time.NanoTimestamp;
import io.debezium.time.Timestamp;
import java.io.Serializable;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.regex.Pattern;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.data.DecimalData;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.data.TimestampData;
import org.apache.flink.table.types.logical.DecimalType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.kafka.connect.data.Decimal;
import org.apache.kafka.connect.data.Field;
import org.apache.kafka.connect.data.Schema;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.source.SourceRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class CDCDBRowDataDeserializeSchema implements DebeziumDeserializationSchema<MessageRowData> {
    private static final long serialVersionUID = 12L;
    protected static final Logger LOG = LoggerFactory.getLogger(RowDataFieldSwapMap.class);
    public final HashMap<TableIdentifier, DeserializationRuntimeConverter> tableIdentifierHashMap;
    private final String dbName;

    public static Builder newBuilder(String dbName) {
        return new Builder(dbName);
    }

    /**
     * 一个数据库一个监控，通过TableIdentifier映射进行数据转换
     *
     * @param physicalDataTypeMap         数据库表RowType映射
     * @param serverTimeZone
     * @param userDefinedConverterFactory
     */
    public CDCDBRowDataDeserializeSchema(
            String dbName,
            Map<TableIdentifier, RowType> physicalDataTypeMap,
            ZoneId serverTimeZone,
            DeserializationRuntimeConverterFactory userDefinedConverterFactory
    ) {
        this.tableIdentifierHashMap = new HashMap<>();
        this.dbName = dbName;
        physicalDataTypeMap.forEach((identifier, rowType) -> tableIdentifierHashMap.put(
                identifier,
                createConverter(
                        checkNotNull(rowType),
                        serverTimeZone,
                        userDefinedConverterFactory
                )
        ));
    }

    /**
     * 获取Table Converter对数据进行转换
     *
     * @param record
     * @param out
     * @throws Exception
     */
    @Override
    public void deserialize(SourceRecord record, Collector<MessageRowData> out) throws Exception {
        Envelope.Operation op = Envelope.operationFor(record);
        Struct value = (Struct) record.value();
        TableIdentifier sourceTableIdentifier = getSourceTableIdentifierFromValue(value);
        DeserializationRuntimeConverter physicalConverter = tableIdentifierHashMap.get(sourceTableIdentifier);
        // 这里报错一定要抛出来，要不都不知道为什么缺数据
        Schema valueSchema = record.valueSchema();
        GenericRowData res;
        try {
            if (op == Envelope.Operation.CREATE || op == Envelope.Operation.READ) {
                // insert
                res = extractAfterRow(value, valueSchema, physicalConverter);
                res.setRowKind(RowKind.INSERT);
            } else if (op == Envelope.Operation.DELETE) {
                // delete
                res = extractBeforeRow(value, valueSchema, physicalConverter);
                res.setRowKind(RowKind.DELETE);
            } else {
                // after
                res = extractAfterRow(value, valueSchema, physicalConverter);
                res.setRowKind(RowKind.UPDATE_AFTER);
            }
        } catch (NullPointerException e) {
            // 正常不应该跑这里来的
            LOG.error(String.format("runtime err. running context:\r\n tbl map : %s\r\nsrc tbID: %s",
                    tableIdentifierHashMap, sourceTableIdentifier
            ));
            throw new RuntimeException(e);
        }
        MessageRowDataAttr messageRowDataAttr = new MessageRowDataAttr();
        messageRowDataAttr.setSrcTable(sourceTableIdentifier);
        MessageRowData messageRowData = MessageRowData.convertFromGenericRowData(res, messageRowDataAttr);
        out.collect(messageRowData);
    }

    private TableIdentifier getSourceTableIdentifierFromValue(Struct value) {
        Struct source = (Struct) value.get("source");
        String tableName = source.getString("table");
        // 不需要从数据里捞DBName了，现在一个DB只会有一个实例来进行监听，哪怕在一个实例里也需要起多个监听器
        // String dbName = source.getString("db");
        String connector = source.getString("connector").toUpperCase();

        DatabaseEnum dbEnum = DatabaseEnum.valueOf(connector.equals("POSTGRESQL") ? "PGSQL" : connector);
        String schemaName;
        try {
            switch (dbEnum) {
                case PGSQL:
                    schemaName = source.getString("schema");
                    break;
                case MYSQL:
                default:
                    schemaName = "";
                    break;
            }
        } catch (Exception e) {
            LOG.error(String.valueOf(value));
            throw e;
        }

        return new TableIdentifier(
                dbEnum,
                this.dbName,
                schemaName,
                tableName
        );
    }

    private GenericRowData extractAfterRow(
            Struct value,
            Schema valueSchema,
            DeserializationRuntimeConverter physicalConverter
    ) throws Exception {
        Schema afterSchema = valueSchema.field(Envelope.FieldName.AFTER).schema();
        Struct after = value.getStruct(Envelope.FieldName.AFTER);
        return (GenericRowData) physicalConverter.convert(after, afterSchema);
    }

    private GenericRowData extractBeforeRow(
            Struct value,
            Schema valueSchema,
            DeserializationRuntimeConverter physicalConverter
    ) throws Exception {
        Schema beforeSchema = valueSchema.field(Envelope.FieldName.BEFORE).schema();
        Struct before = value.getStruct(Envelope.FieldName.BEFORE);
        return (GenericRowData) physicalConverter.convert(before, beforeSchema);
    }

    @Override
    public TypeInformation<MessageRowData> getProducedType() {
        return TypeInformation.of(MessageRowData.class);
    }

    // -------------------------------------------------------------------------------------
    // Builder
    // -------------------------------------------------------------------------------------

    /**
     * Builder of {@link CDCDBRowDataDeserializeSchema}.
     */
    public static class Builder {
        private Map<TableIdentifier, RowType> physicalRowTypeMap;
        private final ZoneId serverTimeZone = ZoneId.of("UTC");
        private final DeserializationRuntimeConverterFactory userDefinedConverterFactory =
                DeserializationRuntimeConverterFactory.DEFAULT;
        private final String dbName;

        public Builder(String dbName) {
            this.dbName = dbName;
            this.physicalRowTypeMap = new HashMap<>();
        }

        public Builder setPhysicalRowTypeMap(Map<TableIdentifier, RowType> physicalRowTypeMap) {
            this.physicalRowTypeMap = physicalRowTypeMap;
            return this;
        }

        public CDCDBRowDataDeserializeSchema build() {
            return new CDCDBRowDataDeserializeSchema(
                    this.dbName,
                    physicalRowTypeMap,
                    serverTimeZone,
                    userDefinedConverterFactory
            );
        }
    }

    // -------------------------------------------------------------------------------------
    // Runtime Converters
    // -------------------------------------------------------------------------------------

    /**
     * Creates a runtime converter which is null safe.
     */
    private static DeserializationRuntimeConverter createConverter(
            LogicalType type,
            ZoneId serverTimeZone,
            DeserializationRuntimeConverterFactory userDefinedConverterFactory) {
        return wrapIntoNullableConverter(
                createNotNullConverter(type, serverTimeZone, userDefinedConverterFactory)
        );
    }

    // --------------------------------------------------------------------------------
    // IMPORTANT! We use anonymous classes instead of lambdas for a reason here. It is
    // necessary because the maven shade plugin cannot relocate classes in
    // SerializedLambdas (MSHADE-260).
    // --------------------------------------------------------------------------------

    /**
     * Creates a runtime converter which assuming input object is not null.
     */
    public static DeserializationRuntimeConverter createNotNullConverter(
            LogicalType type,
            ZoneId serverTimeZone,
            DeserializationRuntimeConverterFactory userDefinedConverterFactory) {
        // user defined converter has a higher resolve order
        Optional<DeserializationRuntimeConverter> converter =
                userDefinedConverterFactory.createUserDefinedConverter(type, serverTimeZone);
        if (converter.isPresent()) {
            return converter.get();
        }

        // if no matched user defined converter, fallback to the default converter
        switch (type.getTypeRoot()) {
            case NULL:
                return new DeserializationRuntimeConverter() {

                    private static final long serialVersionUID = 1L;

                    @Override
                    public Object convert(Object dbzObj, Schema schema) {
                        return null;
                    }
                };
            case BOOLEAN:
                return convertToBoolean();
            case TINYINT:
                return new DeserializationRuntimeConverter() {

                    private static final long serialVersionUID = 1L;

                    @Override
                    public Object convert(Object dbzObj, Schema schema) {
                        return Byte.parseByte(dbzObj.toString());
                    }
                };
            case SMALLINT:
                return new DeserializationRuntimeConverter() {

                    private static final long serialVersionUID = 1L;

                    @Override
                    public Object convert(Object dbzObj, Schema schema) {
                        return Short.parseShort(dbzObj.toString());
                    }
                };
            case INTEGER:
            case INTERVAL_YEAR_MONTH:
                return convertToInt();
            case BIGINT:
            case INTERVAL_DAY_TIME:
                return convertToLong();
            case DATE:
                return convertToDate();
            case TIME_WITHOUT_TIME_ZONE:
                return convertToTime();
            case TIMESTAMP_WITHOUT_TIME_ZONE:
                return convertToTimestamp(serverTimeZone);
            case TIMESTAMP_WITH_LOCAL_TIME_ZONE:
                return convertToLocalTimeZoneTimestamp(serverTimeZone);
            case FLOAT:
                return convertToFloat();
            case DOUBLE:
                return convertToDouble();
            case CHAR:
            case VARCHAR:
                return convertToString();
            case BINARY:
            case VARBINARY:
                return convertToBinary();
            case DECIMAL:
                return createDecimalConverter((DecimalType) type);
            case ROW:
                return createRowConverter(
                        (RowType) type, serverTimeZone, userDefinedConverterFactory);
            case ARRAY:
            case MAP:
            case MULTISET:
            case RAW:
            default:
                throw new UnsupportedOperationException("Unsupported type: " + type);
        }
    }

    private static DeserializationRuntimeConverter convertToBoolean() {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                if (dbzObj instanceof Boolean) {
                    return dbzObj;
                } else if (dbzObj instanceof Byte) {
                    return (Byte) dbzObj == 1;
                } else {
                    return dbzObj instanceof Short ? (Short) dbzObj == 1 : Boolean.parseBoolean(dbzObj.toString());
                }
            }
        };
    }

    private static DeserializationRuntimeConverter convertToInt() {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                if (dbzObj instanceof Integer) {
                    return dbzObj;
                } else {
                    return dbzObj instanceof Long ? ((Long) dbzObj).intValue() : Integer.parseInt(dbzObj.toString());
                }
            }
        };
    }

    private static DeserializationRuntimeConverter convertToLong() {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                if (dbzObj instanceof Integer) {
                    return ((Integer) dbzObj).longValue();
                } else {
                    return dbzObj instanceof Long ? dbzObj : Long.parseLong(dbzObj.toString());
                }
            }
        };
    }

    private static DeserializationRuntimeConverter convertToDouble() {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                if (dbzObj instanceof Float) {
                    return ((Float) dbzObj).doubleValue();
                } else if (dbzObj instanceof Double) {
                    return dbzObj;
                } else {
                    return Double.parseDouble(dbzObj.toString());
                }
            }
        };
    }

    private static DeserializationRuntimeConverter convertToFloat() {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                if (dbzObj instanceof Float) {
                    return dbzObj;
                } else if (dbzObj instanceof Double) {
                    return ((Double) dbzObj).floatValue();
                } else {
                    return Float.parseFloat(dbzObj.toString());
                }
            }
        };
    }

    private static DeserializationRuntimeConverter convertToDate() {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                return (int) TemporalConversions.toLocalDate(dbzObj).toEpochDay();
            }
        };
    }

    private static DeserializationRuntimeConverter convertToTime() {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                if (dbzObj instanceof Long) {
                    switch (schema.name()) {
                        case MicroTime.SCHEMA_NAME:
                            return (int) ((long) dbzObj / 1000);
                        case NanoTime.SCHEMA_NAME:
                            return (int) ((long) dbzObj / 1000_000);
                        default:
                            return dbzObj;
                    }
                } else if (dbzObj instanceof Integer) {
                    return dbzObj;
                }
                // get number of milliseconds of the day
                return TemporalConversions.toLocalTime(dbzObj).toSecondOfDay() * 1000;
            }
        };
    }

    private static DeserializationRuntimeConverter convertToTimestamp(ZoneId serverTimeZone) {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                if (dbzObj instanceof Long) {
                    if (Pattern.matches(".*debezium.time.Timestamp.*", schema.name()))
                    {
                        return TimestampData.fromEpochMillis((Long) dbzObj);
                    } else if (Pattern.matches(".*debezium.time.MicroTimestamp.*", schema.name())) {
                        long micro = (long) dbzObj;
                        return TimestampData.fromEpochMillis(
                                micro / 1000, (int) (micro % 1000 * 1000));
                    } else if (Pattern.matches(".*debezium.time.NanoTimestamp.*", schema.name())) {
                        long nano = (long) dbzObj;
                        return TimestampData.fromEpochMillis(
                                nano / 1000_000, (int) (nano % 1000_000));
                    } else {
                        return dbzObj;
                    }
                }
                LocalDateTime localDateTime =
                        TemporalConversions.toLocalDateTime(dbzObj, serverTimeZone);
                return TimestampData.fromLocalDateTime(localDateTime);
            }
        };
    }

    private static DeserializationRuntimeConverter convertToLocalTimeZoneTimestamp(
            ZoneId serverTimeZone) {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                if (dbzObj instanceof String) {
                    String str = (String) dbzObj;
                    // TIMESTAMP_LTZ type is encoded in string type
                    Instant instant = Instant.parse(str);
                    return TimestampData.fromLocalDateTime(
                            LocalDateTime.ofInstant(instant, serverTimeZone));
                } else if (dbzObj instanceof Number) {
                    // 这段是我们自己内部加上去的，原因在于大部分业务库直接使用的都是timestamp，而不是较正规的timestamp with tz
                    // 这导致直接存的都是时间戳，而不带时区；timestamp ltz最大的问题在于FieldGetter的逻辑跟timestamp不一样
                    // timestamp直接就会返回一个Integer，在下游消费的时候会导致异常。
                    // 所以这里加一个丑陋的patch来适配不太符合规范的内部业务
                    long epochMilli = ((Number) dbzObj).longValue();

                    if (dbzObj instanceof Integer) {
                        // 为Unix Second
                        epochMilli *= 1000;
                    } else if (dbzObj instanceof Long) {
                        if (Pattern.matches(".*debezium.time.MicroTimestamp.*", schema.name())) {
                            epochMilli /= 1000;
                        } else if (Pattern.matches(".*debezium.time.NanoTimestamp.*", schema.name())) {
                            epochMilli /= 1000_000;
                        } else if (!Pattern.matches(".*debezium.time.Timestamp.*", schema.name())) {
                            return epochMilli;
                        }
                    }

                    Instant instant = Instant.ofEpochMilli(epochMilli);
                    return TimestampData.fromLocalDateTime(
                            LocalDateTime.ofInstant(instant, serverTimeZone));
                }
                throw new IllegalArgumentException(
                        "Unable to convert to TimestampData from unexpected value '"
                                + dbzObj
                                + "' of type "
                                + dbzObj.getClass().getName());
            }
        };
    }

    private static DeserializationRuntimeConverter convertToString() {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                return StringData.fromString(dbzObj.toString());
            }
        };
    }

    private static DeserializationRuntimeConverter convertToBinary() {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                if (dbzObj instanceof byte[]) {
                    return dbzObj;
                } else if (dbzObj instanceof ByteBuffer) {
                    ByteBuffer byteBuffer = (ByteBuffer) dbzObj;
                    byte[] bytes = new byte[byteBuffer.remaining()];
                    byteBuffer.get(bytes);
                    return bytes;
                } else {
                    throw new UnsupportedOperationException(
                            "Unsupported BYTES value type: " + dbzObj.getClass().getSimpleName());
                }
            }
        };
    }

    private static DeserializationRuntimeConverter createDecimalConverter(DecimalType decimalType) {
        final int precision = decimalType.getPrecision();
        final int scale = decimalType.getScale();
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) {
                BigDecimal bigDecimal;
                if (dbzObj instanceof byte[]) {
                    // decimal.handling.mode=precise
                    bigDecimal = Decimal.toLogical(schema, (byte[]) dbzObj);
                } else if (dbzObj instanceof String) {
                    // decimal.handling.mode=string
                    bigDecimal = new BigDecimal((String) dbzObj);
                } else if (dbzObj instanceof Double) {
                    // decimal.handling.mode=double
                    bigDecimal = BigDecimal.valueOf((Double) dbzObj);
                } else {
                    if (VariableScaleDecimal.LOGICAL_NAME.equals(schema.name())) {
                        SpecialValueDecimal decimal =
                                VariableScaleDecimal.toLogical((Struct) dbzObj);
                        bigDecimal = decimal.getDecimalValue().orElse(BigDecimal.ZERO);
                    } else {
                        // fallback to string
                        bigDecimal = new BigDecimal(dbzObj.toString());
                    }
                }
                return DecimalData.fromBigDecimal(bigDecimal, precision, scale);
            }
        };
    }

    private static DeserializationRuntimeConverter createRowConverter(
            RowType rowType,
            ZoneId serverTimeZone,
            DeserializationRuntimeConverterFactory userDefinedConverterFactory) {
        final DeserializationRuntimeConverter[] fieldConverters =
                rowType.getFields().stream()
                        .map(RowType.RowField::getType)
                        .map(
                                logicType ->
                                        createConverter(
                                                logicType,
                                                serverTimeZone,
                                                userDefinedConverterFactory))
                        .toArray(DeserializationRuntimeConverter[]::new);
        final String[] fieldNames = rowType.getFieldNames().toArray(new String[0]);

        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) throws Exception {
                Struct struct = (Struct) dbzObj;
                int arity = fieldNames.length;
                GenericRowData row = new GenericRowData(arity);
                for (int i = 0; i < arity; i++) {
                    String fieldName = fieldNames[i];
                    Field field = schema.field(fieldName);
                    if (field == null) {
                        row.setField(i, null);
                    } else {
                        Object fieldValue = struct.getWithoutDefault(fieldName);
                        Schema fieldSchema = schema.field(fieldName).schema();
                        Object convertedField =
                                convertField(fieldConverters[i], fieldValue, fieldSchema);
                        row.setField(i, convertedField);
                    }
                }
                return row;
            }
        };
    }

    private static Object convertField(
            DeserializationRuntimeConverter fieldConverter, Object fieldValue, Schema fieldSchema)
            throws Exception {
        if (fieldValue == null) {
            return null;
        } else {
            return fieldConverter.convert(fieldValue, fieldSchema);
        }
    }

    private static DeserializationRuntimeConverter wrapIntoNullableConverter(
            DeserializationRuntimeConverter converter) {
        return new DeserializationRuntimeConverter() {

            private static final long serialVersionUID = 1L;

            @Override
            public Object convert(Object dbzObj, Schema schema) throws Exception {
                if (dbzObj == null) {
                    return null;
                }
                return converter.convert(dbzObj, schema);
            }
        };
    }
}