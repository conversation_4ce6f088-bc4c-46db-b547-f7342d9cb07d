package com.tencent.andata.dispatcher.cdc.sink;

import com.tencent.andata.dispatcher.cdc.operator.spliter.AIODataPKSplit;
import com.tencent.andata.utils.rowdata.MessageRowData;
import java.io.Serializable;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.data.RowData;

public abstract class SinkActuator implements Serializable {
    public abstract void sink(DataStream<RowData> ds, AIODataPKSplit splitter); // Sink这里，只做Sink

    public abstract Schema getSinkSchema();
}