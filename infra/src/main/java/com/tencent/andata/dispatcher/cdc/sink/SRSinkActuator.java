package com.tencent.andata.dispatcher.cdc.sink;

import com.starrocks.connector.flink.row.sink.StarRocksTableRowTransformer;
import com.starrocks.connector.flink.table.sink.SinkFunctionFactory;
import com.starrocks.connector.flink.table.sink.StarRocksSinkOptions;
import com.tencent.andata.dispatcher.cdc.operator.spliter.AIODataPKSplit;
import com.tencent.andata.singularity.settings.Settings;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.struct.DatabaseConf;

import java.io.Serializable;
import java.util.Map;

import lombok.SneakyThrows;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.catalog.exceptions.CatalogException;
import org.apache.flink.table.data.RowData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.tencent.andata.singularity.settings.Const.OLAP_SR_CPRB;
import static com.tencent.andata.utils.TableUtils.convertFlinkSchemaToTableSchema;
import static com.tencent.andata.utils.TableUtils.convertTableSchemaToTypeInformation;

public class SRSinkActuator extends SinkActuator implements Serializable {
    protected static final Logger LOG = LoggerFactory.getLogger(SRSinkActuator.class);

    StarRocksSinkOptions options;
    TableSchema schema;
    TypeInformation<RowData> typeInfo;

    public SRSinkActuator(StarRocksSinkOptions opts, TableSchema schema, TypeInformation<RowData> typeInfo) {
        this.options = opts;
        this.schema = schema;
        this.typeInfo = typeInfo;
    }

    @Override
    public Schema getSinkSchema() {
        return this.schema.toSchema();
    }

    @Override
    public void sink(DataStream<RowData> ds, AIODataPKSplit splitter) {
        // 至于为什么不用StarRocksSink，主要原因在于StarRocksSink主要用在Row，而我们基本都是使用的是Table的RowData
        // 这里直接用SinkFunction
        ds.addSink(
                SinkFunctionFactory.createSinkFunction(
                        this.options,
                        schema,
                        new StarRocksTableRowTransformer(this.typeInfo)
                )
        ).name(
                String.format(
                        "%s.%s",
                        this.options.getDatabaseName(),
                        this.options.getTableName()
                )
        );
    }

    public static class Builder extends BaseSinkActuatorBuilder {
        @SneakyThrows
        @Override
        public SinkActuator build() throws RuntimeException {
            // 通过Settings界面捞对应的配置信息
            Map<String, String> settings = Settings.extractAll(
                    OLAP_SR_CPRB + "." + this.tableIdentifier.getDbName()
            );
            settings.put("table-name", this.tableIdentifier.getTableName());

            StarRocksSinkOptions.Builder builder = StarRocksSinkOptions.builder();
            settings.forEach(builder::withProperty);
            StarRocksSinkOptions options = builder.build();

            // 为了用FlinkCatalog获取Schema，需要根据Settings构建DatabaseConf
            String[] hostAndPort = settings.get("jdbc-url").split("//")[1].split(":");
            String host = hostAndPort[0], port = hostAndPort[1];
            DatabaseConf dbConf = new DatabaseConf();
            dbConf.dbHost = host;
            dbConf.dbPort = Integer.valueOf(port);

            dbConf.password = settings.get("password");
            dbConf.userName = settings.get("username");
            dbConf.dbName = settings.get("database-name");
            try {
                // TODO: 需要排查下properties为什么会包含PG的user pwd
                Schema schema = CDCUtils.getRdbmsSchema(dbConf, this.tableIdentifier, this.properties);
                TableSchema tableSchema = convertFlinkSchemaToTableSchema(schema);
                return new SRSinkActuator(
                        options,
                        tableSchema,
                        convertTableSchemaToTypeInformation(tableSchema)
                );
            } catch (CatalogException e) {
                LOG.error(
                        String.format(
                                "Catalog loading exception, err: %s, dbConf: %s, tbl: %s, properties: %s",
                                e,
                                dbConf,
                                tableIdentifier,
                                properties
                        )
                );
                throw e;
            }
        }
    }
}