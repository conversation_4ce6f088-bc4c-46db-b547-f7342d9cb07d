package com.tencent.andata.dispatcher.cdc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.tencent.andata.dispatcher.cdc.task.SyncTask;
import com.tencent.andata.singularity.settings.Settings;
import com.tencent.andata.singularity.settings.extractor.RainbowExtractor;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.dispatcher.cdc.struct.TaskParams;
import com.tencent.andata.utils.cdc.conf.input.InputTaskParams;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class RdmsDispatcher {

    /**
     * 从数据库通过CDC分发到不同的地方去
     * 基于DB级别进行实例合并，哪怕是一个实例中的多个DB，仍然也会跑多个同步任务（或者后面直接把配置的DB跟实际的DB写成一致的）
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        Preconditions.checkState(args.length > 0);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        String[] syncDbNameList = parameterTool.get("syncDbName").split(",");
        // 初始化环境
        RainbowUtils rainbowUtils = new RainbowUtils(PropertyUtils.loadProperties("env.properties"));

        // 创建ObjectMapper对象
        ObjectMapper objectMapper = new ObjectMapper();
        // 创建一个List对象来存储解析后的数据
        List<Map<String, Object>> mergedList = new ArrayList<>();
        for (String syncDbName : syncDbNameList) {
            String syncInfo = rainbowUtils.getStringValue("cdc.conf", syncDbName);
            // 将字符串解析为List对象
            List<Map<String, Object>> list = objectMapper.readValue(
                    syncInfo, new TypeReference<List<Map<String, Object>>>(){});
            // 将解析后的List对象合并到mergedList中
            mergedList.addAll(list);
        }
        String inputParam = objectMapper.writeValueAsString(mergedList);
        // Facade Init
        Settings.setExtractor(new RainbowExtractor(rainbowUtils));

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // 从Arg里读取配置，生成TaskParam
        // TODO：这个后面替换成Conf类，保证不会跨层解析底层数据
        List<TaskParams> taskParamsList = TaskParams
                .fromInputTaskParams(InputTaskParams.fromJson(inputParam));

        if (!taskParamsList.isEmpty()) {
            // 如果入参不为空，那么启动同步任务，生成DAG
            SyncTask.fromParams(taskParamsList).sync(env);
            // 设置Flink参数
            CDCUtils.setCheckpointConf(env.getCheckpointConfig());
            // 启动Flink环境
            env.execute();
        } else {
            System.out.println("CDCDSUnion is null");
        }
    }
}