package com.tencent.andata.dispatcher.iceberg;

import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.JSONUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.operator.map.RowDataSplitCleanMap;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonRowDataDeserializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Collector;
import org.apache.iceberg.DistributionMode;
import org.apache.iceberg.Table;
import org.apache.iceberg.flink.TableLoader;
import org.apache.iceberg.flink.sink.FlinkSink;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;

public class L2OdsExtractFromL1Ods {
    /**
     * 入口
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        String oldDataTopic = parameterTool.get("oldDataTopic");
        if (oldDataTopic == null) {
            throw new Exception("oldDataTopic should not be null");
        }
        final IcebergCatalogReader catalog = new IcebergCatalogReader();


        // table env config
        Configuration configuration = flinkEnv.streamTEnv().getConfig().getConfiguration();
        configuration.setString("execution.checkpointing.interval", "60s");
        configuration.setString("execution.runtime-mode", "streaming");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        configuration.setString("table.dynamic-table-options.enabled", "true");
        configuration.setString("table.exec.mini-batch.size", "5000");
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "3 s");
        configuration.setString("table.exec.resource.default-parallelism", parameterTool.get("parallelism", "1"));
        configuration.setString("table.exec.iceberg.infer-source-parallelism", "false");

        // 注册一层ODS
        String icebergDbName = parameterTool.get("icebergDbName");
        String l1OdsTableName = parameterTool.get("l1OdsTableName");
        Table l1OdsTable = catalog.getTableInstance(icebergDbName, l1OdsTableName);
        IcebergTableBuilderStrategy l1OdsTableBuilderStrategy = new IcebergTableBuilderStrategy(l1OdsTable);
        String l1OdsTableDDL = FlinkTableDDL
                .builder()
                .flinkTableName("ods_distribute_table_p")
                .tableBuilderStrategy(l1OdsTableBuilderStrategy)
                .build();
        TableUtils.registerTable(
                flinkEnv.streamTEnv(),
                l1OdsTableDDL
        );
        // 根据tableName提取一层ODS的数据
        String selectDataSql = ""
                + "select \n"
                + "    *\n"
                + "from ods_distribute_table_p\n"
                + "where dst_table_name = '%s'";
        String dstTableName = parameterTool.get("dstTableName");
        org.apache.flink.table.api.Table table = flinkEnv.streamTEnv().sqlQuery(
                String.format(
                        selectDataSql,
                        dstTableName
                )
        );
        DataStream<RowData> l1OdsDS = flinkEnv.streamTEnv().toAppendStream(table, RowData.class);
        // 二层ODS数据处理
        String l2OdsTableName = parameterTool.get("l2OdsTableName");
        TableLoader l2OdsTableLoader = catalog.getTableLoaderInstance(
                icebergDbName,
                l2OdsTableName
        );
        Table l2OdsTable = l2OdsTableLoader.loadTable();
        RowType l2OdsTableRowType = catalog.getTableRowType(l2OdsTable);
        final JsonRowDataDeserializationSchema l2OdsTableDeserializationSchema =
                new JsonRowDataDeserializationSchema(
                        l2OdsTableRowType,
                        InternalTypeInfo.of(l2OdsTableRowType),
                        false,
                        true,
                        TimestampFormat.SQL
                );
        JSONUtils jsonUtils = new JSONUtils();
        SingleOutputStreamOperator<RowData> l2DataDS = l1OdsDS.flatMap(new FlatMapFunction<RowData, String>() {
                    @Override
                    public void flatMap(RowData rowData, Collector<String> collector) throws Exception {
                        String message = rowData.getString(5).toString();
                        ArrayList<ObjectNode> dataList = jsonUtils.getJSONObjectArrayByString(message);
                        for (ObjectNode data : dataList) {
                            if (data.has("topic") && oldDataTopic.equals(data.get("topic").asText())) {
                                // 旧数据格式
                                String value = data.get("value").asText();
                                ArrayList<ObjectNode> valueList = jsonUtils.getJSONObjectArrayByString(value);
                                for (ObjectNode d : valueList) {
                                    collector.collect(d.toString());
                                }
                            } else {
                                // 新数据格式
                                collector.collect(data.toString());
                            }
                        }
                    }
                })
                .setParallelism(4)
                .flatMap(
                        new FlatMapFunction<String, RowData>() {
                            @Override
                            public void flatMap(String s, Collector<RowData> collector) throws Exception {
                                RowData record = l2OdsTableDeserializationSchema.deserialize(
                                        s.getBytes(StandardCharsets.UTF_8)
                                );
                                collector.collect(record);
                            }
                        }
                ).setParallelism(2)
                .map(
                        new RowDataSplitCleanMap(
                                CDCUtils.convertRowTypeToFieldGetterTupleList(l2OdsTableRowType)
                        )
                ).setParallelism(2);
        l2DataDS.print();
        // 入库二层ODS
        FlinkSink.forRowData(l2DataDS)
                .table(l2OdsTable)
                .tableLoader(l2OdsTableLoader)
                .distributionMode(DistributionMode.HASH)
                .writeParallelism(4)
                .append();

        // execute the sql statements
        flinkEnv.env().disableOperatorChaining();
        flinkEnv.env().execute("Application");
    }
}