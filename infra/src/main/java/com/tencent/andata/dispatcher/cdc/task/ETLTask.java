package com.tencent.andata.dispatcher.cdc.task;

import com.tencent.andata.dispatcher.cdc.operator.AIORowDataFieldSwapMap;
import com.tencent.andata.dispatcher.cdc.operator.spliter.AIODataPKSplit;
import com.tencent.andata.dispatcher.cdc.operator.spliter.DataPKSplit;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.dispatcher.cdc.operator.AIORowDataCleanMap;
import com.tencent.andata.utils.cdc.conf.builder.WriteTypeEnum;
import com.tencent.andata.utils.operator.map.RowDataFieldSwapMap;
import com.tencent.andata.utils.operator.process.SplitByDataKey;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import java.util.List;
import java.util.Map;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.data.RowData;

public class ETLTask {
    public final AIODataPKSplit splitter = new AIODataPKSplit();
    public final AIORowDataCleanMap cleaner = new AIORowDataCleanMap();
    public final AIORowDataFieldSwapMap swapper = new AIORowDataFieldSwapMap();

    public ETLTask(Map<MonitorTask, List<SinkTask>> subscribeRelation) {
        subscribeRelation.entrySet().forEach(this::addSubscribeRelation);
    }

    public void addSubscribeRelation(Map.Entry<MonitorTask, List<SinkTask>> relation) {
        relation.getValue().forEach(sinkTask -> {
            TableIdentifier src = sinkTask.subscribeTable;
            Schema srcSchema = relation.getKey().schemaMap.get(src), dstSchema = sinkTask.getSinkSchema();
            WriteTypeEnum writeTypeEnum = sinkTask.writeType;
            List<String> primaryKeys = sinkTask.primaryKeys;
            splitter.addGetter(src, DataPKSplit.fromFlinkSchema(srcSchema, primaryKeys));
            cleaner.addCleaner(src, srcSchema);
            // TODO: SwapChain这里可能会涉及到多版本。现在我们的设计是不启用多版本（一个数据，但分发到多个不同的DST，且DST的格式都不一样）
            // TODO：同一个数据，数据产生的结构以最后一个任务的配置为主
            swapper.addRowDataFieldSwapMap(
                    src,
                    new RowDataFieldSwapMap(srcSchema, dstSchema, writeTypeEnum)
            );
        });
    }

    public SingleOutputStreamOperator<RowData> transform(DataStream<MessageRowData> ds, int transformParallelism) {
        // 为了解决乱序问题，根据DB + Table ( + PK？）来进行分发
        // 做一次KeyBy可行不？不可行的，如果不能保证同一个pk的数据在全链路都是有序的，那么就必须引入sort了
        return ds
                .keyBy(splitter)
                .map(cleaner) // AIO，脏数据去除
                .setParallelism(transformParallelism)
                .forward()
                .map(swapper)
                .setParallelism(transformParallelism)
                .forward()
                .process(
                        // TODO: 这个Split应该由Sink生成，要不会被分割在两个位置
                        // TODO: 或者数据订阅那里由另外一个类来做这个事（Mediator）
                        new SplitByDataKey(
                                (SplitByDataKey.TagKeyGetter) rowData
                                        -> {
                                    MessageRowDataAttr attr = ((MessageRowData) rowData).getAttr();
                                    return attr.getSrcTable().getFullName();
                                }
                        )
                )
                .setParallelism(transformParallelism);

    }
}