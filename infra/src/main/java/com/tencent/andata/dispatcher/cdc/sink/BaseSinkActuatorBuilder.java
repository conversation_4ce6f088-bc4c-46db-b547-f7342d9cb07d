package com.tencent.andata.dispatcher.cdc.sink;

import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.cdc.conf.builder.WriteTypeEnum;
import com.tencent.andata.utils.sink.partition.PartitionSpec;

import java.util.List;
import java.util.Properties;
import org.apache.flink.table.types.logical.RowType;

public abstract class BaseSinkActuatorBuilder {
    protected TableIdentifier tableIdentifier; // 入库的表名 & 对应的存储介质
    protected List<String> primaryKeys; // 主键信息
    protected WriteTypeEnum writeType; // 入库类型，Upsert/Append
    protected PartitionSpec partitionSpec; // 分区信息

    protected Properties properties; // 其他配置，比如如果是入库DB所包含的usr/pwd等

    public BaseSinkActuatorBuilder setPrimaryKeys(List<String> primaryKeys) {
        this.primaryKeys = primaryKeys;
        return this;
    }

    public BaseSinkActuatorBuilder setTableIdentifier(TableIdentifier tableIdentifier) {
        this.tableIdentifier = tableIdentifier;
        return this;
    }

    public BaseSinkActuatorBuilder setProperties(Properties properties) {
        this.properties = properties;
        return this;
    }

    public BaseSinkActuatorBuilder setWriteType(WriteTypeEnum writeType) {
        this.writeType = writeType;
        return this;
    }

    public BaseSinkActuatorBuilder setPartitionSpec(PartitionSpec partitionSpec) {
        this.partitionSpec = partitionSpec;
        return this;
    }

    public abstract SinkActuator build() throws RuntimeException;
}