package demo;

import java.lang.reflect.Field;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.typeutils.TypeExtractor;
import org.apache.flink.runtime.plugable.SerializationDelegate;
import org.apache.flink.runtime.state.KeyGroupRangeAssignment;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.streaming.api.transformations.PartitionTransformation;
import org.apache.flink.streaming.runtime.partitioner.KeyGroupStreamPartitioner;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.Preconditions;
import org.junit.Test;

public class CustomPartitionerTest {
    static class CustomKeyGroupStreamPartitioner<T, K> extends KeyGroupStreamPartitioner<T, K> {
        private final KeySelector<T, K> keySelector;
        private int maxParallelism;

        public CustomKeyGroupStreamPartitioner(KeySelector<T, K> keySelector, int maxParallelism) {
            super(keySelector, maxParallelism);
            this.keySelector = (KeySelector) Preconditions.checkNotNull(keySelector);
            this.maxParallelism = maxParallelism;
        }

        @Override
        public int selectChannel(SerializationDelegate<StreamRecord<T>> record) {
            Object key;
            try {
                key = this.keySelector.getKey((T) ((StreamRecord) record.getInstance()).getValue());
            } catch (Exception var4) {
                throw new RuntimeException("Could not extract key from " + ((StreamRecord) record.getInstance()).getValue(), var4);
            }
            // 写死，验证能否指定SubTask
            return (int) key;
//            return KeyGroupRangeAssignment.assignKeyToParallelOperator(key, this.maxParallelism, this.numberOfChannels);
        }
    }

    @Test
    public void testKeyGroupStreamPartitionerToKeyedStream() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);
        String dataGenSource = "CREATE TABLE tTable (\n"
                + "    pk STRING,\n"
                + "    col1 INT,\n"
                + "    col2 INT\n"
                + ") WITH (\n"
                + "  'connector' = 'datagen',\n"
                + "  'rows-per-second' = '1'\n"
                + ")";
        tEnv.executeSql(dataGenSource);
        DataStream<Row> ds = tEnv
                .toDataStream(tEnv.sqlQuery("SELECT * FROM tTable"));
        KeySelector<Row, Integer> keySelector = new KeySelector<Row, Integer>() {
            @Override
            public Integer getKey(Row row) {
                return Integer.valueOf(0);
            }
        };
        KeyedStream<Row, Integer> keyedStream = ds.keyBy(keySelector);
        // 使用反射修改内部类
        Field transField = DataStream.class.getDeclaredField("transformation");
        transField.setAccessible(true);
        transField.set(
                keyedStream, new PartitionTransformation<>(
                        ds.getTransformation(),
                        new CustomKeyGroupStreamPartitioner<>(keySelector, 128))
        );
        keyedStream.process(new KeyedProcessFunction<Integer, Row, Object>() {
            @Override
            public void processElement(Row row, KeyedProcessFunction<Integer, Row, Object>.Context context,
                                       Collector<Object> collector) throws Exception {
                System.out.printf("%s-%s%n", context.getCurrentKey(), row);
            }
        }).setParallelism(2).print();
        env.execute();
    }
}